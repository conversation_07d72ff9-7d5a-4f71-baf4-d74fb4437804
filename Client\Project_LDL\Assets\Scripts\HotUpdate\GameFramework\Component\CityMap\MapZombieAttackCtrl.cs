using System.Collections.Generic;
using GameFramework.Event;
using Unity.Jobs;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    class ZombieData
    {
        /// <summary>
        /// 单个家建筑物最大僵尸数量
        /// </summary>
        private const int MaxZombieCount = 5;

        /// <summary>
        /// 僵尸刷新间隔
        /// </summary>
        private const float ZombieBornInterval = 3;
        
        private EL_BuildingTeam m_BuildingTeam;
        private float m_CurBornInterval;
        private List<int> m_ZombieList = new List<int>();

        public ZombieData(EL_BuildingTeam BuildingTeam)
        {
            m_BuildingTeam = BuildingTeam;
            m_ZombieList = new List<int>();
            m_CurBornInterval = 0;
        }
        
        public void Tick(float dt)
        {
            if (m_BuildingTeam.BuildingModule.BuildingState is BuildingState.UpgradeComplete
                or BuildingState.ConstructionComplete)
                return;
            if (m_BuildingTeam.BuildingModule.LEVEL <= 0)
                return;
            //刷僵尸
            if (m_ZombieList.Count < MaxZombieCount)
            {
                m_CurBornInterval += dt;
                var interval = ZombieBornInterval / m_BuildingTeam.GetHeroCnt();
                if (m_CurBornInterval >= interval)
                {
                    m_CurBornInterval -= interval;
                    var bornPos = m_BuildingTeam.transform.position + new Vector3(4, 0, -15) +
                                  BattleUtils.GetRandomPointInGroundCircle(5);

                    ED_BuildingTeamZombie data = new ED_BuildingTeamZombie(Game.GameEntry.Entity.GenerateSerialId(),
                        m_BuildingTeam.transform.position);
                    data.Position = bornPos;
                    int uid = GameEntry.Entity.ShowCommonDisplay(GetRandomZombiePath(), data,
                        typeof(EL_BuildingTeamZombie));
                    m_ZombieList.Add(uid);
                }
            }

            //击杀僵尸
            var list = m_BuildingTeam.GetCanAtkHero();
            for (int i = 0; i < list.Count; i++)
            {
                var hero = list[i];
                var zombie = GetCanAtkZombie();
                if (zombie != null)
                {
                    m_ZombieList.Remove(zombie.Id);
                    float flyTime = hero.PlayAtk(zombie.transform.position);
                    zombie.Die(flyTime);

                }
            }
        }

        private EL_BuildingTeamZombie GetCanAtkZombie()
        {
            for (int i = 0; i < m_ZombieList.Count; i++)
            {
                var entity = GameEntry.Entity.GetEntity(m_ZombieList[i]);
                if (entity != null && entity.Logic is EL_BuildingTeamZombie zombie)
                {
                    if (zombie.CanDie())
                    {
                        return zombie;
                    }
                }
            }
            return null;
        }
        
        private string GetRandomZombiePath()
        {
            if (Random.value > 0.5f)
                return "Assets/ResPackage/Battle/Prefab/battle_zombie_1.prefab";
            else
                return "Assets/ResPackage/Battle/Prefab/battle_zombie_2.prefab";
        }
    }
    
    public class MapZombieAttackCtrl
    {
        
        
        Dictionary<int,ZombieData> m_BuildingTeamZombieDataDic = new Dictionary<int, ZombieData>(); 
        // List<EL_BuildingTeam> m_BuildingTeamList = new List<EL_BuildingTeam>();
        
        public MapZombieAttackCtrl()
        {
            m_BuildingTeamZombieDataDic = new Dictionary<int, ZombieData>();
            Game.GameEntry.Event.Subscribe(ShowEntitySuccessEventArgs.EventId,Loaded);
            Game.GameEntry.Event.Subscribe(HideEntityCompleteEventArgs.EventId,OnUnLoaded);
            
        }

        private void OnUnLoaded(object sender, GameEventArgs e)
        {
            
        }

        private void Loaded(object sender, GameEventArgs e)
        {
            if (e is ShowEntitySuccessEventArgs args)
            {
                if (args.EntityLogicType == typeof(EL_BuildingTeam))
                {
                    var buildingTeam = args.Entity.Logic as EL_BuildingTeam;
                    if (buildingTeam != null && !m_BuildingTeamZombieDataDic.ContainsKey(buildingTeam.Id))
                    {
                        var data = new ZombieData(buildingTeam);
                        m_BuildingTeamZombieDataDic.Add(buildingTeam.Id,data);
                    }
                }
            }
        }

        public void Destroy()
        {
            Game.GameEntry.Event.Unsubscribe(ShowEntitySuccessEventArgs.EventId,Loaded);
            Game.GameEntry.Event.Unsubscribe(HideEntityCompleteEventArgs.EventId,OnUnLoaded);
        }

        private float m_TickInterval = 1f;
        private float m_CurTickInterval = 0f;
        public void Update()
        {
            m_CurTickInterval += Time.deltaTime;
            if (m_CurTickInterval < m_TickInterval)
                return;
            m_CurTickInterval -= m_TickInterval;
            foreach (var item in m_BuildingTeamZombieDataDic)
            {
                item.Value.Tick(m_TickInterval);
            }
        }
        
    }
}