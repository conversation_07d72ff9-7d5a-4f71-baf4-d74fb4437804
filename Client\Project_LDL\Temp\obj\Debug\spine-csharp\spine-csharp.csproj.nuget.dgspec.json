{"format": 1, "restore": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-csharp.csproj": {}}, "projects": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-csharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-csharp.csproj", "projectName": "spine-csharp", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-csharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\spine-csharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}}