using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBuildingWagonTragetForm : UGuiFormEx
    {
        private int minBoxId;
        private List<int> getList;
        private List<dungeon_reward> configList;
        private List<RewardInfo> rewardList = new();
        private int nextIndex;
        private int maxIndex;

        private class RewardInfo
        {
            public int Id;
            public int State; // 0:不可领取 1:领取 2:已领取
            public void SetData(int id, int state)
            {
                Id = id;
                State = state;
            }
        }
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            configList = GameEntry.LDLTable.GetTable<dungeon_reward>();

            var count = configList.Count;
            maxIndex = count > 0 ? configList[count - 1].id : 0;

            m_TableViewV.GetItemCount = () => rewardList.Count;
            m_TableViewV.GetItemGo = () => m_goItem;
            m_TableViewV.UpdateItemCell = OnUpdateItem;
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            var param = (ValueTuple<int, List<int>>)userData;
            minBoxId = param.Item1;
            getList = param.Item2;
            OnUpdateInfo();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);

            if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingWagonForm))
                GameEntry.UI.RefreshUIForm(EnumUIForm.UIBuildingWagonForm, 0);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnUpdateInfo(bool isReload = false)
        {
            var curId = GameEntry.LogicData.DungeonData.CurDungeonId;
            var nextIdx = -1;
            for (int i = 0; i < configList.Count; i++)
            {
                var data = configList[i];
                if (data.dungeon_id > curId && nextIdx == -1)
                {
                    nextIdx = data.id;
                }

                int state;
                if (data.id <= minBoxId) { state = 2; }
                else
                {
                    if (data.dungeon_id > curId) { state = 0; }
                    else if (getList.Contains(data.id)) { state = 2; }
                    else { state = 1; }
                }

                if (rewardList.Count >= i + 1)
                {
                    rewardList[i].SetData(data.id, state);
                }
                else
                {
                    rewardList.Add(new RewardInfo
                    {
                        Id = data.id,
                        State = state
                    });
                }
            }
            rewardList.Sort((a, b) =>
            {
                if (a.State == b.State) { return a.Id.CompareTo(b.Id); }
                else
                {
                    var aState = a.State == 1 ? -1 : a.State;
                    var bState = b.State == 1 ? -1 : b.State;
                    return aState.CompareTo(bState); }
            });
            nextIndex = nextIdx != -1 ? nextIdx : maxIndex;

            var hasReward = nextIndex - minBoxId - 1 > getList.Count;
            m_btnClaim.gameObject.SetActive(hasReward);

            if (isReload) { m_TableViewV.ReloadData(); }
            else { m_TableViewV.InitTableViewByIndex(0); }
        }

        private void OnUpdateItem(int index, GameObject go)
        {
            var rootTrans = go.transform.Find("bg");
            var bg = rootTrans.GetComponent<UIImage>();
            var titleTxt = rootTrans.Find("titleTxt").GetComponent<UIText>();
            var nonetxt = rootTrans.Find("m_txtAuto1100468").gameObject;
            var getBtn = rootTrans.Find("getBtn").GetComponent<UIButton>();
            var btnTxt = rootTrans.Find("getBtn/btnTxt").GetComponent<UIText>();
            var tableViewH = rootTrans.Find("tableViewH").GetComponent<Mosframe.TableViewH>();
            var rewardItem = rootTrans.Find("rewardItem").gameObject;

            var data = rewardList[index];
            var config = configList[data.Id - 1];
            titleTxt.text = ToolScriptExtend.GetLangFormat(1100467, config.dungeon_id + "");

            var state = data.State;
            btnTxt.text = state == 1 ? ToolScriptExtend.GetLang(1100273) : ToolScriptExtend.GetLang(1100262);
            bg.SetImage(state == 1 ? "Sprite/ui_public/win2_small_dikuang2_2green.png" : "Sprite/ui_public/win2_small_dikuang2_2.png");
            nonetxt.SetActive(state == 0);
            getBtn.gameObject.SetActive(state != 0);
            getBtn.isEnable = state != 2;
            ToolScriptExtend.SetGameObjectGrey(getBtn.transform, state == 2);

            getBtn.onClick.RemoveAllListeners();
            getBtn.onClick.AddListener(() =>
            {
                GameEntry.LogicData.DungeonData.DungeonClaimBox(data.Id, () =>
                {
                    if (!getList.Contains(data.Id)) getList.Add(data.Id);
                    OnUpdateInfo(true);
                });
            });

            var list = config.reward;
            tableViewH.GetItemCount = () => list.Count;
            tableViewH.GetItemGo = () => rewardItem;
            tableViewH.UpdateItemCell = (idx, item) =>
            {
                var itemObj = item.transform.Find("itemObj").gameObject;
                var itemModule = itemObj.GetComponent<UIItemModule>();
                var itemInfo = list[idx];
                itemModule.SetData(itemInfo.item_id, itemInfo.num);
                itemModule.DisplayInfo();
                itemModule.SetScale(0.55f);
            };
            if (tableViewH.itemPrototype) { tableViewH.ReloadData(); }
            else { tableViewH.InitTableViewByIndex(0); }
        }

        private void OnBtnCloseClick()
        {
            Close();
        }

        private void OnBtnClaimClick()
        {
            GameEntry.LogicData.DungeonData.DungeonClaimBox(0, () =>
            {
                getList.Clear();

                var endIndex = nextIndex != maxIndex ? nextIndex : nextIndex + 1;
                for (int i = minBoxId + 1; i < endIndex; i++)
                {
                    getList.Add(i);
                }
                OnUpdateInfo(true);
            });
        }
    }
}
