using System;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using Game.Hotfix.Config;
using Sirenix.OdinInspector;
using Team;
using UnityEngine;
using UnityEngine.Serialization;
using UnityGameFramework.Runtime;
using itemid = PbGameconfig.itemid;

namespace Game.Hotfix
{
    public class Battle5v5Component : BaseSceneComponent
    {
        [Title("进攻位置布阵")]
        [LabelText("位置1")][FormerlySerializedAs("pos1")] public Transform pos1;
        [LabelText("位置2")][FormerlySerializedAs("pos2")] public Transform pos2;
        [LabelText("位置3")][FormerlySerializedAs("pos3")] public Transform pos3;
        [LabelText("位置4")][FormerlySerializedAs("pos4")] public Transform pos4;
        [LabelText("位置5")][FormerlySerializedAs("pos5")] public Transform pos5;
        [Title("防守位置布阵")]
        [LabelText("位置1")][FormerlySerializedAs("pos11")] public Transform pos11;
        [LabelText("位置2")][FormerlySerializedAs("pos12")] public Transform pos12;
        [LabelText("位置3")][FormerlySerializedAs("pos13")] public Transform pos13;
        [LabelText("位置4")][FormerlySerializedAs("pos14")] public Transform pos14;
        [LabelText("位置5")][FormerlySerializedAs("pos15")] public Transform pos15;
        
        [Title("相机位置")]
        [LabelText("默认相机位置")][FormerlySerializedAs("")] public Transform cameraPosDefault;
        [LabelText("战斗相机位置")][FormerlySerializedAs("")] public Transform cameraPosBattle;
        [LabelText("战斗相机位置")][FormerlySerializedAs("")] public Transform cameraPosBattleBegin;

        [Title("进攻位置战斗中")]
        [LabelText("位置1")][FormerlySerializedAs("pos1Battle")] public Transform pos1Battle;
        [LabelText("位置2")][FormerlySerializedAs("pos2Battle")] public Transform pos2Battle;
        [LabelText("位置3")][FormerlySerializedAs("pos3Battle")] public Transform pos3Battle;
        [LabelText("位置4")][FormerlySerializedAs("pos4Battle")] public Transform pos4Battle;
        [LabelText("位置5")][FormerlySerializedAs("pos5Battle")] public Transform pos5Battle;
        [Title("防守位置战斗中")]
        [LabelText("位置1")][FormerlySerializedAs("pos11Battle")] public Transform pos11Battle;
        [LabelText("位置2")][FormerlySerializedAs("pos12Battle")] public Transform pos12Battle;
        [LabelText("位置3")][FormerlySerializedAs("pos13Battle")] public Transform pos13Battle;
        [LabelText("位置4")][FormerlySerializedAs("pos14Battle")] public Transform pos14Battle;
        [LabelText("位置5")][FormerlySerializedAs("pos15Battle")] public Transform pos15Battle;
        
        public bool IsDebug = false;
        public BattleFiled BattleFiled => m_BattleBattleFiled;
        
        private Dictionary<EnumBattlePos,ColoredBgPanel> m_ChoosePositions;
        private Dictionary<EnumBattlePos,Transform> m_BattlePositions;

        private BattleFiled m_BattleBattleFiled;
        private Battle5v5Data m_Battle5V5Data;

        protected override int GetSceneId()
        {
            return (int)SceneDefine.Battle5v5Scene;
        }

        protected override void OnStart()
        {
            base.OnStart();
            m_ChoosePositions = InitChoosePos();
            m_BattlePositions = InitBattlePos();

            IsDebug = TempConnectHelper.Instance.ConnectTargetPath == TempConnectHelper.ConnectTarget.技能编辑;

            m_Battle5V5Data = GameEntry.LogicData.Battle5v5Data;
            
            m_BattleBattleFiled = new BattleFiled();
            m_BattleBattleFiled.IsDebug = IsDebug;
            BattleFiled.Init(m_ChoosePositions, m_BattlePositions, this);


            //创建初始敌人
            if (m_Battle5V5Data.CurBattleType == EnumBattle5v5Type.Debug)
            {
                m_BattleBattleFiled.TeamCtrl.CreateHero(EnumBattlePos.PosR1,11501);
                m_BattleBattleFiled.TeamCtrl.CreateHero(EnumBattlePos.PosR2,11502);
                m_BattleBattleFiled.TeamCtrl.CreateHero(EnumBattlePos.PosR3,11504);
                m_BattleBattleFiled.TeamCtrl.CreateHero(EnumBattlePos.PosR4,12503);
                m_BattleBattleFiled.TeamCtrl.CreateHero(EnumBattlePos.PosR5,13401);
            }
            else if (m_Battle5V5Data.CurBattleType == EnumBattle5v5Type.Dungeon)
            {
                var param = m_Battle5V5Data.GetBattle5V5Param<Battle5v5ParamDungeon>();
                if (param != null)
                {
                    foreach (var hero in param.Heros)
                    {
                        m_BattleBattleFiled.TeamCtrl.CreateHero((EnumBattlePos)hero.Pos, (int)hero.Code, hero);
                    }
                }
                else
                {
                    Debug.LogError("Battle5v5ParamDungeon not found");
                }
            }else if (m_Battle5V5Data.CurBattleType == EnumBattle5v5Type.TradeTruck)
            {
                var param = m_Battle5V5Data.GetBattle5V5Param<Battle5v5ParamTradeTruck>();
                if (param != null)
                {
                    Debug.Log($"货车战斗参数获取成功，防守英雄数量: {param.DefHeros.Count}");
                    foreach (var hero in param.DefHeros)
                    {
                        Debug.Log($"创建防守英雄: Code={hero.Code}, Pos={hero.Pos}, Level={hero.Level}");
                        m_BattleBattleFiled.TeamCtrl.CreateHero((EnumBattlePos)hero.Pos, (int)hero.Code, hero);
                    }
                }
                else
                {
                    Debug.LogError("Battle5v5ParamTradeTruck not found");
                }
            }

            MoveCameraToDefault(0);
        }

        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);
            BattleFiled?.OnUpdate(Time.deltaTime);
        }

        protected override void OnDestroy()
        {
            BattleFiled.UnInit();
            GameEntry.Battle5v5 = null;
            base.OnDestroy();
        }

        private Dictionary<EnumBattlePos, Transform> InitBattlePos()
        {
            var bp = new Dictionary<EnumBattlePos, Transform>();
            bp.Add(EnumBattlePos.PosL1, pos1Battle);
            bp.Add(EnumBattlePos.PosL2, pos2Battle);
            bp.Add(EnumBattlePos.PosL3, pos3Battle);
            bp.Add(EnumBattlePos.PosL4, pos4Battle);
            bp.Add(EnumBattlePos.PosL5, pos5Battle);

            bp.Add(EnumBattlePos.PosR1, pos11Battle);
            bp.Add(EnumBattlePos.PosR2, pos12Battle);
            bp.Add(EnumBattlePos.PosR3, pos13Battle);
            bp.Add(EnumBattlePos.PosR4, pos14Battle);
            bp.Add(EnumBattlePos.PosR5, pos15Battle);
            return bp;
        }

        private Dictionary<EnumBattlePos, ColoredBgPanel> InitChoosePos()
        {
            var bp = new Dictionary<EnumBattlePos, ColoredBgPanel>();
            bp.Add(EnumBattlePos.PosL1,pos1.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL2,pos2.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL3,pos3.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL4,pos4.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosL5,pos5.gameObject.GetOrAddComponent<ColoredBgPanel>());
            
            bp.Add(EnumBattlePos.PosR1,pos11.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR2,pos12.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR3,pos13.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR4,pos14.gameObject.GetOrAddComponent<ColoredBgPanel>());
            bp.Add(EnumBattlePos.PosR5,pos15.gameObject.GetOrAddComponent<ColoredBgPanel>());
            return bp;
        }

        public EnumBattlePos? IsInPosBounds(Vector3 worldPos, EnumBattleSide side)
        {
            foreach (var item in m_ChoosePositions)
            {
                if (side == EnumBattleSide.Left && (int)item.Key > BattleDefine.AttackTeamMaxPos)
                    continue;
                if (side == EnumBattleSide.Right && (int)item.Key <= BattleDefine.AttackTeamMaxPos)
                    continue;

                if (item.Value.Contains(worldPos))
                    return item.Key;
            }

            return null; 
        }

        public void MoveCameraToBattle(float duration)
        {
            MoveCameraTo(cameraPosBattleBegin, 0);
            MoveCameraTo(cameraPosBattle,duration);
        }
        
        public void MoveCameraToDefault(float duration)
        {
            MoveCameraTo(cameraPosDefault,duration);
        }

        private void MoveCameraTo(Transform targetTrans,float duration)
        {
            var curCameraTrans = GameEntry.Camera.CurUseCamera.transform;
            if (duration > 0)
            {
                curCameraTrans.DOMove(targetTrans.position,duration);
                // curCameraTrans.DOLookAt(targetTrans.forward, duration);
            }
            else
            {
                curCameraTrans.position = targetTrans.position;
                // curCameraTrans.rotation = targetTrans.rotation;
            }
        }
    }
    
}