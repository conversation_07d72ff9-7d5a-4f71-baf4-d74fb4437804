{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.2D.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Unity.Mathematics >= 1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Runtime >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Runtime.Tests.csproj", "projectName": "Unity.RenderPipelines.Universal.Runtime.Tests", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Runtime.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\Unity.RenderPipelines.Universal.Runtime.Tests\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Mathematics.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}