%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &485261542299417462
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8579780950486011245}
  m_Layer: 9
  m_Name: Bone025(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8579780950486011245
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 485261542299417462}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000029984262, y: -0.0000002039376, z: 0.014701076, w: 0.99989194}
  m_LocalPosition: {x: -0.7050207, y: 0.47840896, z: -1.0488033}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &842792279267910481
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 218879132547099229}
  m_Layer: 9
  m_Name: Bone016
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &218879132547099229
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 842792279267910481}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07677077, y: -0.7029267, z: 0.7035281, w: -0.07105393}
  m_LocalPosition: {x: -0.4356134, y: 0.8078592, z: 1.2069918}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1273305815778860913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6241165291430289900}
  m_Layer: 9
  m_Name: Bone029(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6241165291430289900
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1273305815778860913}
  serializedVersion: 2
  m_LocalRotation: {x: 2.325557e-21, y: -0.00000016364652, z: 1.4210855e-14, w: 1}
  m_LocalPosition: {x: -0.8596438, y: 0.12119445, z: -0.01297101}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1291367649547333100
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1083691102988083033}
  m_Layer: 9
  m_Name: slot_fire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1083691102988083033
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1291367649547333100}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000020861626, y: 0.00000017881393, z: 0.00000035762787, w: 1}
  m_LocalPosition: {x: 0, y: -0, z: 0.553}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 2583407565245289813}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1520126445057793440
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6822162412341938935}
  m_Layer: 9
  m_Name: Bone026(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6822162412341938935
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1520126445057793440}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0.00000016292071, z: -0, w: 1}
  m_LocalPosition: {x: -0.85964376, y: 0.83312285, z: -0.0000007629394}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1205170968475614734}
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1681031324095099713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2893607058860004718}
  m_Layer: 9
  m_Name: Bone024(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2893607058860004718
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1681031324095099713}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000022317053, y: -0.00000015178917, z: 0.014701076, w: 0.99989194}
  m_LocalPosition: {x: -0.70502096, y: 0.4784091, z: -0.42476454}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1938963189372812283
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9133018654424228901}
  m_Layer: 9
  m_Name: Bone004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9133018654424228901
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1938963189372812283}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0.000000020489097, z: -0, w: 1}
  m_LocalPosition: {x: -0.5940056, y: 0.000000114440915, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7354350454028758380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1963944032614716964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8940479104393071023}
  m_Layer: 9
  m_Name: Bone011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8940479104393071023
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1963944032614716964}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7046926, y: -0.11478941, z: 0.049682666, w: 0.69840056}
  m_LocalPosition: {x: -0.75962335, y: 1.0045935, z: -0.83138883}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4854046942829992847}
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1982731990429301479
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7928129288424357268}
  - component: {fileID: 754812987499639171}
  m_Layer: 9
  m_Name: 18_tank_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7928129288424357268
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1982731990429301479}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0.0000000055171805}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6045772736739310361}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &754812987499639171
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1982731990429301479}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2aea8c0be8e0fac419dcfe5b6e29cba8, type: 2}
  - {fileID: 2100000, guid: cc0f04bf59c29f4468852cc13ea71684, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -4315318346307919801, guid: ced0b016d6771e14da7267223252274f, type: 3}
  m_Bones:
  - {fileID: 7042413208728956559}
  - {fileID: 7354350454028758380}
  - {fileID: 4854046942829992847}
  - {fileID: 8633395260690343695}
  - {fileID: 8252895346158383544}
  - {fileID: 4813037800157797325}
  - {fileID: 218879132547099229}
  - {fileID: 5014666002659205090}
  - {fileID: 8789026291601132664}
  - {fileID: 8940479104393071023}
  - {fileID: 4091092390795131610}
  - {fileID: 6400460790502752607}
  - {fileID: 5575617593302728195}
  - {fileID: 6290746321601289080}
  - {fileID: 5345635961127215851}
  - {fileID: 2893607058860004718}
  - {fileID: 6111015622547997957}
  - {fileID: 8579780950486011245}
  - {fileID: 8162437962989020973}
  - {fileID: 1264294571049241247}
  - {fileID: 4311197480171444061}
  - {fileID: 6982138877001032071}
  - {fileID: 9135086003005994410}
  - {fileID: 6822162412341938935}
  - {fileID: 6241165291430289900}
  - {fileID: 8380911651675855492}
  - {fileID: 2434788047667815232}
  - {fileID: 7490275396427087305}
  - {fileID: 8560604536995797729}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 7042413208728956559}
  m_AABB:
    m_Center: {x: -0.9545822, y: 0.0023845434, z: 0.016603589}
    m_Extent: {x: 1.6683667, y: 1.524769, z: 1.3683499}
  m_DirtyAABB: 0
--- !u!1 &2191928397288775423
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7354350454028758380}
  m_Layer: 9
  m_Name: Bone003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7354350454028758380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2191928397288775423}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000012644057, y: 0.7071068, z: 0.00000012644057, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0.0000000025761744, z: 0.043596726}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9133018654424228901}
  m_Father: {fileID: 2583407565245289813}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2376652027193122913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5014666002659205090}
  m_Layer: 9
  m_Name: Bone014
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5014666002659205090
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2376652027193122913}
  serializedVersion: 2
  m_LocalRotation: {x: -0.03760018, y: -0.7023356, z: 0.71004206, w: -0.03393001}
  m_LocalPosition: {x: -0.4414955, y: 0.08431633, z: 1.206992}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6048654105482207887}
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2556755464440157574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4554915382622146753}
  m_Layer: 9
  m_Name: slot_hurt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4554915382622146753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2556755464440157574}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.673, z: 0.326}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children: []
  m_Father: {fileID: 6045772736739310361}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3082624010396443066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3672968832561380299}
  m_Layer: 9
  m_Name: Bone006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3672968832561380299
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3082624010396443066}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.39024407, y: 0.00000015258789, z: -0.000000046516977}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5575617593302728195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3162372052445887450
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5784215851130249355}
  m_Layer: 9
  m_Name: Bone027
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5784215851130249355
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3162372052445887450}
  serializedVersion: 2
  m_LocalRotation: {x: 6.123234e-17, y: 6.123234e-17, z: -1.8369701e-16, w: 1}
  m_LocalPosition: {x: -0.24403282, y: 0, z: 0.000000021334017}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6111015622547997957}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3267469328541331251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4091092390795131610}
  m_Layer: 9
  m_Name: Bone009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4091092390795131610
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3267469328541331251}
  serializedVersion: 2
  m_LocalRotation: {x: -1.7296353e-23, y: 8.6595606e-17, z: -0.70710665, w: 0.70710695}
  m_LocalPosition: {x: 0.98315144, y: 0.17663482, z: -0.00000005732232}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1924756020738920833}
  m_Father: {fileID: 530527854528701984}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3364584550026811717
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8380911651675855492}
  m_Layer: 9
  m_Name: Bone028(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8380911651675855492
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3364584550026811717}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0.00000015631905, z: -0, w: 1}
  m_LocalPosition: {x: -0.85964423, y: 0.54644126, z: 1.2631538}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3836554555922680001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8560604536995797729}
  m_Layer: 9
  m_Name: Bone028
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8560604536995797729
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3836554555922680001}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016292068, y: -1.2246469e-16, z: 1, w: 6.123236e-17}
  m_LocalPosition: {x: 0.84886724, y: 0.5464412, z: 1.2631541}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3897350788227852893
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6045772736739310361}
  - component: {fileID: 3015208556656070992}
  m_Layer: 9
  m_Name: 18_tank_Battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6045772736739310361
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3897350788227852893}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 7928129288424357268}
  - {fileID: 2760211324042610864}
  - {fileID: 4554915382622146753}
  m_Father: {fileID: 1565915903285372568}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &3015208556656070992
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3897350788227852893}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 8b2ec22929d8e1241b8e08d16fdd1a59, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &4058731856739109106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2434788047667815232}
  m_Layer: 9
  m_Name: Bone030
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2434788047667815232
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4058731856739109106}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016292068, y: -1.2246469e-16, z: 1, w: 6.123236e-17}
  m_LocalPosition: {x: 0.8488664, y: 0.53615934, z: -1.4377944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4789390679659193665
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1205170968475614734}
  m_Layer: 9
  m_Name: Bone027(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1205170968475614734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4789390679659193665}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: -0.00000004371139, z: 2.6054018e-15, w: 1}
  m_LocalPosition: {x: -0.24403296, y: 0.000000021765013, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6822162412341938935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4813794268900001789
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5404941645032977548}
  m_Layer: 9
  m_Name: Bone008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5404941645032977548
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4813794268900001789}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.39024407, y: 0.00000015258789, z: -0.000000046516977}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6400460790502752607}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5004930450291861481
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6111015622547997957}
  m_Layer: 9
  m_Name: Bone026
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6111015622547997957
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5004930450291861481}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016292068, y: -1.2246469e-16, z: 1, w: 6.123236e-17}
  m_LocalPosition: {x: 0.84886676, y: 0.83312285, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5784215851130249355}
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5153503333596324875
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1264294571049241247}
  m_Layer: 9
  m_Name: Bone021
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1264294571049241247
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5153503333596324875}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000015923895, y: -0.0000000023412543, z: 0.99989194, w: 0.014701185}
  m_LocalPosition: {x: 0.6815061, y: 0.50830245, z: 0.26768595}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5655704812013829741}
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5197013504561973405
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 530527854528701984}
  m_Layer: 9
  m_Name: Dummy003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &530527854528701984
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5197013504561973405}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000337172, y: 0.0000000013705163, z: 0.70422673, w: 0.7099751}
  m_LocalPosition: {x: -0.5674309, y: -0.012074556, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5575617593302728195}
  - {fileID: 6400460790502752607}
  - {fileID: 4091092390795131610}
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5243253432531718485
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6982138877001032071}
  m_Layer: 9
  m_Name: Bone025
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6982138877001032071
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5243253432531718485}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000015923895, y: -0.0000000023412543, z: 0.99989194, w: 0.014701185}
  m_LocalPosition: {x: 0.6818262, y: 0.51919866, z: -1.0488027}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5319899840610329102
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6290746321601289080}
  m_Layer: 9
  m_Name: Bone023(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6290746321601289080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5319899840610329102}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000029984262, y: -0.0000002039376, z: 0.014701076, w: 0.99989194}
  m_LocalPosition: {x: -0.70408905, y: 0.44670805, z: 0.8845491}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5334471514233623982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8789026291601132664}
  m_Layer: 9
  m_Name: Bone017
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8789026291601132664
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5334471514233623982}
  serializedVersion: 2
  m_LocalRotation: {x: -0.021804927, y: 0.70677024, z: -0.70657015, w: -0.027549671}
  m_LocalPosition: {x: -0.3454424, y: -0.5797597, z: 1.2069926}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5357205862765997167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7042413208728956559}
  m_Layer: 9
  m_Name: Bone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7042413208728956559
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5357205862765997167}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49796337, y: 0.50202817, z: -0.49796373, w: 0.5020282}
  m_LocalPosition: {x: -0.0000000050023576, y: 0.70346767, z: -0.11444069}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3634142193433787206}
  - {fileID: 8940479104393071023}
  - {fileID: 5014666002659205090}
  - {fileID: 218879132547099229}
  - {fileID: 8789026291601132664}
  - {fileID: 4813037800157797325}
  - {fileID: 8252895346158383544}
  - {fileID: 8633395260690343695}
  - {fileID: 2583407565245289813}
  - {fileID: 530527854528701984}
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5474792198356103660
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8252895346158383544}
  m_Layer: 9
  m_Name: Bone019
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8252895346158383544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5474792198356103660}
  serializedVersion: 2
  m_LocalRotation: {x: -0.002874301, y: -0.7071007, z: 0.7071013, w: 0.0028739662}
  m_LocalPosition: {x: -0.44149488, y: 0.08431465, z: -1.1970985}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5578300745444518639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3634142193433787206}
  m_Layer: 9
  m_Name: Bone002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3634142193433787206
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5578300745444518639}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3406436, y: -0.000000114440915, z: -0.00000015721349}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6725126108179138407
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1565915903285372568}
  - component: {fileID: 6146148593266472549}
  - component: {fileID: 4116125576116849338}
  m_Layer: 9
  m_Name: role_11505_battle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1565915903285372568
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6725126108179138407}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6045772736739310361}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6146148593266472549
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6725126108179138407}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1770034b84515b45a96b3f473aae6c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ObjTransform: {fileID: 2583407565245289813}
--- !u!114 &4116125576116849338
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6725126108179138407}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d1eb6e16bbb69d1478b3a81466b2d544, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slots:
  - Slot: 0
    Transform: {fileID: 1083691102988083033}
  - Slot: 9
    Transform: {fileID: 4554915382622146753}
--- !u!1 &6801488243382810366
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5655704812013829741}
  m_Layer: 9
  m_Name: Bone022
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5655704812013829741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6801488243382810366}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -1.2246469e-16, w: 1}
  m_LocalPosition: {x: -0.37073234, y: 0, z: 0.000000031920045}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1264294571049241247}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7041424351046549728
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7490275396427087305}
  m_Layer: 9
  m_Name: Bone029
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7490275396427087305
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7041424351046549728}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016292068, y: -1.2246469e-16, z: 1, w: 6.123236e-17}
  m_LocalPosition: {x: 0.8488669, y: 0.12119445, z: -0.012970448}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7121316527052639086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4854046942829992847}
  m_Layer: 9
  m_Name: Bone012
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4854046942829992847
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7121316527052639086}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00028528887, y: 0.015070994, z: -0.018924123, w: 0.99970734}
  m_LocalPosition: {x: -0.5657347, y: 0.000000114440915, z: 0.000000024059117}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4775931159243686001}
  m_Father: {fileID: 8940479104393071023}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7263158976620768521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4813037800157797325}
  m_Layer: 9
  m_Name: Bone018
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4813037800157797325
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7263158976620768521}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02138307, y: -0.7067831, z: 0.70693415, w: -0.015636757}
  m_LocalPosition: {x: -0.34544134, y: -0.57976127, z: -1.197098}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7439484792418018670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6048654105482207887}
  m_Layer: 9
  m_Name: Bone015
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6048654105482207887
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7439484792418018670}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.4795639, y: 0.000000076293944, z: -0.000000020963016}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5014666002659205090}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7562760650522099658
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9135086003005994410}
  m_Layer: 9
  m_Name: Bone030(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9135086003005994410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7562760650522099658}
  serializedVersion: 2
  m_LocalRotation: {x: 4.263256e-14, y: -0.5337848, z: 0.00000007107772, w: 0.8456204}
  m_LocalPosition: {x: -0.8596434, y: 0.53615934, z: -1.4377954}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: -64.523, z: 0}
--- !u!1 &7611842738849570370
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8162437962989020973}
  m_Layer: 9
  m_Name: Bone023
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8162437962989020973
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7611842738849570370}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000015923895, y: -0.0000000023412543, z: 0.99989194, w: 0.014701185}
  m_LocalPosition: {x: 0.68089443, y: 0.48749778, z: 0.8845497}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7730745514945415978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2583407565245289813}
  m_Layer: 9
  m_Name: Dummy002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2583407565245289813
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7730745514945415978}
  serializedVersion: 2
  m_LocalRotation: {x: 0.49796256, y: -0.5020292, z: 0.49796256, w: 0.5020292}
  m_LocalPosition: {x: -1.9837627, y: -0.00567194, z: -0.00000061035155}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7354350454028758380}
  - {fileID: 1083691102988083033}
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 89.534, y: -90, z: 0}
--- !u!1 &7737863769164063036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2760211324042610864}
  m_Layer: 9
  m_Name: Dummy001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2760211324042610864
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7737863769164063036}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7042413208728956559}
  - {fileID: 1264294571049241247}
  - {fileID: 5345635961127215851}
  - {fileID: 8162437962989020973}
  - {fileID: 6290746321601289080}
  - {fileID: 4311197480171444061}
  - {fileID: 2893607058860004718}
  - {fileID: 6982138877001032071}
  - {fileID: 8579780950486011245}
  - {fileID: 6111015622547997957}
  - {fileID: 6822162412341938935}
  - {fileID: 8560604536995797729}
  - {fileID: 8380911651675855492}
  - {fileID: 7490275396427087305}
  - {fileID: 6241165291430289900}
  - {fileID: 2434788047667815232}
  - {fileID: 9135086003005994410}
  m_Father: {fileID: 6045772736739310361}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7820601303152701855
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5575617593302728195}
  m_Layer: 9
  m_Name: Bone005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5575617593302728195
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7820601303152701855}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.006241043, w: 0.99998057}
  m_LocalPosition: {x: -0.18804413, y: 0.42914, z: 0.81870764}
  m_LocalScale: {x: 1.0000004, y: 1.0000005, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3672968832561380299}
  m_Father: {fileID: 530527854528701984}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7903272977260645294
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8633395260690343695}
  m_Layer: 9
  m_Name: Bone020
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8633395260690343695
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7903272977260645294}
  serializedVersion: 2
  m_LocalRotation: {x: -0.046457592, y: 0.70557874, z: -0.7051783, w: -0.05219182}
  m_LocalPosition: {x: -0.43561286, y: 0.80785763, z: -1.197099}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7042413208728956559}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8126550972590549086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6400460790502752607}
  m_Layer: 9
  m_Name: Bone007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6400460790502752607
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8126550972590549086}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.006241043, w: 0.99998057}
  m_LocalPosition: {x: -0.19775344, y: 0.42921904, z: -0.8724968}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5404941645032977548}
  m_Father: {fileID: 530527854528701984}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8196887963174985452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5345635961127215851}
  m_Layer: 9
  m_Name: Bone021(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5345635961127215851
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8196887963174985452}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000022317053, y: -0.00000015178917, z: 0.014701076, w: 0.99989194}
  m_LocalPosition: {x: -0.7047007, y: 0.4675127, z: 0.2676855}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8503873575209247538
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1924756020738920833}
  m_Layer: 9
  m_Name: Bone010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1924756020738920833
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8503873575209247538}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -1.2246469e-16, w: 1}
  m_LocalPosition: {x: -0.46866485, y: -0.000000076293944, z: -7.867129e-13}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4091092390795131610}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8555570386956475707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4311197480171444061}
  m_Layer: 9
  m_Name: Bone024
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4311197480171444061
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8555570386956475707}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000015923895, y: -0.0000000023412543, z: 0.99989194, w: 0.014701185}
  m_LocalPosition: {x: 0.6818264, y: 0.51919883, z: -0.4247641}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2760211324042610864}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8908184010337702409
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4775931159243686001}
  m_Layer: 9
  m_Name: Bone013
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4775931159243686001
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8908184010337702409}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.0944047, y: 0.000000076293944, z: 0.00000008937479}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4854046942829992847}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
