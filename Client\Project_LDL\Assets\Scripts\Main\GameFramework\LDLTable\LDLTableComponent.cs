using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Threading.Tasks;
using Game.Hotfix.Config;
using Game.Hotfix.Proto;
using GameFramework.Resource;
using UnityEngine;
using UnityGameFramework.Runtime;
using Table = Game.Hotfix.Config.Table;
using UIForm = Game.Hotfix.Config.uiform_config;

namespace Game
{
    using TableProto = Hotfix.Proto.Table;
    public sealed class LDLTableComponent : GameFrameworkComponent
    {
        private Dictionary<string, bool> m_LoadedFlag = new Dictionary<string, bool>();

        private Dictionary<string, TaskCompletionSource<TextAsset>> m_tableTcs =
            new Dictionary<string, TaskCompletionSource<TextAsset>>();

        public Table ldl_table;
        public TableProto ldl_proto_table;
        protected override void Awake()
        {
            base.Awake();
            ldl_table = new Table();
            ldl_proto_table = new TableProto();
        }

        public bool HaseTable<T>() where T : tabtoy.ITableSerializable
        {
            var type = typeof(T);
            string fullPropertyName = type.Name;
            // 使用反射获取属性
            var filedInfo = typeof(Table).GetField(fullPropertyName, BindingFlags.Public | BindingFlags.Instance);
            if (filedInfo != null)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取配置方法，在没有更好的方法之前 先用反射
        /// </summary>
        /// <param name="id"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public T GetTableById<T>(int id, string key = "id") where T : tabtoy.ITableSerializable
        {
            var type = typeof(T);
            // 拼接属性名称
            string fullPropertyName = type.Name + "By" + key;
            // 使用反射获取属性
            var filedInfo = typeof(Table).GetField(fullPropertyName, BindingFlags.Public | BindingFlags.Instance);
            if (filedInfo != null)
            {
                object propertyValue = filedInfo.GetValue(ldl_table);
                // 检查属性值是否为 Dictionary<Int32, T>, T 是你的目标类型
                if (propertyValue is System.Collections.IDictionary dictionary)
                {
                    // 使用泛型方法来处理字典
                    return ProcessDictionary<T>(dictionary, id);
                }
                else
                {
                    Debug.LogError($"Property '{fullPropertyName}' is not a Dictionary.");
                }
            }
            else
            {
                Debug.LogError($"Property '{fullPropertyName}' not found in class A.");
            }

            return default;
        }

        /// <summary>
        /// 获取配置方法，在没有更好的方法之前 先用反射
        /// </summary>
        /// <param name="id"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public T GetTableById<T>(itemid id, string key = "id") where T : tabtoy.ITableSerializable
        {
            var type = typeof(T);
            // 拼接属性名称
            string fullPropertyName = type.Name + "By" + key;
            // 使用反射获取属性
            var filedInfo = typeof(Table).GetField(fullPropertyName, BindingFlags.Public | BindingFlags.Instance);
            if (filedInfo != null)
            {
                object propertyValue = filedInfo.GetValue(ldl_table);
                // 检查属性值是否为 Dictionary<Int32, T>, T 是你的目标类型
                if (propertyValue is System.Collections.IDictionary dictionary)
                {
                    // 使用泛型方法来处理字典
                    return ProcessDictionary<T>(dictionary, (int)id);
                }
                else
                {
                    Debug.LogError($"Property '{fullPropertyName}' is not a Dictionary.");
                }
            }
            else
            {
                Debug.LogError($"Property '{fullPropertyName}' not found in class A.");
            }

            return default;
        }

        public T GetKeyValueTable<T>() where T : tabtoy.ITableSerializable
        {
            var list = GetTable<T>();
            if (list.Count > 0)
                return list[0];
            return default;
        }
        
        public List<T> GetTable<T>() where T : tabtoy.ITableSerializable
        {
            var type = typeof(T);
            string fullPropertyName = type.Name;
            var filedInfo = typeof(Table).GetField(fullPropertyName, BindingFlags.Public | BindingFlags.Instance);
            if (filedInfo != null)
            {
                object propertyValue = filedInfo.GetValue(ldl_table);
                if (propertyValue is List<T> list)
                {
                    return list;
                }
                else
                {
                    Debug.LogError($"Property '{fullPropertyName}' is not a List.");
                }
            }
            else
            {
                Debug.LogError($"Property '{fullPropertyName}' not found in config.cs");
            }

            return default;
        }

        T ProcessDictionary<T>(System.Collections.IDictionary dictionary, int id) where T : tabtoy.ITableSerializable
        {
            Type dictionaryType = dictionary.GetType();
            // 验证是否是 Dictionary<Int32, targetType>， 避免不安全的类型转换
            if (dictionaryType.IsGenericType
            && dictionaryType.GetGenericTypeDefinition() == typeof(Dictionary<,>)
            && (dictionaryType.GetGenericArguments()[0] == typeof(Int32) || dictionaryType.GetGenericArguments()[0] == typeof(itemid) || dictionaryType.GetGenericArguments()[0] == typeof(paymentid)
                || dictionaryType.GetGenericArguments()[0] == typeof(attributessource))
            && dictionaryType.GetGenericArguments()[1] == typeof(T))
            {
                if (dictionary.Contains((itemid)id))
                {
                    return (T)dictionary[(itemid)id];
                }
                else if (dictionary.Contains((paymentid)id))
                {
                    return (T)dictionary[(paymentid)id];
                }
                else if (dictionary.Contains((attributessource)id))
                {
                    return (T)dictionary[(attributessource)id];
                }

                else if (dictionary.Contains(id))
                {
                    return (T)dictionary[id];
                }
                else
                {
                    Debug.LogError($"配置表不存在ID ! Dictionary does not contain key {id}" + "   configname: " + typeof(T).Name);
                }

            }
            else
            {
                Debug.LogError($"Dictionary is not of type Dictionary<Int32, {typeof(T).Name}>");
            }

            return default;
        }

        public async void PreLoadTable(string flag, object userData)
        {
            m_LoadedFlag.Clear();
            m_tableTcs.Clear();

            await LoadProtoTableByBytes(ldl_proto_table, nameof(messageDefine));

            await LoadTableByBytes(ldl_table, nameof(build_soldier));
            await LoadTableByBytes(ldl_table, nameof(scene_config));
            await LoadTableByBytes(ldl_table, nameof(sound_config));
            await LoadTableByBytes(ldl_table, nameof(uigroup_config));
            await LoadTableByBytes(ldl_table, nameof(uiform_config));
            await LoadTableByBytes(ldl_table, nameof(language_config));
            await LoadTableByBytes(ldl_table, nameof(language_type));
            await LoadTableByBytes(ldl_table, nameof(build_config));
            await LoadTableByBytes(ldl_table, nameof(build_menubutton));
            await LoadTableByBytes(ldl_table, nameof(hero_config));
            await LoadTableByBytes(ldl_table, nameof(language_describe));
            await LoadTableByBytes(ldl_table, nameof(item_config));
            await LoadTableByBytes(ldl_table, nameof(innercity_initialbuild));
            await LoadTableByBytes(ldl_table, nameof(innercity_map));
            await LoadTableByBytes(ldl_table, nameof(build_level));
            await LoadTableByBytes(ldl_table, nameof(innercity_buildpre));
            await LoadTableByBytes(ldl_table, nameof(hero_level));
            await LoadTableByBytes(ldl_table, nameof(hero_star));
            await LoadTableByBytes(ldl_table, nameof(hero_promotion));
            await LoadTableByBytes(ldl_table, nameof(equipment_config));
            await LoadTableByBytes(ldl_table, nameof(equipment_level));
            await LoadTableByBytes(ldl_table, nameof(equipment_materials));
            await LoadTableByBytes(ldl_table, nameof(equipment_promotion));
            await LoadTableByBytes(ldl_table, nameof(innercity_wall));
            await LoadTableByBytes(ldl_table, nameof(skill_config));
            await LoadTableByBytes(ldl_table, nameof(skill_effect));
            await LoadTableByBytes(ldl_table, nameof(hero_skill_level));
            await LoadTableByBytes(ldl_table, nameof(attributes_config));
            await LoadTableByBytes(ldl_table, nameof(hero_promotion_unlock));
            await LoadTableByBytes(ldl_table, nameof(demand_config));
            await LoadTableByBytes(ldl_table, nameof(item_randomreward));
            await LoadTableByBytes(ldl_table, nameof(recruit_config));
            await LoadTableByBytes(ldl_table, nameof(global_setting));
            await LoadTableByBytes(ldl_table, nameof(innercity_path));
            await LoadTableByBytes(ldl_table, nameof(recruit_drops));
            await LoadTableByBytes(ldl_table, nameof(item_choosereward));
            await LoadTableByBytes(ldl_table, nameof(store_type));
            await LoadTableByBytes(ldl_table, nameof(store_config));
            await LoadTableByBytes(ldl_table, nameof(item_count));
            await LoadTableByBytes(ldl_table, nameof(battle_role));
            await LoadTableByBytes(ldl_table, nameof(battle_effect));
            await LoadTableByBytes(ldl_table, nameof(union_button));
            await LoadTableByBytes(ldl_table, nameof(union_const_setting));
            await LoadTableByBytes(ldl_table, nameof(union_flag));
            await LoadTableByBytes(ldl_table, nameof(union_permission));
            await LoadTableByBytes(ldl_table, nameof(union_permission_desc));
            await LoadTableByBytes(ldl_table, nameof(union_position));
            await LoadTableByBytes(ldl_table, nameof(gift_pack));
            await LoadTableByBytes(ldl_table, nameof(get_way));
            await LoadTableByBytes(ldl_table, nameof(payment));
            await LoadTableByBytes(ldl_table, nameof(shopping_dailydeal));
            await LoadTableByBytes(ldl_table, nameof(skill_show));
            await LoadTableByBytes(ldl_table, nameof(task_main));
            await LoadTableByBytes(ldl_table, nameof(task_daily));
            await LoadTableByBytes(ldl_table, nameof(shopping_center));
            await LoadTableByBytes(ldl_table, nameof(shopping_monthycard_weeklycard));
            await LoadTableByBytes(ldl_table, nameof(innercity_areaunlock));
            await LoadTableByBytes(ldl_table, nameof(innercity_decoration));
            await LoadTableByBytes(ldl_table, nameof(demand_hero));
            await LoadTableByBytes(ldl_table, nameof(dungeon));
            await LoadTableByBytes(ldl_table, nameof(monster_group));
            await LoadTableByBytes(ldl_table, nameof(common_effect));
            await LoadTableByBytes(ldl_table, nameof(tech_config));
            await LoadTableByBytes(ldl_table, nameof(item_levelreward));
            await LoadTableByBytes(ldl_table, nameof(shopping_settings));
            await LoadTableByBytes(ldl_table, nameof(shopping_foundation));
            await LoadTableByBytes(ldl_table, nameof(union_log));
            await LoadTableByBytes(ldl_table, nameof(attributes_detail));
            await LoadTableByBytes(ldl_table, nameof(attributes_source));
            await LoadTableByBytes(ldl_table, nameof(attributes_class));
            await LoadTableByBytes(ldl_table, nameof(skill_buff));
            await LoadTableByBytes(ldl_table, nameof(task_setting));
            await LoadTableByBytes(ldl_table, nameof(vip_level));
            await LoadTableByBytes(ldl_table, nameof(vip_setting));
            await LoadTableByBytes(ldl_table, nameof(vip_privilege_show));
            await LoadTableByBytes(ldl_table, nameof(survivor_list));
            await LoadTableByBytes(ldl_table, nameof(survivor_star));
            await LoadTableByBytes(ldl_table, nameof(survivor_config));
            await LoadTableByBytes(ldl_table, nameof(union_tech_donate));
            await LoadTableByBytes(ldl_table, nameof(union_tech_lv));
            await LoadTableByBytes(ldl_table, nameof(union_tech_show));
            await LoadTableByBytes(ldl_table, nameof(union_tech_tag));
            await LoadTableByBytes(ldl_table, nameof(map_city_s0));
            await LoadTableByBytes(ldl_table, nameof(map_buildpre));
            await LoadTableByBytes(ldl_table, nameof(arena_novice_robot));
            await LoadTableByBytes(ldl_table, nameof(arena_robot_type)); 
            await LoadTableByBytes(ldl_table, nameof(map_element));
			await LoadTableByBytes(ldl_table, nameof(arena_novice_rank_reward));
            await LoadTableByBytes(ldl_table, nameof(arena_novice_achieve_reward));
            await LoadTableByBytes(ldl_table, nameof(union_common_milestone));
            await LoadTableByBytes(ldl_table, nameof(mail));
            await LoadTableByBytes(ldl_table, nameof(mailbox));
            await LoadTableByBytes(ldl_table, nameof(mail_setting));
            await LoadTableByBytes(ldl_table, nameof(activity_main));
            await LoadTableByBytes(ldl_table, nameof(activity_recharge));
            await LoadTableByBytes(ldl_table, nameof(hero_spine_config));
            await LoadTableByBytes(ldl_table, nameof(trade));
            await LoadTableByBytes(ldl_table, nameof(trade_level));
            await LoadTableByBytes(ldl_table, nameof(trade_rate));
            await LoadTableByBytes(ldl_table, nameof(trade_reload));
            await LoadTableByBytes(ldl_table, nameof(trade_setting));
			await LoadTableByBytes(ldl_table, nameof(arena_compete_rank_reward));
            await LoadTableByBytes(ldl_table, nameof(arena_compete_rank_group));
            await LoadTableByBytes(ldl_table, nameof(activity_herostar));
            await LoadTableByBytes(ldl_table, nameof(activity_herostar_condition));
            await LoadTableByBytes(ldl_table, nameof(map_setting));
            
            await LoadTableByBytes(ldl_table, nameof(uav_level));
            await LoadTableByBytes(ldl_table, nameof(uav_level_advanced));
            await LoadTableByBytes(ldl_table, nameof(uav_skill));
            await LoadTableByBytes(ldl_table, nameof(uav_skin));
            await LoadTableByBytes(ldl_table, nameof(arena_setting));
            await LoadTableByBytes(ldl_table, nameof(union_gift_common));
            await LoadTableByBytes(ldl_table, nameof(union_gift_group));
            await LoadTableByBytes(ldl_table, nameof(union_gift_lv));
            await LoadTableByBytes(ldl_table, nameof(activity_recharge_score));
            await LoadTableByBytes(ldl_table, nameof(arena_peak_rank_reward));
            await LoadTableByBytes(ldl_table, nameof(monster_config));
            await LoadTableByBytes(ldl_table, nameof(tech_type));
            await LoadTableByBytes(ldl_table, nameof(tech_show));
            await LoadTableByBytes(ldl_table, nameof(activity_set));
            await LoadTableByBytes(ldl_table, nameof(battle_const_set));
            await LoadTableByBytes(ldl_table, nameof(component_level));
            await LoadTableByBytes(ldl_table, nameof(activity_battlepass_grade));
            await LoadTableByBytes(ldl_table, nameof(activity_battlepass_main));
            await LoadTableByBytes(ldl_table, nameof(activity_battlepass_reward));
            await LoadTableByBytes(ldl_table, nameof(trade_set));
            await LoadTableByBytes(ldl_table, nameof(activity_battlepass_task));
            await LoadTableByBytes(ldl_table, nameof(dungeon_reward));
            await LoadTableByBytes(ldl_table, nameof(mail_set));
            await LoadTableByBytes(ldl_table, nameof(task_dictionary));
            await LoadTableByBytes(ldl_table, nameof(activity_battlepass_vip));
            await LoadTableByBytes(ldl_table, nameof(activity_pioneer_getway));
            
            if (IsLoadSuccssful())
            {
                GameEntry.Event.Fire(this, LoadLDLConfigSuccessEventArgs.Create(flag, userData));
            }
            else
            {
                GameEntry.Event.Fire(this, LoadLDLConfigFailureEventArgs.Create(flag, userData));
            }
        }

        private static Stream ByteArrayToStream(byte[] byteArray)
        {
            return new MemoryStream(byteArray);
        }

        private async Task LoadTableByBytes(Table tab, string tableName)
        {
            var data = await LoadTableAssetAsync(tableName);
            await using (Stream stream = ByteArrayToStream(data.bytes))
            {
                var reader = new tabtoy.TableReader(stream);
                try
                {
                    tab.Deserialize(reader);
                }
                catch (Exception e)
                {
                    Debug.LogError(e);
                    throw;
                }
            }
            tab.IndexData(tableName);
        }

        private async Task LoadProtoTableByBytes(TableProto tab, string tableName)
        {
            var data = await LoadTableAssetAsync(tableName);
            await using (Stream stream = ByteArrayToStream(data.bytes))
            {
                var reader = new tabtoy.TableReader(stream);
                try
                {
                    tab.Deserialize(reader);
                }
                catch (Exception e)
                {
                    Debug.LogError(e);
                    throw;
                }
            }
            tab.IndexData(tableName);
        }

        private Task<TextAsset> LoadTableAssetAsync(string assetName)
        {
            var tcs = new TaskCompletionSource<TextAsset>();
            string assetFullName = AssetUtility.GetLDLTableAsset(assetName);
            m_tableTcs.Add(assetFullName, tcs);
            m_LoadedFlag.Add(assetFullName, false);

            GameEntry.Resource.LoadAsset(assetFullName, new LoadAssetCallbacks(
                (string assetName, object asset, float duration, object userData) =>
                {
                    m_tableTcs.TryGetValue(assetName, out TaskCompletionSource<TextAsset> tcs);
                    if (tcs != null)
                    {
                        m_LoadedFlag[assetName] = true;
                        tcs.SetResult((TextAsset)asset);
                        m_tableTcs.Remove(assetName);
                    }
                }, (string assetName, LoadResourceStatus status, string errorMessage,
                    object userData) =>
                {
                    m_tableTcs.TryGetValue(assetName, out TaskCompletionSource<TextAsset> tcs);
                    if (tcs != null)
                    {
                        tcs.SetCanceled();
                        m_tableTcs.Remove(assetName);
                    }

                    Log.Error("Can not load table from '{0}' with error message '{1}'.", assetName, errorMessage);
                }));

            return tcs.Task;
        }

        public messageDefine GetMessageDefineById(int messageID)
        {
            MessageID id = MessageID.None;
            if (MessageID.TryParse(messageID.ToString(), out id))
            {
                return GetMessageDefineById(id);
            }

            return default;
        }

        public messageDefine GetMessageDefineById(MessageID messageID)
        {
            if (ldl_proto_table.messageDefineByMessageID.ContainsKey(messageID))
            {
                return ldl_proto_table.messageDefineByMessageID[messageID];
            }
            return default;
        }

        private bool IsLoadSuccssful()
        {
            foreach (var flag in m_LoadedFlag)
            {
                if (!flag.Value)
                    return false;
            }

            return true;
        }
    }
}