using System;
using GameFramework;
using GameFramework.Event;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public abstract class BattleUnitBase:IReference
    {
        public int UID => Uid;
        public Entity EntityLogic => m_EntityLogic;
        
        protected int Uid;
        
        protected Vector3 Position;
        protected Quaternion Rotation;
        protected Vector3 Scale;

        
        private Action<Entity> m_OnLoaded;
        
        private Entity m_EntityLogic;
        protected virtual void Init(Action<Entity> onLoaded = null,Vector3? p = null,Quaternion? r = null,Vector3? s = null)
        {
            m_OnLoaded = onLoaded;
            
            Position = p ?? Vector3.one;
            Rotation = r ?? Quaternion.identity;
            Scale = s ?? Vector3.one;
        }

        protected void Load()
        {
            var path = GetPrefabPath();
            Debug.Log($"BattleUnitBase.Load: 预制体路径={path}");
            if (!string.IsNullOrEmpty(path))
            {
                Debug.Log($"开始加载战斗单位: 类型={GetEntityLogicType()}, 路径={path}");
                Uid = Game.GameEntry.Entity.ShowBattleUnit(GetEntityLogicType(),path, Position, Rotation, Scale,
                    GetBattleUnitData());
                Game.GameEntry.Event.Subscribe(ShowEntitySuccessEventArgs.EventId,Loaded);
            }
            else
            {
                Debug.LogError("预制体路径为空，无法加载战斗单位");
            }
        }
        
        private void Loaded(object sender, GameEventArgs e)
        {
            if (e is ShowEntitySuccessEventArgs showEntitySuccessEventArgs)
            {
                if (showEntitySuccessEventArgs.Entity.Id ==Uid)
                {
                    Debug.Log($"战斗单位加载完毕: Uid={Uid}, EntityType={showEntitySuccessEventArgs.Entity.GetType()}");
                    Game.GameEntry.Event.Unsubscribe(ShowEntitySuccessEventArgs.EventId,Loaded);
                    m_EntityLogic = showEntitySuccessEventArgs.Entity.Logic as Entity;
                    if (m_EntityLogic != null)
                    {
                        m_EntityLogic.SetPosition(Position);
                        m_EntityLogic.SetRotation(Rotation);
                        m_EntityLogic.SetScale(Scale);
                    }
                    if (m_OnLoaded != null)
                    {
                        m_OnLoaded.Invoke(m_EntityLogic);
                    }
                    OnLoaded(m_EntityLogic);
                } 
            }
        }

        public void SetPosition(Vector3 position)
        {
            Position.Set(position.x, position.y, position.z);
            if (m_EntityLogic!=null)
            {
                m_EntityLogic.SetPosition(Position);
            }
        }

        public Vector3 GetPosition()
        {
            return Position;
        }

        public void SetRotation(Quaternion quaternion)
        {
            Rotation = quaternion;
            if (m_EntityLogic!=null)
            {
                m_EntityLogic.SetRotation(Rotation);
            }
        }

        public Quaternion GetRotation()
        {
            return Rotation;
        }

        public void SetScale(Vector3 scale)
        {
            Scale.Set(scale.x, scale.y, scale.z);
            if (m_EntityLogic!=null)
            {
                m_EntityLogic.SetScale(Scale);
            }
        }

        public Vector3 GetScale()
        {
            return Scale;
        }

        public void Tick(float dt)
        {
            OnTick(dt);
        }

        protected abstract void OnTick(float dt);
        
        protected abstract Type GetEntityLogicType();

        protected abstract string GetPrefabPath();

        protected abstract ED_BattleUnitBase GetBattleUnitData();

        protected abstract void UnInit();

        protected abstract void OnLoaded(Entity entity);
        
        public void Clear()
        {
            m_EntityLogic = null;
            m_OnLoaded = null;
            if (Uid != 0)
            {
                Game.GameEntry.Entity.HideEntity(Uid);
            }
            Uid = 0;

            bool hasEvent = Game.GameEntry.Event.Check(ShowEntitySuccessEventArgs.EventId, Loaded);
            if (hasEvent)
            {
                Game.GameEntry.Event.Unsubscribe(ShowEntitySuccessEventArgs.EventId, Loaded);
            }
            
            UnInit();
        }
    }
}