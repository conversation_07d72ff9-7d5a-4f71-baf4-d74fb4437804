%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &8434293544091840
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5360523932349507576}
  m_Layer: 0
  m_Name: Bone068
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5360523932349507576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8434293544091840}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000005427537, y: -1.9398885e-11, z: 0.00042295447, w: 0.99999994}
  m_LocalPosition: {x: -0.20988199, y: 0.0000008869171, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 141970402161661300}
  m_Father: {fileID: 6038473392040392976}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &142884267032016675
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8280227341744631156}
  m_Layer: 0
  m_Name: Bone075
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8280227341744631156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 142884267032016675}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000028131353, y: 0.00000013740849, z: -0.19336212, w: 0.98112744}
  m_LocalPosition: {x: -0.17381011, y: -0.000000009536743, z: 0.000000076293944}
  m_LocalScale: {x: 0.99999976, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6276296988369577089}
  m_Father: {fileID: 6412785885622267996}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &229854838316317036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4141042780456311366}
  m_Layer: 0
  m_Name: Point021
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4141042780456311366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 229854838316317036}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.30508476, z: -0.14043576}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8586719932821099905}
  m_Father: {fileID: 1651586556973787343}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &232525420203483846
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2145725793344461915}
  m_Layer: 0
  m_Name: Bone020
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2145725793344461915
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 232525420203483846}
  serializedVersion: 2
  m_LocalRotation: {x: -0.046457592, y: 0.70557874, z: -0.7051783, w: -0.05219182}
  m_LocalPosition: {x: -0.43561286, y: 0.80785763, z: -1.197099}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &237501176855446690
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4693615105171158896}
  m_Layer: 0
  m_Name: Bone015
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4693615105171158896
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 237501176855446690}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.4795639, y: 0.000000076293944, z: -0.000000020963016}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 312063711159215611}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &269375753165815205
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3143405146758524699}
  m_Layer: 0
  m_Name: Dummy003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3143405146758524699
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 269375753165815205}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000337172, y: 0.000000001370516, z: 0.70422673, w: 0.7099751}
  m_LocalPosition: {x: -0.5674309, y: -0.012074556, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4514887143990050006}
  - {fileID: 1688978804504216214}
  - {fileID: 3114242233896866078}
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &327290677301916711
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7076728598441496716}
  m_Layer: 0
  m_Name: Bone044
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7076728598441496716
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 327290677301916711}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000051573092, y: 0.00000028489723, z: -0.06384932, w: 0.99795955}
  m_LocalPosition: {x: -0.20851249, y: 0.000000019073486, z: 0.00000015258789}
  m_LocalScale: {x: 0.9999995, y: 0.9999995, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2165660100848843614}
  m_Father: {fileID: 6226028342133701073}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &467980854272059147
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1846618809913684022}
  m_Layer: 0
  m_Name: Point006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1846618809913684022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 467980854272059147}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.24697326, z: -0.15012102}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8981947964279206327}
  m_Father: {fileID: 1036507165499587953}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &606122813772245816
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4387247167726070047}
  m_Layer: 0
  m_Name: Bone072
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4387247167726070047
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 606122813772245816}
  serializedVersion: 2
  m_LocalRotation: {x: 1.7605425e-10, y: -0.0000001828765, z: -0.00036889865, w: 0.99999994}
  m_LocalPosition: {x: -0.22759159, y: -0.00000042915343, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7260478392015209847}
  m_Father: {fileID: 5608545210855762436}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &629205015544728711
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8260334300778429156}
  m_Layer: 0
  m_Name: Bone023(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8260334300778429156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 629205015544728711}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023784155, y: -0.00000016545633, z: 0.0034964017, w: 0.9999939}
  m_LocalPosition: {x: -0.7014249, y: 0.42510623, z: 0.513057}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &686519717456806857
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 932601482573696061}
  m_Layer: 0
  m_Name: IK Chain002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &932601482573696061
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 686519717456806857}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0.8634017, y: 0.7282643, z: 0.13148166}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2657182712835043920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &774852700417700975
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9100573872828366256}
  m_Layer: 0
  m_Name: Point017
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9100573872828366256
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 774852700417700975}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.44309917, z: 0.0072639463}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6267376918551394481}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &791048756827361193
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6278041516205912796}
  m_Layer: 0
  m_Name: Bone084
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6278041516205912796
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 791048756827361193}
  serializedVersion: 2
  m_LocalRotation: {x: 1.1596106e-16, y: 1.0768537e-14, z: -0.010767882, w: 0.99994206}
  m_LocalPosition: {x: -0.22891873, y: -0.0000072479247, z: 0}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8949682601454119820}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &808051279831029061
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1492915450649976122}
  m_Layer: 0
  m_Name: Bone025(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1492915450649976122
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 808051279831029061}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023784155, y: -0.00000016545633, z: 0.0034964017, w: 0.9999939}
  m_LocalPosition: {x: -0.7014249, y: 0.4251062, z: -0.46658453}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &829540853084793292
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1321140216081128432}
  m_Layer: 0
  m_Name: Bone065
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1321140216081128432
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 829540853084793292}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000046437734, y: 0.0000001170551, z: -0.18205422, w: 0.9832885}
  m_LocalPosition: {x: -0.15536705, y: -0.000000038146972, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7670751876844018325}
  m_Father: {fileID: 5881751480378482290}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &970824694371036241
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3706310414627823443}
  m_Layer: 0
  m_Name: Bone018
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3706310414627823443
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 970824694371036241}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02138307, y: -0.7067831, z: 0.70693415, w: -0.015636757}
  m_LocalPosition: {x: -0.34544134, y: -0.57976127, z: -1.197098}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1025764664343762969
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5608545210855762436}
  m_Layer: 0
  m_Name: Bone071
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5608545210855762436
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1025764664343762969}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000011423289, y: 5.72355e-10, z: -0.013728638, w: 0.99990577}
  m_LocalPosition: {x: -0.20572376, y: 0.000000028610229, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4387247167726070047}
  m_Father: {fileID: 4944984587569294365}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1223626990711783041
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1788703128430804623}
  m_Layer: 0
  m_Name: Bone047
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1788703128430804623
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1223626990711783041}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000003117751, y: 0.000000010397188, z: -0.27717763, w: 0.9608187}
  m_LocalPosition: {x: -0.14890197, y: -0.000000038146972, z: 0}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 482062234377448531}
  m_Father: {fileID: 2002611272420700795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1362898274166884575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 141970402161661300}
  m_Layer: 0
  m_Name: Bone069
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &141970402161661300
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1362898274166884575}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000027554439, y: -0.00000018551533, z: 0.0022769293, w: 0.99999744}
  m_LocalPosition: {x: -0.2261781, y: 0.000000095367426, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4944984587569294365}
  m_Father: {fileID: 5360523932349507576}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1485585103849064547
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1193475051202865780}
  m_Layer: 0
  m_Name: Bone006
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1193475051202865780
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1485585103849064547}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.39024407, y: 0.00000015258789, z: -0.000000046516977}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4514887143990050006}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1487859730448821945
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2686750240841922685}
  m_Layer: 0
  m_Name: Bone060
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2686750240841922685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1487859730448821945}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000014985156, y: -0.0000000017073031, z: -0.15831012, w: 0.98738945}
  m_LocalPosition: {x: -0.16898789, y: 0.0000006866455, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2157427781147224817}
  m_Father: {fileID: 7299950854097443828}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1716740424291885833
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1245328670715726249}
  - component: {fileID: 8750538834995620985}
  - component: {fileID: 1650958338449088809}
  m_Layer: 0
  m_Name: 18_tank
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1245328670715726249
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716740424291885833}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2657182712835043920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8750538834995620985
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716740424291885833}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e927ab6d10b6f484c8fc4493b2d32b16, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4375854324983403421, guid: 6631e1e6cc1a3324981e4fb6a7046fa0, type: 3}
  m_Bones:
  - {fileID: 6387797265431783506}
  - {fileID: 8517474145966650295}
  - {fileID: 3114242233896866078}
  - {fileID: 6452209518611813383}
  - {fileID: 1688978804504216214}
  - {fileID: 2145725793344461915}
  - {fileID: 6665375934158185116}
  - {fileID: 3706310414627823443}
  - {fileID: 1343503503288766220}
  - {fileID: 8260334300778429156}
  - {fileID: 1352267729293359297}
  - {fileID: 1492915450649976122}
  - {fileID: 6682650372957148236}
  - {fileID: 4514887143990050006}
  - {fileID: 4807960841191368761}
  - {fileID: 312063711159215611}
  - {fileID: 2390467810327671602}
  - {fileID: 8792807559513550431}
  - {fileID: 4341897123119528466}
  - {fileID: 8998813511687239700}
  - {fileID: 3365759111935735347}
  - {fileID: 5558857554925225261}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 6387797265431783506}
  m_AABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 1.549522, y: 1.4958771, z: 1.3397557}
  m_DirtyAABB: 0
--- !u!114 &1650958338449088809
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1716740424291885833}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eaeffb5043afebc49a5ecf62000f3569, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mainLightColor: {r: 1, g: 1, b: 1, a: 1}
  mainLightRotation: {x: 35.7, y: 59.7, z: 36.6}
  auxiliaryLightColor: {r: 2.118547, g: 1.6623534, b: 0.5084513, a: 1}
  auxiliaryLightRotation: {x: 174.1, y: 70.1, z: -17.2}
  custommaterials:
  - {fileID: 2100000, guid: e927ab6d10b6f484c8fc4493b2d32b16, type: 2}
--- !u!1 &1974493163158967009
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2437193528566635163}
  m_Layer: 0
  m_Name: Point001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2437193528566635163
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1974493163158967009}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: 0.85732025, y: 0.725775, z: -0.083771974}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5170275441645835519}
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2352200811401812963
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9165697840787571485}
  m_Layer: 0
  m_Name: Bone008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9165697840787571485
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2352200811401812963}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.39024407, y: 0.00000015258789, z: -0.000000046516977}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1688978804504216214}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2379379684886028132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1688978804504216214}
  m_Layer: 0
  m_Name: Bone007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1688978804504216214
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2379379684886028132}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.006241043, w: 0.99998057}
  m_LocalPosition: {x: -0.19775344, y: 0.42921904, z: -0.8724968}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9165697840787571485}
  m_Father: {fileID: 3143405146758524699}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2408813977395117752
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2343007502436300514}
  - component: {fileID: 3455431640378050556}
  - component: {fileID: 7936065544764819408}
  - component: {fileID: 6039369576278955456}
  m_Layer: 5
  m_Name: role_11505_show
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2343007502436300514
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2408813977395117752}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2657182712835043920}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3455431640378050556
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2408813977395117752}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f1770034b84515b45a96b3f473aae6c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ObjTransform: {fileID: 0}
--- !u!114 &7936065544764819408
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2408813977395117752}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d1eb6e16bbb69d1478b3a81466b2d544, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  slots:
  - Slot: 0
    Transform: {fileID: 0}
  - Slot: 9
    Transform: {fileID: 0}
--- !u!114 &6039369576278955456
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2408813977395117752}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eaeffb5043afebc49a5ecf62000f3569, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mainLightColor: {r: 1, g: 1, b: 1, a: 1}
  mainLightRotation: {x: 0, y: 0, z: 0}
  auxiliaryLightColor: {r: 1, g: 1, b: 1, a: 1}
  auxiliaryLightRotation: {x: 0, y: 0, z: 0}
  custommaterials: []
--- !u!1 &2457119948223264156
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6452209518611813383}
  m_Layer: 0
  m_Name: Bone012
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6452209518611813383
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2457119948223264156}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00028528887, y: 0.015070994, z: -0.018924123, w: 0.99970734}
  m_LocalPosition: {x: -0.5657347, y: 0.000000114440915, z: 0.000000024059117}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4211410193544012903}
  m_Father: {fileID: 4751751967963374554}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2534412839578611328
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8447600702775461539}
  m_Layer: 0
  m_Name: Point024
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8447600702775461539
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2534412839578611328}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.43825656, z: -0.036319617}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 578212868402431671}
  m_Father: {fileID: 2948903240106704414}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2704261742037349555
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2489395058038500550}
  m_Layer: 0
  m_Name: Point030
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2489395058038500550
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2704261742037349555}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.06295395, z: 0.1404358}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8338430977859769638}
  m_Father: {fileID: 5223454582821257491}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2731139099165209586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8949682601454119820}
  m_Layer: 0
  m_Name: Bone083
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8949682601454119820
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2731139099165209586}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000024504152, y: -5.411706e-10, z: -0.012705431, w: 0.9999193}
  m_LocalPosition: {x: -0.18939288, y: 0.000000076293944, z: 0}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6278041516205912796}
  m_Father: {fileID: 6973866669050784019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2761370463644805058
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3354305740681447338}
  m_Layer: 0
  m_Name: Bone058
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3354305740681447338
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761370463644805058}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000395001, y: 0.00000017262744, z: -0.018097278, w: 0.99983627}
  m_LocalPosition: {x: -0.22344261, y: 0.000000076293944, z: -0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7299950854097443828}
  m_Father: {fileID: 6531297925353255350}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2831007043242180922
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6434590039845881931}
  m_Layer: 0
  m_Name: Bone039
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6434590039845881931
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2831007043242180922}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000037020413, y: -0.00000016865481, z: 0.00042303876, w: 0.99999994}
  m_LocalPosition: {x: -0.20988207, y: 0.0000008869171, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4701160260506119589}
  m_Father: {fileID: 7567211234118238366}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2872282375198349899
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9210078939962559239}
  m_Layer: 0
  m_Name: Bone050
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9210078939962559239
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2872282375198349899}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000012008171, y: -0.00000016309406, z: -0.26894838, w: 0.9631546}
  m_LocalPosition: {x: -0.17542678, y: -0.00000015258789, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2367221453285746225}
  m_Father: {fileID: 2512500308376457675}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2875148894508655613
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8411582059782495188}
  m_Layer: 0
  m_Name: Point010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8411582059782495188
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2875148894508655613}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.42615005, z: 0.009685192}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8130257411792057151}
  m_Father: {fileID: 2764354375131656507}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2891193491069394018
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2764354375131656507}
  m_Layer: 0
  m_Name: Point009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2764354375131656507
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2891193491069394018}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.4479418, z: -0.0024212836}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8411582059782495188}
  m_Father: {fileID: 2069920234053543253}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2908933762148357881
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6412785885622267996}
  m_Layer: 0
  m_Name: Bone074
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6412785885622267996
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2908933762148357881}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000013593125, y: -0.00000022874721, z: -0.0054617506, w: 0.9999851}
  m_LocalPosition: {x: -0.18927117, y: -0.000000028610229, z: 0}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8280227341744631156}
  m_Father: {fileID: 7260478392015209847}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2983551070737904381
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7299950854097443828}
  m_Layer: 0
  m_Name: Bone059
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7299950854097443828
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2983551070737904381}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000000104486, y: -0.00000017377997, z: -0.035122745, w: 0.99938303}
  m_LocalPosition: {x: -0.22085425, y: -0.000000076293944, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2686750240841922685}
  m_Father: {fileID: 3354305740681447338}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2989107850984214759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1043595920077558213}
  m_Layer: 0
  m_Name: Bone010
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1043595920077558213
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2989107850984214759}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -1.2246469e-16, w: 1}
  m_LocalPosition: {x: -0.46866485, y: -0.000000076293944, z: -7.867129e-13}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3114242233896866078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3015628620198213644
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2390467810327671602}
  m_Layer: 0
  m_Name: Bone017
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2390467810327671602
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3015628620198213644}
  serializedVersion: 2
  m_LocalRotation: {x: -0.021804927, y: 0.70677024, z: -0.70657015, w: -0.027549671}
  m_LocalPosition: {x: -0.3454424, y: -0.5797597, z: 1.2069926}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3084700033760861790
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4341897123119528466}
  m_Layer: 0
  m_Name: Bone023
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4341897123119528466
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3084700033760861790}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016234608, y: -5.676397e-10, z: 0.9999939, w: 0.0034964574}
  m_LocalPosition: {x: 0.7035619, y: 0.42510623, z: 0.513057}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3151253392476264734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1088424011585138786}
  m_Layer: 0
  m_Name: Point027
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1088424011585138786
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3151253392476264734}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.42615005, z: 0.009685192}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8656278834150765178}
  m_Father: {fileID: 5768583217489375511}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3244492066402920527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5680296388209611144}
  m_Layer: 0
  m_Name: Bone002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5680296388209611144
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3244492066402920527}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.3406436, y: -0.000000114440915, z: -0.00000015721349}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3244657878566000597
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7260478392015209847}
  m_Layer: 0
  m_Name: Bone073
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7260478392015209847
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3244657878566000597}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000017949076, y: 0.00000029208738, z: -0.06384933, w: 0.99795955}
  m_LocalPosition: {x: -0.20851249, y: 0.000000009536743, z: 0.00000015258789}
  m_LocalScale: {x: 0.9999996, y: 0.9999996, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6412785885622267996}
  m_Father: {fileID: 4387247167726070047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3257249412165091436
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4751751967963374554}
  m_Layer: 0
  m_Name: Bone011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4751751967963374554
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3257249412165091436}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7046926, y: -0.11478941, z: 0.049682666, w: 0.69840056}
  m_LocalPosition: {x: -0.75962335, y: 1.0045935, z: -0.83138883}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6452209518611813383}
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3349424134383395131
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6665375934158185116}
  m_Layer: 0
  m_Name: Bone019
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6665375934158185116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3349424134383395131}
  serializedVersion: 2
  m_LocalRotation: {x: -0.002874301, y: -0.7071007, z: 0.7071013, w: 0.0028739662}
  m_LocalPosition: {x: -0.44149488, y: 0.08431465, z: -1.1970985}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3409761547143497965
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2508903308937247219}
  m_Layer: 0
  m_Name: Bone077
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2508903308937247219
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3409761547143497965}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000046396855, y: -0.000000017666684, z: -0.38247967, w: 0.9239639}
  m_LocalPosition: {x: -0.14955398, y: 0, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5229744173440642654}
  m_Father: {fileID: 6276296988369577089}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3432880141076395232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4982151518254986614}
  m_Layer: 0
  m_Name: Point013
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4982151518254986614
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3432880141076395232}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.06295395, z: 0.1404358}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4025223835197656328}
  m_Father: {fileID: 2247836658590412954}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3515276593557005976
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5881751480378482290}
  m_Layer: 0
  m_Name: Bone064
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5881751480378482290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3515276593557005976}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000001108226, y: -0.00000018314614, z: -0.102078244, w: 0.99477637}
  m_LocalPosition: {x: -0.17897254, y: 0.000000076293944, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1321140216081128432}
  m_Father: {fileID: 8775788777013853163}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3517638414523064010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 312063711159215611}
  m_Layer: 0
  m_Name: Bone014
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &312063711159215611
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3517638414523064010}
  serializedVersion: 2
  m_LocalRotation: {x: -0.03760018, y: -0.7023356, z: 0.71004206, w: -0.03393001}
  m_LocalPosition: {x: -0.4414955, y: 0.08431633, z: 1.206992}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4693615105171158896}
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3533122236175750499
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5337867796415803713}
  m_Layer: 0
  m_Name: Bone037
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5337867796415803713
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3533122236175750499}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000025681564, y: -0.000000028869572, z: -0.011838025, w: 0.99992996}
  m_LocalPosition: {x: -0.25664908, y: -0.0000000047683715, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7567211234118238366}
  m_Father: {fileID: 4839782231939942585}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3600007974272630022
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1651586556973787343}
  m_Layer: 0
  m_Name: Point020
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1651586556973787343
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3600007974272630022}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.3922516, z: -0.016949158}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4141042780456311366}
  m_Father: {fileID: 8576494166361558358}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3624790538834476986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2479133766317680866}
  m_Layer: 0
  m_Name: Bone029
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2479133766317680866
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3624790538834476986}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000004568189, y: 0.0000000011113085, z: -0.01809728, w: 0.99983627}
  m_LocalPosition: {x: -0.22344252, y: 0, z: -0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7230457030015408012}
  m_Father: {fileID: 5513915628710821992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3664235860727962815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6267376918551394481}
  m_Layer: 0
  m_Name: Point016
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6267376918551394481
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3664235860727962815}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.34382567, z: 0.048426054}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9100573872828366256}
  m_Father: {fileID: 618584448507422544}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3689275596959787339
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8130257411792057151}
  m_Layer: 0
  m_Name: Point011
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8130257411792057151
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3689275596959787339}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.35108963, z: 0.053268794}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2247836658590412954}
  m_Father: {fileID: 8411582059782495188}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3706226578488303892
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6226028342133701073}
  m_Layer: 0
  m_Name: Bone043
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6226028342133701073
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3706226578488303892}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000053993098, y: -0.00000018291635, z: -0.0003687376, w: 0.99999994}
  m_LocalPosition: {x: -0.22759162, y: -0.00000044822693, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7076728598441496716}
  m_Father: {fileID: 6492204131477246884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3757111543074184375
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1343503503288766220}
  m_Layer: 0
  m_Name: Bone021(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1343503503288766220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3757111543074184375}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023803408, y: -0.000000110392214, z: 0.0034964017, w: 0.9999939}
  m_LocalPosition: {x: -0.7014249, y: 0.42510635, z: 0.98276585}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3880946897760463744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8338430977859769638}
  m_Layer: 0
  m_Name: Point031
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8338430977859769638
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3880946897760463744}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.06053268, z: 0.1476997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2164738407253646366}
  m_Father: {fileID: 2489395058038500550}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3959375923061923484
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 578212868402431671}
  m_Layer: 0
  m_Name: Point025
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &578212868402431671
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3959375923061923484}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.45762697, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5768583217489375511}
  m_Father: {fileID: 8447600702775461539}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3991768638174066294
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3399331136403743229}
  m_Layer: 0
  m_Name: Bone034
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3399331136403743229
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3991768638174066294}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000003732653, y: 0.00000015596406, z: -0.46381095, w: 0.88593423}
  m_LocalPosition: {x: -0.22233807, y: 0, z: 0.000000076293944}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4159983573126065927}
  m_Father: {fileID: ************6720480}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4034017818680844100
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7567211234118238366}
  m_Layer: 0
  m_Name: Bone038
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7567211234118238366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4034017818680844100}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000029227715, y: 0.00000017593682, z: -0.029497173, w: 0.9995649}
  m_LocalPosition: {x: -0.21536693, y: 0.00000082015987, z: 0.000000076293944}
  m_LocalScale: {x: 0.9999998, y: 0.9999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6434590039845881931}
  m_Father: {fileID: 5337867796415803713}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4037937967602090017
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9015764655598044376}
  m_Layer: 0
  m_Name: Point034
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9015764655598044376
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4037937967602090017}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.44309917, z: 0.0072639463}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 751554438987934968}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4066946946353751466
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6682650372957148236}
  m_Layer: 0
  m_Name: Bone026(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6682650372957148236
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4066946946353751466}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023803408, y: -0.000000110392214, z: 0.0034964017, w: 0.9999939}
  m_LocalPosition: {x: -0.7014249, y: 0.42510617, z: -0.9319919}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4194701033859648775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6038473392040392976}
  m_Layer: 0
  m_Name: Bone067
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6038473392040392976
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4194701033859648775}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000009402883, y: 0.00000017843153, z: -0.02949726, w: 0.9995649}
  m_LocalPosition: {x: -0.215367, y: 0.00000082015987, z: 0.00000015258789}
  m_LocalScale: {x: 0.9999997, y: 0.9999997, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5360523932349507576}
  m_Father: {fileID: 7670751876844018325}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4206462012239538042
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6738986016492519890}
  m_Layer: 0
  m_Name: Bone081
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6738986016492519890
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4206462012239538042}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000052519393, y: 0.0000003900425, z: -0.057306148, w: 0.9983567}
  m_LocalPosition: {x: -0.17016846, y: -0.000000076293944, z: 0.00000015258789}
  m_LocalScale: {x: 0.99999946, y: 0.99999946, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6973866669050784019}
  m_Father: {fileID: 4406230230589986052}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4270129724151880251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8792807559513550431}
  m_Layer: 0
  m_Name: Bone021
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8792807559513550431
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4270129724151880251}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016234608, y: -5.676397e-10, z: 0.9999939, w: 0.0034964574}
  m_LocalPosition: {x: 0.7035619, y: 0.42510635, z: 0.98276585}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4295868268715088427
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2948903240106704414}
  m_Layer: 0
  m_Name: Point023
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2948903240106704414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4295868268715088427}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.24697326, z: -0.15012102}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8447600702775461539}
  m_Father: {fileID: 8586719932821099905}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4509421154280938526
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6191743973002671991}
  m_Layer: 0
  m_Name: IK Chain001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6191743973002671991
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4509421154280938526}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0.8573205, y: 0.7282644, z: 0.13148223}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2657182712835043920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4591835393205277741
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 117329463648930968}
  m_Layer: 0
  m_Name: Bone032
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &117329463648930968
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4591835393205277741}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000090273693, y: -0.00000023350532, z: -0.050476857, w: 0.99872524}
  m_LocalPosition: {x: -0.18014984, y: 0, z: 0.000000076293944}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: ************6720480}
  m_Father: {fileID: 6494068722993973984}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4593558118948686203
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7547127614180488004}
  m_Layer: 0
  m_Name: Bone055
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7547127614180488004
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4593558118948686203}
  serializedVersion: 2
  m_LocalRotation: {x: -1.6788225e-15, y: 9.066919e-15, z: -0.010767882, w: 0.99994206}
  m_LocalPosition: {x: -0.22891872, y: -0.000007095337, z: 0}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7434898894375035844}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4658399818015583759
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4944984587569294365}
  m_Layer: 0
  m_Name: Bone070
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4944984587569294365
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4658399818015583759}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000018669666, y: 0.00000018541698, z: 0.0000006644987, w: 1}
  m_LocalPosition: {x: -0.20573619, y: -0.00000030517577, z: 0.000000076293944}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5608545210855762436}
  m_Father: {fileID: 141970402161661300}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4742077746600143114
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1352267729293359297}
  m_Layer: 0
  m_Name: Bone024(mirrored)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1352267729293359297
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4742077746600143114}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000023784766, y: -0.00000016371008, z: 0.0034964017, w: 0.9999939}
  m_LocalPosition: {x: -0.7014249, y: 0.42510623, z: 0.019662552}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4747417317021677888
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5513915628710821992}
  m_Layer: 0
  m_Name: Bone028
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5513915628710821992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4747417317021677888}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000053547733, y: 1.8705212e-10, z: -0.0034932205, w: 0.9999939}
  m_LocalPosition: {x: -0.27249324, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2479133766317680866}
  m_Father: {fileID: 2196056778676711363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4777431135730716521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1099620446284344478}
  m_Layer: 0
  m_Name: Dummy001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1099620446284344478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4777431135730716521}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006657903, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6387797265431783506}
  - {fileID: 8792807559513550431}
  - {fileID: 1343503503288766220}
  - {fileID: 98341029822100205}
  - {fileID: 4341897123119528466}
  - {fileID: 8260334300778429156}
  - {fileID: 8998813511687239700}
  - {fileID: 1352267729293359297}
  - {fileID: 3365759111935735347}
  - {fileID: 1492915450649976122}
  - {fileID: 5558857554925225261}
  - {fileID: 6682650372957148236}
  - {fileID: 2437193528566635163}
  - {fileID: 699705377645734382}
  m_Father: {fileID: 2657182712835043920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4799836435993659209
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8625279316205514047}
  m_Layer: 0
  m_Name: Dummy002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8625279316205514047
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4799836435993659209}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000337172, y: 0.000000001370516, z: 0.70422673, w: 0.7099751}
  m_LocalPosition: {x: -1.9837627, y: -0.00567194, z: -0.00000061035155}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8517474145966650295}
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4847881657170476736
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8517474145966650295}
  m_Layer: 0
  m_Name: Bone003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8517474145966650295
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4847881657170476736}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: 1.4210855e-14, z: 0.00000023841858, w: 1}
  m_LocalPosition: {x: -0.043596726, y: 0.000000009536743, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4115616457676300291}
  m_Father: {fileID: 8625279316205514047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4897991869191125606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6494068722993973984}
  m_Layer: 0
  m_Name: Bone031
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6494068722993973984
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4897991869191125606}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000012957017, y: 0.000000029223866, z: -0.15831009, w: 0.98738945}
  m_LocalPosition: {x: -0.16898796, y: 0.0000007629394, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 117329463648930968}
  m_Father: {fileID: 7230457030015408012}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5048773507765688266
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 690888383768825406}
  m_Layer: 0
  m_Name: Bone052
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &690888383768825406
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5048773507765688266}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000057023634, y: 0.000000003939731, z: -0.057306238, w: 0.9983567}
  m_LocalPosition: {x: -0.17016868, y: -0.000000076293944, z: 0.000000076293944}
  m_LocalScale: {x: 0.99999946, y: 0.99999946, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2867754438782892110}
  m_Father: {fileID: 2367221453285746225}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5113837640965430509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2196056778676711363}
  m_Layer: 0
  m_Name: Bone027
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2196056778676711363
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5113837640965430509}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 4.32978e-17, z: 0.7071067, w: -4.3297806e-17}
  m_LocalPosition: {x: 0.85732013, y: 0.725775, z: -0.08377185}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5513915628710821992}
  m_Father: {fileID: 2657182712835043920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5117505518325785876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 98341029822100205}
  m_Layer: 0
  m_Name: Bone022
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &98341029822100205
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5117505518325785876}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016265572, y: 0.00000008799192, z: 0.9999939, w: 0.003496191}
  m_LocalPosition: {x: 1.0498089, y: 0.422685, z: 0.9827658}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5141877016822650778
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2002611272420700795}
  m_Layer: 0
  m_Name: Bone046
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2002611272420700795
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5141877016822650778}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000032552296, y: -0.0000005065996, z: -0.19336183, w: 0.9811275}
  m_LocalPosition: {x: -0.1738102, y: -0.000000028610229, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1788703128430804623}
  m_Father: {fileID: 2165660100848843614}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5192368839270027199
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6276296988369577089}
  m_Layer: 0
  m_Name: Bone076
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6276296988369577089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5192368839270027199}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000027776512, y: -0.00000004986404, z: -0.27717835, w: 0.9608185}
  m_LocalPosition: {x: -0.14890175, y: 0, z: -0.00000015258789}
  m_LocalScale: {x: 0.9999998, y: 0.9999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2508903308937247219}
  m_Father: {fileID: 8280227341744631156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5276448840057696310
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4839782231939942585}
  m_Layer: 0
  m_Name: Bone036
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4839782231939942585
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5276448840057696310}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000012367624, y: -0.00000005702049, z: -0.18205397, w: 0.9832886}
  m_LocalPosition: {x: -0.15536697, y: -0.000000057220458, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5337867796415803713}
  m_Father: {fileID: 4159983573126065927}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5312965015741887147
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4025223835197656328}
  m_Layer: 0
  m_Name: Point014
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4025223835197656328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5312965015741887147}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.06053268, z: 0.1476997}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 618584448507422544}
  m_Father: {fileID: 4982151518254986614}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5324938286571405119
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2164738407253646366}
  m_Layer: 0
  m_Name: Point032
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2164738407253646366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5324938286571405119}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.13801445, z: 0.1162228}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 751554438987934968}
  m_Father: {fileID: 8338430977859769638}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5325536430767120060
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4701160260506119589}
  m_Layer: 0
  m_Name: Bone040
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4701160260506119589
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5325536430767120060}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000000031022418, y: 0.00000016867877, z: 0.0022769372, w: 0.99999744}
  m_LocalPosition: {x: -0.2261781, y: 0.000000085830685, z: 0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3959472543585617778}
  m_Father: {fileID: 6434590039845881931}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5441963763511067941
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3114242233896866078}
  m_Layer: 0
  m_Name: Bone009
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3114242233896866078
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5441963763511067941}
  serializedVersion: 2
  m_LocalRotation: {x: -1.7296353e-23, y: 8.6595606e-17, z: -0.70710665, w: 0.70710695}
  m_LocalPosition: {x: 0.98315144, y: 0.17663482, z: -0.00000005732232}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1043595920077558213}
  m_Father: {fileID: 3143405146758524699}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5580528075007155270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2512500308376457675}
  m_Layer: 0
  m_Name: Bone049
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2512500308376457675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5580528075007155270}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000032289023, y: -0.00000012761411, z: -0.25943357, w: 0.965761}
  m_LocalPosition: {x: -0.16390143, y: 0.00000015258789, z: 0}
  m_LocalScale: {x: 0.99999976, y: 0.99999976, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9210078939962559239}
  m_Father: {fileID: 482062234377448531}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5627234827301892357
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7670751876844018325}
  m_Layer: 0
  m_Name: Bone066
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7670751876844018325
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5627234827301892357}
  serializedVersion: 2
  m_LocalRotation: {x: 4.4139917e-10, y: -0.00000017657734, z: -0.011837955, w: 0.99992996}
  m_LocalPosition: {x: -0.25664902, y: -0.000000014305114, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6038473392040392976}
  m_Father: {fileID: 1321140216081128432}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5745534210921722060
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2657182712835043920}
  - component: {fileID: 8908823040406237554}
  m_Layer: 0
  m_Name: 18_tank
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2657182712835043920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5745534210921722060}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.93, y: 0.93, z: 0.93}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 1245328670715726249}
  - {fileID: 1414921479670630306}
  - {fileID: 2196056778676711363}
  - {fileID: 8489983714968659993}
  - {fileID: 1099620446284344478}
  - {fileID: 6191743973002671991}
  - {fileID: 932601482573696061}
  m_Father: {fileID: 2343007502436300514}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &8908823040406237554
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5745534210921722060}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 47b196c39cd1942449f5ee34f113de7d, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &5747268183565431792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4115616457676300291}
  m_Layer: 0
  m_Name: Bone004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4115616457676300291
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5747268183565431792}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0.000000020489097, z: -0, w: 1}
  m_LocalPosition: {x: -0.5940056, y: 0.000000114440915, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8517474145966650295}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5947395169792716779
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2157427781147224817}
  m_Layer: 0
  m_Name: Bone061
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2157427781147224817
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5947395169792716779}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000023069369, y: -0.000000280438, z: -0.050477047, w: 0.99872524}
  m_LocalPosition: {x: -0.18014984, y: -0.000000076293944, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7738459325810178366}
  m_Father: {fileID: 2686750240841922685}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6135325401490116270
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 751554438987934968}
  m_Layer: 0
  m_Name: Point033
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &751554438987934968
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6135325401490116270}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.34382567, z: 0.048426054}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9015764655598044376}
  m_Father: {fileID: 2164738407253646366}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6154743454789140336
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2867754438782892110}
  m_Layer: 0
  m_Name: Bone053
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2867754438782892110
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6154743454789140336}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000114993455, y: 2.2512049e-14, z: -0.0000009294596, w: 1}
  m_LocalPosition: {x: -0.22494426, y: 0.0000003814697, z: 0}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7434898894375035844}
  m_Father: {fileID: 690888383768825406}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6241457128631163681
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3959472543585617778}
  m_Layer: 0
  m_Name: Bone041
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3959472543585617778
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6241457128631163681}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000009887244, y: -0.00000018542808, z: 0.0000004789327, w: 1}
  m_LocalPosition: {x: -0.20573616, y: -0.00000029563904, z: 0.000000076293944}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6492204131477246884}
  m_Father: {fileID: 4701160260506119589}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6309426538888320508
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7434898894375035844}
  m_Layer: 0
  m_Name: Bone054
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7434898894375035844
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6309426538888320508}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000009356495, y: 3.2051878e-10, z: -0.012705474, w: 0.9999193}
  m_LocalPosition: {x: -0.18939285, y: -0.000000076293944, z: 0}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7547127614180488004}
  m_Father: {fileID: 2867754438782892110}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6338378814604872172
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 701370392982265923}
  m_Layer: 0
  m_Name: Point004
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &701370392982265923
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6338378814604872172}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.30508476, z: -0.14043576}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1036507165499587953}
  m_Father: {fileID: 5800409760207313197}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6361216038356169847
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8656278834150765178}
  m_Layer: 0
  m_Name: Point028
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8656278834150765178
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6361216038356169847}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.35108963, z: 0.053268794}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5223454582821257491}
  m_Father: {fileID: 1088424011585138786}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6386868834224344314
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8998813511687239700}
  m_Layer: 0
  m_Name: Bone024
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8998813511687239700
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6386868834224344314}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016234608, y: -5.676397e-10, z: 0.9999939, w: 0.0034964574}
  m_LocalPosition: {x: 0.7035619, y: 0.42510623, z: 0.019662552}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6387736148648094127
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7230457030015408012}
  m_Layer: 0
  m_Name: Bone030
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7230457030015408012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6387736148648094127}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000014996301, y: 0.00000000582306, z: -0.03512263, w: 0.99938303}
  m_LocalPosition: {x: -0.2208541, y: -0.000000076293944, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6494068722993973984}
  m_Father: {fileID: 2479133766317680866}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6497292616681526411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5170275441645835519}
  m_Layer: 0
  m_Name: Point002
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5170275441645835519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6497292616681526411}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.45520577, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5800409760207313197}
  m_Father: {fileID: 2437193528566635163}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6594817195103992215
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2165660100848843614}
  m_Layer: 0
  m_Name: Bone045
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2165660100848843614
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6594817195103992215}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000002710048, y: -0.000000011017356, z: -0.0054617506, w: 0.9999851}
  m_LocalPosition: {x: -0.18927117, y: -0.0000000667572, z: 0}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2002611272420700795}
  m_Father: {fileID: 7076728598441496716}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6682267442521473931
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4211410193544012903}
  m_Layer: 0
  m_Name: Bone013
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4211410193544012903
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6682267442521473931}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -1.0944047, y: 0.000000076293944, z: 0.00000008937479}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6452209518611813383}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6738707504245730307
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4807960841191368761}
  m_Layer: 0
  m_Name: Bone016
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4807960841191368761
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6738707504245730307}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07677077, y: -0.7029267, z: 0.7035281, w: -0.07105393}
  m_LocalPosition: {x: -0.4356134, y: 0.8078592, z: 1.2069918}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6387797265431783506}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6923028884607599533
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6531297925353255350}
  m_Layer: 0
  m_Name: Bone057
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6531297925353255350
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6923028884607599533}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000045668166, y: -4.57632e-11, z: -0.0034931714, w: 0.9999939}
  m_LocalPosition: {x: -0.2724931, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3354305740681447338}
  m_Father: {fileID: 8489983714968659993}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6978428831108648231
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4159983573126065927}
  m_Layer: 0
  m_Name: Bone035
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4159983573126065927
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6978428831108648231}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000009907319, y: 0.000000013977096, z: -0.102078706, w: 0.9947763}
  m_LocalPosition: {x: -0.17897254, y: 0.00000015258789, z: 0.000000076293944}
  m_LocalScale: {x: 0.9999998, y: 0.9999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4839782231939942585}
  m_Father: {fileID: 3399331136403743229}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7119460575079314134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 699705377645734382}
  m_Layer: 0
  m_Name: Point018
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &699705377645734382
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7119460575079314134}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0.86340207, y: 0.725775, z: -0.083771974}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8576494166361558358}
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7227086359401769419
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1961141491960768813}
  m_Layer: 0
  m_Name: Bone079
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1961141491960768813
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7227086359401769419}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000006980272, y: -0.000000025044896, z: -0.26894888, w: 0.96315444}
  m_LocalPosition: {x: -0.17542678, y: -0.00000015258789, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4406230230589986052}
  m_Father: {fileID: 5229744173440642654}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7227815581751107145
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4406230230589986052}
  m_Layer: 0
  m_Name: Bone080
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4406230230589986052
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7227815581751107145}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000005940506, y: -0.0000004258569, z: -0.021851411, w: 0.9997612}
  m_LocalPosition: {x: -0.20416488, y: 0.00000045776366, z: 0}
  m_LocalScale: {x: 1.0000005, y: 1.0000005, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6738986016492519890}
  m_Father: {fileID: 1961141491960768813}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7370323065599085620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2247836658590412954}
  m_Layer: 0
  m_Name: Point012
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2247836658590412954
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7370323065599085620}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.13801445, z: 0.08474571}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4982151518254986614}
  m_Father: {fileID: 8130257411792057151}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7421437996159001788
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8489983714968659993}
  m_Layer: 0
  m_Name: Bone056
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8489983714968659993
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7421437996159001788}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 4.32978e-17, z: 0.7071067, w: -4.3297806e-17}
  m_LocalPosition: {x: -0.86340207, y: 0.725775, z: -0.08377185}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6531297925353255350}
  m_Father: {fileID: 2657182712835043920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7579612379138825639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5229744173440642654}
  m_Layer: 0
  m_Name: Bone078
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5229744173440642654
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7579612379138825639}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000007144313, y: 0.00000016950084, z: -0.25943267, w: 0.9657612}
  m_LocalPosition: {x: -0.16390136, y: 0.00000015258789, z: -0.000000076293944}
  m_LocalScale: {x: 0.9999998, y: 0.9999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1961141491960768813}
  m_Father: {fileID: 2508903308937247219}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7608037438807727051
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2069920234053543253}
  m_Layer: 0
  m_Name: Point008
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2069920234053543253
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7608037438807727051}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.45762697, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2764354375131656507}
  m_Father: {fileID: 8981947964279206327}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7649750316065969296
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8981947964279206327}
  m_Layer: 0
  m_Name: Point007
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8981947964279206327
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7649750316065969296}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.43825656, z: -0.036319617}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2069920234053543253}
  m_Father: {fileID: 1846618809913684022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7828033959176518675
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6387797265431783506}
  m_Layer: 0
  m_Name: Bone001
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6387797265431783506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7828033959176518675}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49796337, y: 0.50202817, z: -0.49796373, w: 0.5020282}
  m_LocalPosition: {x: -0.0000000050023576, y: 0.70346767, z: -0.11444069}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5680296388209611144}
  - {fileID: 4751751967963374554}
  - {fileID: 312063711159215611}
  - {fileID: 4807960841191368761}
  - {fileID: 2390467810327671602}
  - {fileID: 3706310414627823443}
  - {fileID: 6665375934158185116}
  - {fileID: 2145725793344461915}
  - {fileID: 8625279316205514047}
  - {fileID: 3143405146758524699}
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7868546580027762895
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1036507165499587953}
  m_Layer: 0
  m_Name: Point005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1036507165499587953
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7868546580027762895}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.012106475, z: -0.25665855}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1846618809913684022}
  m_Father: {fileID: 701370392982265923}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7893409825145229024
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8775788777013853163}
  m_Layer: 0
  m_Name: Bone063
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8775788777013853163
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7893409825145229024}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000039561616, y: 0.0000001349044, z: -0.4638111, w: 0.8859341}
  m_LocalPosition: {x: -0.22233821, y: -0.000000076293944, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5881751480378482290}
  m_Father: {fileID: 7738459325810178366}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7996007939894190104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1414921479670630306}
  - component: {fileID: 487799147791317533}
  - component: {fileID: 616230424632768362}
  m_Layer: 0
  m_Name: 18_tank_lvdai
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1414921479670630306
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7996007939894190104}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2657182712835043920}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &487799147791317533
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7996007939894190104}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c417fe1478fd1e14d8f731ab0534f8c3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -8482888588335292178, guid: 6631e1e6cc1a3324981e4fb6a7046fa0, type: 3}
  m_Bones:
  - {fileID: 9210078939962559239}
  - {fileID: 2512500308376457675}
  - {fileID: 2367221453285746225}
  - {fileID: 690888383768825406}
  - {fileID: 2479133766317680866}
  - {fileID: 2196056778676711363}
  - {fileID: 5513915628710821992}
  - {fileID: 7230457030015408012}
  - {fileID: 6494068722993973984}
  - {fileID: 7547127614180488004}
  - {fileID: 7434898894375035844}
  - {fileID: 2867754438782892110}
  - {fileID: 482062234377448531}
  - {fileID: 1788703128430804623}
  - {fileID: 2002611272420700795}
  - {fileID: 2165660100848843614}
  - {fileID: 7076728598441496716}
  - {fileID: 6226028342133701073}
  - {fileID: 6492204131477246884}
  - {fileID: 3959472543585617778}
  - {fileID: 4701160260506119589}
  - {fileID: 6434590039845881931}
  - {fileID: 7567211234118238366}
  - {fileID: 5337867796415803713}
  - {fileID: 4839782231939942585}
  - {fileID: 4159983573126065927}
  - {fileID: 3399331136403743229}
  - {fileID: ************6720480}
  - {fileID: 117329463648930968}
  - {fileID: 6278041516205912796}
  - {fileID: 5229744173440642654}
  - {fileID: 1961141491960768813}
  - {fileID: 4406230230589986052}
  - {fileID: 6738986016492519890}
  - {fileID: 6531297925353255350}
  - {fileID: 3354305740681447338}
  - {fileID: 7299950854097443828}
  - {fileID: 2686750240841922685}
  - {fileID: 8489983714968659993}
  - {fileID: 6973866669050784019}
  - {fileID: 8949682601454119820}
  - {fileID: 6276296988369577089}
  - {fileID: 2508903308937247219}
  - {fileID: 6412785885622267996}
  - {fileID: 8280227341744631156}
  - {fileID: 4387247167726070047}
  - {fileID: 7260478392015209847}
  - {fileID: 4944984587569294365}
  - {fileID: 5608545210855762436}
  - {fileID: 5360523932349507576}
  - {fileID: 141970402161661300}
  - {fileID: 7670751876844018325}
  - {fileID: 6038473392040392976}
  - {fileID: 5881751480378482290}
  - {fileID: 1321140216081128432}
  - {fileID: 7738459325810178366}
  - {fileID: 8775788777013853163}
  - {fileID: 2157427781147224817}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 8489983714968659993}
  m_AABB:
    m_Center: {x: 0.1114589, y: 0.3070734, z: 0.8635143}
    m_Extent: {x: 1.4744953, y: 0.42624038, z: 1.0952907}
  m_DirtyAABB: 0
--- !u!114 &616230424632768362
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7996007939894190104}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: eaeffb5043afebc49a5ecf62000f3569, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mainLightColor: {r: 4.539984, g: 4.539984, b: 4.539984, a: 1}
  mainLightRotation: {x: 35.7, y: 59.7, z: 36.6}
  auxiliaryLightColor: {r: 3.4005368, g: 2.668288, b: 0.81612885, a: 1}
  auxiliaryLightRotation: {x: 174.1, y: 70.1, z: -17.2}
  custommaterials:
  - {fileID: 2100000, guid: c417fe1478fd1e14d8f731ab0534f8c3, type: 2}
--- !u!1 &8122634591378641392
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5800409760207313197}
  m_Layer: 0
  m_Name: Point003
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5800409760207313197
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8122634591378641392}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.3922516, z: -0.016949158}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 701370392982265923}
  m_Father: {fileID: 5170275441645835519}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8279042881554286882
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7738459325810178366}
  m_Layer: 0
  m_Name: Bone062
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7738459325810178366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8279042881554286882}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000001757776, y: 0.00000028078725, z: -0.47558764, w: 0.87966835}
  m_LocalPosition: {x: -0.13552932, y: 0, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8775788777013853163}
  m_Father: {fileID: 2157427781147224817}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8310053091386657253
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6973866669050784019}
  m_Layer: 0
  m_Name: Bone082
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6973866669050784019
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8310053091386657253}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000000031421983, y: -0.00000016958414, z: -0.0000011101362, w: 1}
  m_LocalPosition: {x: -0.22494434, y: 0.0000003814697, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8949682601454119820}
  m_Father: {fileID: 6738986016492519890}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8342040941996908819
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5768583217489375511}
  m_Layer: 0
  m_Name: Point026
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5768583217489375511
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8342040941996908819}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.000000076293944, y: -0.4479418, z: -0.0024212836}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1088424011585138786}
  m_Father: {fileID: 578212868402431671}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8413084182655514980
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 618584448507422544}
  m_Layer: 0
  m_Name: Point015
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &618584448507422544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8413084182655514980}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.13801445, z: 0.1162228}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6267376918551394481}
  m_Father: {fileID: 4025223835197656328}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8449470309156786529
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3365759111935735347}
  m_Layer: 0
  m_Name: Bone025
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3365759111935735347
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8449470309156786529}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016234608, y: -5.676397e-10, z: 0.9999939, w: 0.0034964574}
  m_LocalPosition: {x: 0.7035619, y: 0.4251062, z: -0.46658453}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8462853108280379063
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2367221453285746225}
  m_Layer: 0
  m_Name: Bone051
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2367221453285746225
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8462853108280379063}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000004891218, y: 0.00000017469807, z: -0.021851538, w: 0.9997612}
  m_LocalPosition: {x: -0.20416488, y: 0.0000003814697, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000005, y: 1.0000005, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 690888383768825406}
  m_Father: {fileID: 9210078939962559239}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8509003983796103024
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5558857554925225261}
  m_Layer: 0
  m_Name: Bone026
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5558857554925225261
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8509003983796103024}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000016234608, y: -5.676397e-10, z: 0.9999939, w: 0.0034964574}
  m_LocalPosition: {x: 0.7035619, y: 0.42510617, z: -0.9319919}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099620446284344478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8871857830293705445
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 482062234377448531}
  m_Layer: 0
  m_Name: Bone048
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &482062234377448531
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8871857830293705445}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000004453244, y: -0.0000007384032, z: -0.38248017, w: 0.9239637}
  m_LocalPosition: {x: -0.14955406, y: 0.00000015258789, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2512500308376457675}
  m_Father: {fileID: 1788703128430804623}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8897089787266456382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8576494166361558358}
  m_Layer: 0
  m_Name: Point019
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8576494166361558358
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8897089787266456382}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.000000076293944, y: 0.45520577, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1651586556973787343}
  m_Father: {fileID: 699705377645734382}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8974758804016093687
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6492204131477246884}
  m_Layer: 0
  m_Name: Bone042
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6492204131477246884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8974758804016093687}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000011064302, y: 0.00000018509725, z: -0.013728712, w: 0.99990577}
  m_LocalPosition: {x: -0.20572378, y: 0.000000028610229, z: 0.00000015258789}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6226028342133701073}
  m_Father: {fileID: 3959472543585617778}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9113564808386851190
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: ************6720480}
  m_Layer: 0
  m_Name: Bone033
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &************6720480
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9113564808386851190}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000020677949, y: 0.00000093504343, z: -0.47558787, w: 0.87966824}
  m_LocalPosition: {x: -0.13552932, y: -0.000000076293944, z: 0.00000015258789}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3399331136403743229}
  m_Father: {fileID: 117329463648930968}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9135781297289157645
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5223454582821257491}
  m_Layer: 0
  m_Name: Point029
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5223454582821257491
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9135781297289157645}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.13801445, z: 0.08474571}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2489395058038500550}
  m_Father: {fileID: 8656278834150765178}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9143738760072163224
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4514887143990050006}
  m_Layer: 0
  m_Name: Bone005
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4514887143990050006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9143738760072163224}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0.006241043, w: 0.99998057}
  m_LocalPosition: {x: -0.18804413, y: 0.42914, z: 0.81870764}
  m_LocalScale: {x: 1.0000004, y: 1.0000005, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1193475051202865780}
  m_Father: {fileID: 3143405146758524699}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9160946224559852124
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8586719932821099905}
  m_Layer: 0
  m_Name: Point022
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8586719932821099905
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9160946224559852124}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.012106475, z: -0.25665855}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2948903240106704414}
  m_Father: {fileID: 4141042780456311366}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
