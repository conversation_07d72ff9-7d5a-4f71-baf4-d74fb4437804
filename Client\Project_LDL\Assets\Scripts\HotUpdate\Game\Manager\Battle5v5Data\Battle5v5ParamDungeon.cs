using System.Collections.Generic;
using Battle;
using Game.Hotfix.Config;
using JetBrains.Annotations;
using UnityEngine;

namespace Game.Hotfix
{
    public class Battle5v5ParamDungeon : Battle5v5ParamBase
    {
        public int DungeonId => m_DungeonCfg.id;
        public dungeon DungeonCfg=>m_DungeonCfg;
        public PvePathModule PvePathModule => m_pvePathModule;
        public long MonsterGroupTotalPower => m_MonsterGroup?.total_power ?? 0;
        
        public List<TeamHero> Heros =>m_TeamHeros;
        [CanBeNull] public Report Report => m_Report;
        
        private dungeon m_DungeonCfg;
        private PvePathModule m_pvePathModule;
        private List<TeamHero> m_TeamHeros;
        [CanBeNull] private Battle.Report m_Report;

        private monster_group m_MonsterGroup;
        
        public Battle5v5ParamDungeon(dungeon dungeonCfg,PvePathModule pvePathModule)
        {
            m_DungeonCfg = dungeonCfg;
            m_pvePathModule = pvePathModule;
                
            m_TeamHeros = new List<TeamHero>();
            
            //初始化 英雄
            m_MonsterGroup = GameEntry.LDLTable.GetTableById<monster_group>(dungeonCfg.monster_group);
            if (m_MonsterGroup != null)
            {
                Dictionary<EnumBattlePos,int> heros = new Dictionary<EnumBattlePos,int>()
                {
                    {EnumBattlePos.PosR1,m_MonsterGroup.monster_1},
                    {EnumBattlePos.PosR2,m_MonsterGroup.monster_2},
                    {EnumBattlePos.PosR3,m_MonsterGroup.monster_3},
                    {EnumBattlePos.PosR4,m_MonsterGroup.monster_4},
                    {EnumBattlePos.PosR5,m_MonsterGroup.monster_5},
                };
                foreach (var item in heros)
                {
                    var monsterId = item.Value;
                    if(monsterId<=0)
                        continue;
                    monster_config monsterConfig = GameEntry.LDLTable.GetTableById<monster_config>(monsterId);
                    if (monsterConfig == null)
                        continue;
                    
                    var pos = item.Key;
                    
                    Battle.TeamHero teamHero = new TeamHero();
                    teamHero.Code = (PbGameconfig.itemid)monsterConfig.hero_id;
                    teamHero.Pos = (int)pos;
                    teamHero.Level = (uint)monsterConfig.monster_level;
                    teamHero.StarStage = (uint)monsterConfig.monster_star;
                    m_TeamHeros.Add(teamHero);
                    
                }
            }
            
        }

        public void SetRecord(Battle.Report report)
        {
            m_Report = report;
        }

        public override bool IsFinish()
        {
            return m_Report != null;
        }

        public bool IsWin()
        {
            if (Report != null)
            {
                return Report.Result == BattleResult.AttackerWin;
            }

            return false;
        }        
    }
}
