using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;
using GameFramework.Event;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UITradeTrainBattlePlanForm : UGuiFormEx
    {
        readonly List<Team.TeamType> teamTypeAttack = new()
        {
            Team.TeamType.TradeTrainAttack1,
            Team.TeamType.TradeTrainAttack2,
            Team.TeamType.TradeTrainAttack3,
        };

        readonly List<Team.TeamType> teamTypeDefend = new()
        {
            Team.TeamType.TradeTrainDefend1,
            Team.TeamType.TradeTrainDefend2,
            Team.TeamType.TradeTrainDefend3,
        };

        readonly List<Vector2> heroPos = new()
        {
            new(-128f, 74f),
            new(128, 74f),
            new(-128, -55f),
            new(0, -55f),
            new(128, -55f),
        };

        Trade.TradeCargoTransport trainData;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            if (userData is Trade.TradeCargoTransport data)
            {
                trainData = data;
            }

            RefreshPanel();
            GameEntry.Event.Subscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            GameEntry.Event.Unsubscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        void OnTeamChange(object sender, GameEventArgs e)
        {
            if (e is TeamChangeEventArgs teamChangeArgs)
            {

            }

            RefreshTeam(m_transTeamLeft);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnFightClick()
        {
            if (trainData == null)
            {
                ColorLog.Red("火车数据为空");
                return;
            }

            List<Team.TeamType> attack = new();
            List<Team.TeamType> defend = new();

            foreach (var item in teamTypeAttack)
            {
                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(item);
                if (teamData != null && teamData.Count > 0)
                {
                    attack.Add(item);
                }
            }

            GameEntry.TradeTruckData.RequestTrainStartFight(trainData.Id, attack, defend, (result) =>
            {
                ColorLog.Pink("火车开始战斗", result);
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTrainBattleResultForm, trainData);
            });
        }

        private void OnBtnExchange1Click()
        {

        }

        private void OnBtnExchange2Click()
        {

        }

        void InitPanel()
        {

        }

        void RefreshPanel()
        {
            RefreshPlayer(m_transPlayerLeft);
            RefreshPlayer(m_transPlayerRight);
            RefreshTeam(m_transTeamLeft);
            RefreshTeam(m_transTeamRight);
        }

        void RefreshPlayer(Transform parent)
        {
            UIText txtName = parent.Find("txtName").GetComponent<UIText>();
            UIText txtPower = parent.Find("txtPower").GetComponent<UIText>();

            ulong roleID = 0;
            if (parent == m_transPlayerLeft)
            {
                roleID = GameEntry.RoleData.RoleID;
            }
            else if (parent == m_transPlayerRight && trainData != null)
            {
                roleID = trainData.RoleId;
            }

            if (roleID != 0)
            {
                GameEntry.RoleData.RequestRoleQueryLocalSingle(roleID, (roleBrief) =>
                {
                    ColorLog.Pink("查询玩家信息", roleBrief);
                    if (roleBrief != null)
                    {
                        txtName.text = roleBrief.Name;
                        txtPower.text = ToolScriptExtend.FormatNumberWithUnit(roleBrief.Power);
                    }
                });
            }
        }

        void RefreshTeam(Transform parent)
        {
            foreach (Transform teamParent in parent)
            {
                int index = int.Parse(teamParent.gameObject.name.Substring(teamParent.gameObject.name.Length - 1, 1));
                Team.TeamType teamType = Team.TeamType.TradeTrainAttack1;
                if (parent == m_transTeamLeft)
                {
                    teamType = teamTypeAttack[index - 1];
                }
                else if (parent == m_transTeamRight)
                {
                    teamType = teamTypeDefend[index - 1];
                }

                UIButton btnBg = teamParent.Find("bg").GetComponent<UIButton>();
                UIImage bg = teamParent.Find("bg").GetComponent<UIImage>();
                UIText txtNumber = teamParent.Find("txtNumber").GetComponent<UIText>();
                UIText txtPower = teamParent.Find("txtPower").GetComponent<UIText>();
                Transform heroParent = teamParent.Find("hero");

                List<Fight.FormationHero> teamData = GameEntry.LogicData.TeamData.GetTeam(teamType);

                txtPower.gameObject.SetActive(false);
                txtPower.text = string.Empty;

                if (teamData != null)
                {
                    ulong powerTotal = 0;
                    foreach (var data in teamData)
                    {
                        var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)data.HeroId);
                        powerTotal += heroVo.power;
                    }
                    txtPower.text = ToolScriptExtend.FormatNumberWithUnit(powerTotal);
                    txtPower.gameObject.SetActive(powerTotal > 0);
                }

                for (int i = 0; i < 5; i++)
                {
                    Transform transHeroItem;
                    if (i < heroParent.childCount)
                    {
                        transHeroItem = heroParent.GetChild(i);
                    }
                    else
                    {
                        transHeroItem = Instantiate(m_transHeroItem, heroParent);
                    }
                    RectTransform rect = transHeroItem.GetComponent<RectTransform>();
                    rect.anchoredPosition = heroPos[i];
                    transHeroItem.gameObject.SetActive(true);
                    GameObject empty = transHeroItem.Find("empty").gameObject;
                    UIHeroItem item = transHeroItem.Find("UIHeroItem").GetComponent<UIHeroItem>();
                    if (teamData != null)
                    {
                        Fight.FormationHero heroData = null;
                        foreach (var hero in teamData)
                        {
                            if (hero.Pos == i + 1)
                            {
                                heroData = hero;
                            }
                        }

                        if (heroData != null)
                        {
                            HeroModule heroModule = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroData.HeroId);
                            item.Refresh(heroModule);
                            item.gameObject.SetActive(true);
                            empty.SetActive(false);
                        }
                        else
                        {
                            item.gameObject.SetActive(false);
                            empty.SetActive(true);
                        }
                    }
                    else
                    {
                        item.gameObject.SetActive(false);
                        empty.SetActive(true);
                    }
                }

                btnBg.onClick.RemoveAllListeners();
                btnBg.onClick.AddListener(()=>
                {
                    if (parent == m_transTeamRight) return;
                    UITeamFormParam param = new()
                    {
                        Index = index - 1,
                        TeamFormType = UITeamFormType.TradeTrainAttack
                    };
                    GameEntry.UI.OpenUIForm(EnumUIForm.UITeamForm, param);
                });
            }
        }
    }
}
