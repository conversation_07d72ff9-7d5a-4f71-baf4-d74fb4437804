using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UITradeTruckPlunderForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnPlunder;
        [SerializeField] private UIButton m_btnTip;
        [SerializeField] private UIButton m_btnShare;
        [SerializeField] private UIButton m_btnCollect;
        [SerializeField] private UIButton m_btnTruckDetails;
        [SerializeField] private UIButton m_btnTeamPower;
        [SerializeField] private UIButton m_btnTrainDetails;

        [SerializeField] private UIText m_txtTitle;
        [SerializeField] private UIText m_txtTruckPlunderTodayCount;
        [SerializeField] private UIText m_txtNameTruck;
        [SerializeField] private UIText m_txtTruckPlunderCount;
        [SerializeField] private UIText m_txtTrainPlunderTodayCount;
        [SerializeField] private UIText m_txtTrainPlunderCount;
        [SerializeField] private UIText m_txtTruckDesc;

        [SerializeField] private UIImage m_imgQuality;

        [SerializeField] private RectTransform m_rectBg;
        [SerializeField] private GameObject m_goTruckItem;
        [SerializeField] private GameObject m_goTrainItem;
        [SerializeField] private GameObject m_goPlunder;
        [SerializeField] private GameObject m_goTipDesc;
        [SerializeField] private GameObject m_goTipBack;
        [SerializeField] private GameObject m_goTruckInfo;
        [SerializeField] private Transform m_transHeroItem;
        [SerializeField] private Transform m_transContentHeroTruck;
        [SerializeField] private Transform m_transContentRewardTruck;
        [SerializeField] private Transform m_transRewardTruck;
        [SerializeField] private GameObject m_goTrainInfo;
        [SerializeField] private Transform m_transContentRewardTrain;
        [SerializeField] private GameObject m_goTruckDesc;
        [SerializeField] private GameObject m_goTrainDesc;

        void InitBind()
        {
            m_btnPlunder.onClick.AddListener(OnBtnPlunderClick);
            m_btnTip.onClick.AddListener(OnBtnTipClick);
            m_btnShare.onClick.AddListener(OnBtnShareClick);
            m_btnCollect.onClick.AddListener(OnBtnCollectClick);
            m_btnTruckDetails.onClick.AddListener(OnBtnTruckDetailsClick);
            m_btnTeamPower.onClick.AddListener(OnBtnTeamPowerClick);
            m_btnTrainDetails.onClick.AddListener(OnBtnTrainDetailsClick);
        }
    }
}
