using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Game.Hotfix.Config;
using Mosframe;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIBuildingWagonForm : UGuiFormEx
    {
        private int maxId;
        private long maxTime;
        private long curTime;
        private float deltaTime = 0;
        private float moveSpeed = 150;
        private UIItemModule itemModule;
        private int minBoxId;
        private List<int> getList = new();
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();

            itemModule = m_btnTarget.transform.Find("itemObj").GetComponent<UIItemModule>();

            var list = GameEntry.LDLTable.GetTable<dungeon>();
            maxId = list.Count;
            maxTime = (long)GameEntry.LogicData.DungeonData.GetAccumulatedTime();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            GameEntry.LogicData.DungeonData.DungeonLoad((resp) =>
            {
                minBoxId = resp.CliamedBoxMin;
                getList = resp.ClaimedBox.ToList();
                OnUpdateInfo();
            });
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            var showType = (int)userData;
            if (showType == 0) { OnUpdateTraget(); }
            else if (showType == 1) { OnUpdateReward(); }
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            if (curTime < maxTime)
            {
                deltaTime += elapseSeconds;
                if (deltaTime >= 1)
                {
                    deltaTime = 0;

                    curTime++;
                    m_imgProgress.fillAmount = (float)curTime / maxTime;
                }
            }

            var posY = m_rectBg.anchoredPosition.y - elapseSeconds * moveSpeed;
            if (posY <= -2700) posY = 0;
            m_rectBg.anchoredPosition = new Vector2(0, posY);
        }

        private void OnUpdateInfo()
        {
            var curId = GameEntry.LogicData.DungeonData.CurDungeonId;
            var count = m_transLevel.childCount;
            var minId = curId == maxId ? curId - 3 : curId - 2;
            var lastShowIndex = 0;
            for (int i = 0; i < count; i++)
            {
                var id = minId + i;
                var item = m_transLevel.GetChild(i);
                var itemDark = m_transDark.GetChild(i);
                var isShow = id <= maxId;
                if (isShow)
                {
                    var levelTxt = item.Find("levelTxt").GetComponent<UIText>();
                    var bossSp = item.Find("bossSp").gameObject;

                    levelTxt.text = id == maxId ? "Max" : id + "";

                    var config = GameEntry.LDLTable.GetTableById<dungeon>(id);
                    var isBoss = config?.dungeon_type == 1;
                    bossSp.SetActive(i == 3 && isBoss);

                    lastShowIndex = i;
                }
                item.gameObject.SetActive(isShow);
                itemDark.gameObject.SetActive(isShow);
            }

            var offset = maxId - curId;
            if (offset > 3) { m_rectProgressBg.sizeDelta = new Vector2(1080, 25); }
            else
            {
                var item = m_transLevel.GetChild(lastShowIndex);
                m_rectProgressBg.sizeDelta = new Vector2(540 + item.localPosition.x, 25);
            }

            OnUpdateTraget();
            OnUpdateReward();
        }

        private void OnUpdateTraget()
        {
            var curId = GameEntry.LogicData.DungeonData.CurDungeonId;
            var list = GameEntry.LDLTable.GetTable<dungeon_reward>();
            var count = list.Count;
            var nextIndex = -1;
            for (int i = 0; i < count; i++)
            {
                if (list[i].dungeon_id > curId)
                {
                    nextIndex = i + 1;
                    break;
                }
            }
            nextIndex = nextIndex != -1 ? nextIndex : count;

            var rewardInfo = list[nextIndex - 1];
            var itemInfo = rewardInfo.reward[0];
            itemModule.SetData(itemInfo.item_id, itemInfo.num);
            itemModule.DisplayInfo();
            itemModule.SetScale(0.58f);

            if (rewardInfo.dungeon_id > curId)
            {
                var num = rewardInfo.dungeon_id - curId;
                m_txtTarget.text = ToolScriptExtend.GetLangFormat(1100459, num + "");
            }
            else
            {
                if (nextIndex == minBoxId) { m_txtTarget.text = ToolScriptExtend.GetLang(10048); }
                else { m_txtTarget.text = ToolScriptExtend.GetLang(1100414); }
            }

            var hasReward = nextIndex - minBoxId - 1 > getList.Count;
            m_goTargetRed.SetActive(hasReward);
        }

        private void OnUpdateReward()
        {
            var timeAt = GameEntry.LogicData.DungeonData.AccumulatedRewardAt;
            curTime = timeAt > 0 ? (long)TimeComponent.Now - timeAt : 0;
            curTime = curTime < maxTime ? curTime : maxTime;
            m_imgProgress.fillAmount = (float)curTime / maxTime;
        }

        private void OnBtnTipClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIComDescForm, 19);
        }

        private void OnBtnRankClick()
        {
            // 打开排行榜
        }

        private void OnBtnTargetClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingWagonTragetForm, (minBoxId, getList));
        }

        private void OnBtnExitClick()
        {
            Close();
        }

        private void OnBtnExploreClick()
        {
            // 探索
            if (GameEntry.LogicData.DungeonData.CurDungeonId >= maxId)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100509)
                });
                return;
            }
        }

        private void OnBtnAutoClick()
        {
            // 自动挑战
            if (GameEntry.LogicData.DungeonData.CurDungeonId >= maxId)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                {
                    Content = ToolScriptExtend.GetLang(1100509)
                });
                return;
            }
        }

        private void OnBtnRewardClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingWagonRewardForm);
        }
    }
}