using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIRecruitForm : UGuiFormEx
    {
        class TabNode
        {
            public int index;
            public GameObject offBg;
            public GameObject onBg;
            public UIText txt1;
            public UIText txt2;
            public UIImage icon;
            public UIButton btn;
            public UnityAction callback;
            public UIButton mask;
            public GameObject redDot;
        }

        class PreviewNode
        {
            public GameObject rootNode;
            public GameObject offNode;
            public GameObject onNode;
        }

        private int _selectIndex; //选中的标签页索引
        private Transform tabRoot; //标签组的父节点
        private int recruitId; //招募id

        private List<TabNode> _objList;
        private Dictionary<int, UnityAction> _callbackDic;

        private recruittype recruitType = recruittype.recruittype_hero;
        private RectTransform descBg;

        private List<recruit_drops> preHeroList; //英雄预览列表
        private List<PreviewNode> preNodeList; //英雄预览节点列表
        private int curPreIndex = 0;
        private int curPreTimerCount;//英雄预告倒计时
        private bool isTickPre; //是否检测预告倒计时
        
        private bool isTickFree; //是否检测免费倒计时
        private int freeCountDown = 0;

        private StringBuilder sb;

        //"下次免费"
        private string TimerStr1;

        private bool IsShowHundred;

        private Dictionary<recruittype, int> giftDic;//记录每个招募模块的礼包id
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
            _selectIndex = -1;
            InitBind();
            _objList = new List<TabNode>();
            preHeroList = new List<recruit_drops>();
            _callbackDic = new Dictionary<int, UnityAction>();
            preNodeList = new List<PreviewNode>();
            GameEntry.LogicData.RecruitData.SetTopDiamondView(m_goDiamond);
            descBg = m_goDescNode.transform.Find("Image_1").GetComponent<RectTransform>();
            BindTrigger();
            TimerStr1 = ToolScriptExtend.GetLang(1100105);
            sb = new StringBuilder();
            m_togSkip.onValueChanged.AddListener(isOn =>
            {
                var curValue = isOn ? 1 : 0;
                var lastValue = PlayerPrefs.GetInt("HundredRecruitSkip",0);
                if (curValue != lastValue)
                {
                    PlayerPrefs.SetInt("HundredRecruitSkip",curValue);
                }
            });
            
            var txt = m_btnOneFree.transform.Find("Text").GetComponent<UIText>();
            //招募{0}次
            var str = ToolScriptExtend.GetLang(1100101);
            txt.text = string.Format(str,1);
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);

            var targetType = recruittype.recruittype_hero;
            if (userData is recruittype result)
            {
                targetType = result;
            }

            var targetIndex = 0;
            switch(targetType)
            {
                case recruittype.recruittype_hero:
                    targetIndex = 0;
                    break;
                case recruittype.recruittype_survivor:
                    targetIndex = 1;
                    break;
                case recruittype.recruittype_season:
                    //TODO
                    break;
                default:
                    break;
            }


            IsShowHundred = false;
            _objList.Clear();
            preNodeList.Clear();
            _callbackDic.Clear();
            preNodeList.Clear();
            m_goDescCheck.SetActive(false);
            m_btnTen.gameObject.SetActive(true);
            m_btnHundred.gameObject.SetActive(false);
            m_togSkip.gameObject.SetActive(false);
            if (giftDic == null)
            {
                giftDic = new Dictionary<recruittype, int>();
            }
            else
            {
                giftDic.Clear();
            }
           
            curPreIndex = 0;
            _selectIndex = -1;
            tabRoot = m_goContent.transform;
            
            InitCount(2);
            // BindSelectCallback(0, "传奇再临", "jianzhu_jiuguan_yeqian_image1.png", () =>
            // {
            //     recruitType = RecruitType.Season;
            //     recruitId = GameEntry.LogicData.RecruitData.RecruitId_Season;
            //
            // });
            
            //英雄招募
            BindSelectCallback(0, ToolScriptExtend.GetLang(1100074), "jianzhu_jiuguan_yeqian_image1.png", () =>
            {
                recruitType = recruittype.recruittype_hero;
                recruitId = GameEntry.LogicData.RecruitData.RecruitId_Hero;
                CheckGiftInfo();
            });
            //幸存者招募
            BindSelectCallback(1, ToolScriptExtend.GetLang(210201), "zhaomu_tongyong_dilang_qieye2_image1.png", () =>
            {
                recruitType = recruittype.recruittype_survivor;
                recruitId = GameEntry.LogicData.RecruitData.RecruitId_Survivor;
                CheckGiftInfo();
            });

            Timers.Instance.Add("UIRecruitForm", 1, (a) =>
            {
                //免费招募CD更新逻辑
                if (isTickFree)
                {
                    if (freeCountDown > 0)
                    {
                        sb.Clear();
                        freeCountDown--;
                        sb.Append(TimerStr1);
                        var time = GameEntry.LogicData.RecruitData.FormatTime(freeCountDown);
                        sb.Append(time);
                        m_txtFreeTimer.text = sb.ToString();
                    }
                    else
                    {
                        isTickFree = false;
                        m_txtFreeTimer.gameObject.SetActive(false);
                        m_btnOneFree.gameObject.SetActive(true);
                        m_btnOne.gameObject.gameObject.SetActive(false);
                    }
                }
                
                //新英雄预告更新逻辑
                if (isTickPre)
                {
                    if (curPreTimerCount > 0)
                    {
                        sb.Clear();
                        curPreTimerCount--;
                        var time = GameEntry.LogicData.RecruitData.FormatTime(curPreTimerCount);
                        sb.Append(time);
                        m_txtHeroPre.text = sb.ToString();
                    }
                    else
                    {
                        isTickPre = false;
                        m_goPreview.SetActive(false);
                        ShowPreviewHero();
                    }
                }
                
            }, 86400);
            GameEntry.Event.Subscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Subscribe(RedPointEventArgs.EventId, OnRedDotChange);
            GameEntry.LogicData.RecruitData.C2SRecruitPoolsInfo();
            GameEntry.Event.Subscribe(PaymentFinishEventArgs.EventId, OnPaymentFinish);

            ShowHeroVertical();
            
            
            OnSelectLogic(targetIndex);
            
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            isTickPre = false;
            isTickFree = false;
            giftDic?.Clear();
            Timers.Instance.Remove("UIRecruitForm");
            GameEntry.Event.Unsubscribe(ItemChangeEventArgs.EventId, OnItemChange);
            GameEntry.Event.Unsubscribe(RedPointEventArgs.EventId, OnRedDotChange);
            GameEntry.Event.Unsubscribe(PaymentFinishEventArgs.EventId, OnPaymentFinish);
        }
        
        //充值购买刷新
        void OnPaymentFinish(object sender, GameFramework.Event.GameEventArgs e)
        {
            RequestNewGiftId();
        }
        
        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            var msgId = (int)userData;
            if (msgId == 1)
            {
                //心愿英雄更新
                CheckWishHeroView();
            }
            else if (msgId == 2)
            {
                //初始化协议返回更新
                S2CRefresh();
                GameEntry.LogicData.RecruitData.DirtyAllDot();
            }
            else if (msgId == 3)
            {
                //刷新（跳过动画勾选）
                var result = PlayerPrefs.GetInt("HundredRecruitSkip",0);
                m_togSkip.isOn = result == 1;
            }    
        }
        
        //道具数量更新
        void OnItemChange(object sender, GameEventArgs e)
        {
            if (e is ItemChangeEventArgs args)
            {
                
            }
            GameEntry.LogicData.RecruitData.SetTopDiamondView(m_goDiamond, true);
            GameEntry.LogicData.RecruitData.SetRecruitTicketView(recruitType, m_goTopItem, true);
            CheckHundredShow();
            GameEntry.LogicData.RecruitData.DirtyAllDot();
        }

        //红点事件响应
        void OnRedDotChange(object sender, GameEventArgs e)
        {
            _objList[0].redDot.SetActive(RedPointManager.Instance.IsRed(EnumRed.Recruit_Hero.ToString()));
            _objList[1].redDot.SetActive(RedPointManager.Instance.IsRed(EnumRed.Recruit_Survivor.ToString()));
        }
        
        private void OnBtnBackClick()
        {
            this.Close();
        }

        //初始化的后端数据返回，界面刷新
        private void S2CRefresh()
        {
            CheckTagUnlock();
            CheckWishHeroView();

            if (_selectIndex == -1)
            {
                _selectIndex = 0;
            }
            RefreshIndexView(_selectIndex);
            
            SetBaoDiData(m_btnHeroBaoDi, GameEntry.LogicData.RecruitData.RecruitId_Hero);
            // SetBaoDiData(m_btnLegendBaoDi);
            SetBaoDiData(m_btnSurvivalBaoDi, GameEntry.LogicData.RecruitData.RecruitId_Survivor);

            SetWishHeroProgress();
            ShowPreviewHero();
            CheckFree();
            CheckHundredShow();
        }

        #region 公共逻辑

        //单抽
        private void OnBtnOneClick()
        {
            OpenTargetForm(recruitnumtype.recruitnumtype_one);
        }

        //十抽
        private void OnBtnTenClick()
        {
            OpenTargetForm(recruitnumtype.recruitnumtype_ten);
        }

        //免费抽
        private void OnBtnOneFreeClick()
        {
            OpenTargetForm(recruitnumtype.recruitnumtype_free);
        }

        //百抽
        private void OnBtnHundredClick()
        {
            OpenTargetForm(recruitnumtype.recruitnumtype_hundred);
            
        }


        //掉落概率说明
        private void OnBtnHeroDropClick()
        {
            Game.GameEntry.UI.OpenUIForm(EnumUIForm.UIRecruitRatioForm,recruitId);
        }
        
        private void SetNodeActive(recruittype type)
        {
            m_goLegend.SetActive(type == recruittype.recruittype_season);
            m_goHero.SetActive(type == recruittype.recruittype_hero);
            m_goSurvival.SetActive(type == recruittype.recruittype_survivor);
        }

        private void OpenTargetForm(recruitnumtype numType)
        {
            if (recruitType == recruittype.recruittype_hero)
            {
                var manager = GameEntry.LogicData.RecruitData;
                var wishUnlock = manager.CheckWishUnlock();
                var isSelected = manager.HasSelectWishHero();
                if (wishUnlock)
                {
                    if (isSelected)
                    {
                        var selectHeroId = manager.GetWishHero();
                        var isOwn = GameEntry.LogicData.HeroData.IsHeroActive(selectHeroId);
                        if (isOwn)
                        {
                            var heroModule = GameEntry.LogicData.HeroData.GetHeroModule(selectHeroId);
                            var is5Star = heroModule.StarNum >=5;
                            if (is5Star)
                            {
                                //该英雄已达5星
                                GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
                                {
                                    Content = ToolScriptExtend.GetLang(1100373),//"心愿英雄英雄已达5星，建议更换其他英雄，是否确认招募"
                                    ConfirmText = ToolScriptExtend.GetLang(1100144),//"确定"
                                    CancelText = ToolScriptExtend.GetLang(1100143),//"取消"
                                    OnClickConfirm = (a) => { JumpTargetForm(numType); },
                                });
                                return;
                            }
                        }
                    }
                    else
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UICommonConfirmForm, new DialogParams
                        {
                            Content = ToolScriptExtend.GetLang(1100103),//"尚未选择心愿英雄，是否确认招募？"
                            ConfirmText = ToolScriptExtend.GetLang(1100144),//"确定"
                            CancelText = ToolScriptExtend.GetLang(1100143),//"取消"
                            OnClickConfirm = (a) => { JumpTargetForm(numType); },
                        });
                        return;
                    }
                }
            }

            JumpTargetForm(numType);
        }

        private void JumpTargetForm(recruitnumtype numType)
        {
            var itemId = GameEntry.LogicData.RecruitData.GetIconIdByType(recruitType);
            var ownCount = GameEntry.LogicData.BagData.GetAmountById(itemId);
            var needCount = (int)numType;
            if (ownCount < needCount)
            {
                //道具不够，打开道具获取途径界面
                ItemModule itemModule = new(itemId);
                GameEntry.LogicData.BagData.ResourceGetWay(itemModule.SetGetWayCount(needCount));
                return;
            }
            var dialog = numType == recruitnumtype.recruitnumtype_hundred ? EnumUIForm.UIRecruitHundredForm : EnumUIForm.UIRecruitAnimForm;
            GameEntry.LogicData.RecruitData.C2SRecruitReq(recruitId, numType, (msg) =>
            {
                if (msg.List.Count <= 0) return;
                var result = new List<Recruit.RecruitReward>();
                result.AddRange(msg.List);
                Game.GameEntry.UI.OpenUIForm(dialog, new RecruitParams.RecruitResult
                    {
                        recruitId = recruitId,
                        numType = numType,
                        recruitType = recruitType,
                        rewardList = result
                    });
            });
        }

        //查看保底详情描述界面
        private void OpenCheckDesc(Vector3 pos, string str)
        {
            m_goDescCheck.SetActive(true);
            m_txtDesc.text = str;
            m_goDescNode.transform.position = pos;
            LayoutRebuilder.ForceRebuildLayoutImmediate(descBg);
        }

        //关闭保底详情描述界面
        private void OnBtnDescCloseClick()
        {
            m_goDescCheck.SetActive(false);
        }

        //切换百抽和十抽
        private void OnBtnSwitchClick()
        {
            var tenObj = m_btnTen.gameObject;
            var hundredObj = m_btnHundred.gameObject;
            tenObj.SetActive(!tenObj.activeInHierarchy);
            hundredObj.SetActive(!hundredObj.activeInHierarchy);
            m_togSkip.gameObject.SetActive(hundredObj.activeInHierarchy);
            
            var result = PlayerPrefs.GetInt("HundredRecruitSkip",0);
            m_togSkip.isOn = result == 1;
            IsShowHundred = m_btnHundred.gameObject.activeInHierarchy;
        }

        //判断是否可以免费招募
        private void CheckFree()
        {
            var isCanFreeRecruit = GameEntry.LogicData.RecruitData.IsCanFree(recruitId);
            isTickFree = !isCanFreeRecruit;
            m_btnOneFree.gameObject.SetActive(isCanFreeRecruit);
            m_btnOne.gameObject.SetActive(!isCanFreeRecruit);
            m_txtFreeTimer.gameObject.SetActive(isTickFree);

            if (isCanFreeRecruit) return;
            freeCountDown = GameEntry.LogicData.RecruitData.GetFreeTimeInterval(recruitId);
            if (freeCountDown <= 0) return;
            sb.Clear();
            freeCountDown--;
            sb.Append(TimerStr1);
            var time = GameEntry.LogicData.RecruitData.FormatTime(freeCountDown);
            sb.Append(time);
            m_txtFreeTimer.text = sb.ToString();
        }

        private void ShowBaoDiTip(int id, int langId)
        {
            var count2 = GameEntry.LogicData.RecruitData.GetGuaranteeCount(id);
            var count1 = GameEntry.LogicData.RecruitData.GetGuardTimes(id);
            var value = count2 - count1;
            value = value < 0 ? 0 : value;
            var str = string.Format(ToolScriptExtend.GetLang(langId), value);
            OpenCheckDesc(m_btnHeroBaoDi.transform.position, str);
        }
        
        //判断是否显示百抽入口
        private void CheckHundredShow()
        {
            var itemId = GameEntry.LogicData.RecruitData.GetIconIdByType(recruitType);
            var ownCount = GameEntry.LogicData.BagData.GetAmountById(itemId);
            var showHundred = ownCount >= 100;
            m_btnSwitch.gameObject.SetActive(showHundred);
            if (showHundred)
            {
                if (IsShowHundred)
                {
                    if (m_btnTen.gameObject.activeInHierarchy)
                    {
                        m_btnTen.gameObject.SetActive(false);
                    }
                    m_btnHundred.gameObject.SetActive(true);
                    m_togSkip.gameObject.SetActive(true);
                }
                else
                {
                    m_btnTen.gameObject.SetActive(true);
                    m_btnHundred.gameObject.SetActive(false);
                    m_togSkip.gameObject.SetActive(false);
                }
            }
            else
            {
                m_btnHundred.gameObject.SetActive(false);
                m_btnTen.gameObject.SetActive(true);
                m_togSkip.gameObject.SetActive(false);
            }
        }
        #endregion

        
        #region 切页标签

        /// <summary>
        /// 初始化调用
        /// </summary>
        /// <param name="count">标签的数量</param>
        public void InitCount(int count)
        {
            _objList.Clear();

            ToolScriptExtend.ClearAllChild(tabRoot);
            for (var i = 0; i < count; i++)
            {
                var obj = Instantiate(m_goTagItem, tabRoot);
                obj.SetActive(true);
                var trans = obj.transform;
                var node = new TabNode
                {
                    txt1 = trans.Find("btn/txt1").GetComponent<UIText>(),
                    txt2 = trans.Find("btn/txt2").GetComponent<UIText>(),
                    icon = trans.Find("btn/icon").GetComponent<UIImage>(),
                    onBg = trans.Find("btn/onBg").gameObject,
                    offBg = trans.Find("btn/offBg").gameObject,
                    btn = trans.Find("btn").GetComponent<UIButton>(),
                    mask = trans.Find("mask").GetComponent<UIButton>(),
                    redDot = trans.Find("btn/dot").gameObject,
                };
                var index = i;
                BindBtnLogic(node.btn, () => { OnSelectLogic(index); });
                _objList.Add(node);
            }
        }

        /// <summary>
        /// 绑定指定页签索引的回调函数
        /// </summary>
        /// <param name="index">页签索引</param>
        /// <param name="txt">页签问本</param>
        /// <param name="callback">选中回调</param>
        public void BindSelectCallback(int index, string txt, string spritePath, UnityAction callback)
        {
            if (_callbackDic.ContainsKey(index))
            {
                Debug.LogError("已经注册过了！");
                return;
            }

            if (callback == null)
            {
                Debug.LogError("回调为空！");
                return;
            }

            if (index < 0 || index > _objList.Count)
            {
                Debug.LogError("索引越界！");
                return;
            }

            var node = _objList[index];
            node.txt1.text = ToolScriptExtend.GetLang(1100097);//"招募"
            node.txt2.text = txt;
            node.icon.SetImage("Sprite/ui_jianzhu_jiuguan/" + spritePath);
            _callbackDic.Add(index, callback);
            node.redDot.SetActive(false);

            var selectBg = "";

            if (index == 1)
            {
                var img = node.onBg.GetComponent<UIImage>();
                img.SetImage("Sprite/ui_jianzhu_jiuguan/zhaomu_tongyong_dilang_qieye2.png");
            }
        }

        /// <summary>
        /// 调用指定索引对应的回调函数
        /// </summary>
        /// <param name="index"></param>
        private void OnSelectLogic(int index)
        {
            if (index == _selectIndex)
            {
                return;
            }

            IsShowHundred = false;
            RefreshIndexView(index);
        }

        private void RefreshIndexView(int index)
        {
            _selectIndex = index;
            var isExist = _callbackDic.TryGetValue(_selectIndex, out UnityAction callback);
            if (isExist)
            {
                callback?.Invoke();
                SetSelectStatus(index);
                SetNodeActive(recruitType);
                
                GameEntry.LogicData.RecruitData.SetRecruitTicketView(recruitType, m_goTopItem);
                GameEntry.LogicData.RecruitData.SetBtnTxt(m_btnOne, recruitnumtype.recruitnumtype_one, recruitType);
                GameEntry.LogicData.RecruitData.SetBtnTxt(m_btnTen, recruitnumtype.recruitnumtype_ten, recruitType);
                GameEntry.LogicData.RecruitData.SetBtnTxt(m_btnHundred, recruitnumtype.recruitnumtype_hundred, recruitType);
                CheckFree();
                CheckHundredShow();
                ShowPreviewHero();
                
            }
        }
        
        /// <summary>
        /// 控制指定页签的UI表现
        /// </summary>
        /// <param name="index"></param>
        private void SetSelectStatus(int index)
        {
            var count = _objList.Count;
            for (var i = 0; i < count; i++)
            {
                var isActive = index == i;
                var node = _objList[i];
                node.onBg.SetActive(isActive);
                node.offBg.SetActive(!isActive);
            }
        }

        //绑定按钮点击回调
        private void BindBtnLogic(Button btn, UnityAction action)
        {
            btn.onClick.RemoveAllListeners();
            if (action != null)
            {
                btn.onClick.AddListener(action);
            }
        }

        //判断标签解锁
        private void CheckTagUnlock()
        {
            for (var i = 0; i < _objList.Count; i++)
            {
                var unlock = true;
                var node = _objList[i];
                node.mask.onClick.RemoveAllListeners();
                if (i == 0)
                {
                }
                else if (i == 1)
                {
                }
                else if (i == 2)
                {
                    // unlock = false;
                    node.mask.onClick.AddListener(() =>
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {
                            Content = "酒馆3级解锁！",
                        });
                    });
                }

                node.mask.gameObject.SetActive(!unlock);
            }
        }

        #endregion

        #region 购买礼包

        //检测礼包信息
        private void CheckGiftInfo()
        {
            m_goGift.gameObject.SetActive(false);
            if (giftDic.TryGetValue(recruitType, out var giftId))
            {
                DisplayGiftInfo(giftId);
                return;
            }

            RequestNewGiftId();
        }

        //请求礼包数据
        private void RequestNewGiftId()
        {
            if (ToolScriptExtend.GetConfigById<recruit_config>(recruitId, out var config))
            {
                GameEntry.LogicData.MallData.C2SGiftGetWay((int)config.gift, (giftId) =>
                {
                    giftDic[recruitType] = giftId;
                    DisplayGiftInfo(giftId);
                });
            }
        }
        
        private void DisplayGiftInfo(int giftId)
        {
            var rewardCount = 0;
            if (giftId > 0)
            {
                var rewardList = GameEntry.LogicData.MallData.GetRewardList(giftId);
                rewardCount = rewardList.Count;
                ShowGiftList(rewardList);
                SetGiftBuy(giftId);
            }
            m_goGift.gameObject.SetActive(giftId > 0 && rewardCount > 0);
        }
        
        //设置礼包购买信息
        private void SetGiftBuy(int giftId)
        {
            var root = m_goGift.transform;
            var title = root.Find("Text").GetComponent<UIText>();//礼包名称
            var icon = root.Find("icon").GetComponent<UIImage>();//礼包图标
            var discount = root.Find("Image/discount").GetComponent<UIText>();//折扣
            var btn = root.Find("btn").GetComponent<UIButton>();//购买按钮
            var price = root.Find("btn/price").GetComponent<UIText>();//价格
            
            if (!ToolScriptExtend.GetConfigById<gift_pack>(giftId, out var data)) return;
            
            title.text = ToolScriptExtend.GetLang(data.gift_pack_name);
            
            icon.SetImage(data.gift_pack_icon);
            discount.text = $"{data.cost_effectiveness}% {ToolScriptExtend.GetLang(1100286)}";
            price.text = GameEntry.LogicData.MallData.GetPrice(data.payment_id);
            GameEntry.LogicData.MallData.CreateRechargeScore(data.payment_id,btn.transform);

            BindBtnLogic(btn, () =>
            {
                GameEntry.PaymentData.Pay(data.payment_id);
            });
        }
        
        private void ShowGiftList(List<reward> rewardList)
        {
            var giftRoot = m_goGiftRoot.transform;
            ToolScriptExtend.ClearAllChild(giftRoot);
            
            var mallManager = GameEntry.LogicData.MallData;
            mallManager.RecycleOrCreate( m_goRewardItem,giftRoot,rewardList.Count);
            for (var i = 0; i < rewardList.Count; i++)
            {
                var child = giftRoot.GetChild(i);
                var node = child.Find("node");
                node.localScale = Vector3.one*0.45f;
                var cell = rewardList[i];
                var com = child.GetComponentsInChildren<UIItemModule>();
                if (com.Length == 0)
                {
                    Instantiate(mallManager.RewardObj,node);
                }
                mallManager.SetRewardInfo(node, node.GetChild(0).gameObject, cell.item_id, (int)cell.num,1.4f);
            }
        }

        #endregion

        #region 传奇再临

        //传奇保底说明
        private void OnBtnLegendBaoDiClick()
        {
            var str = "再招募41次，百分百会出现传奇英雄。\n抽到传奇英雄后将重置次数。\n重复获得的英雄会转化为10个同名英雄碎片。\n赛季结束后，保底进度将重置。";
            OpenCheckDesc(m_btnLegendBaoDi.transform.position, str);
        }

        private void OnBtnLegendDropClick()
        {
            Game.GameEntry.UI.OpenUIForm(EnumUIForm.UIRecruitRatioForm,recruitId);
        }

        #endregion

        #region 英雄招募

        private float offsetX;

        //英雄保底说明
        private void OnBtnHeroBaoDiClick()
        {
            ShowBaoDiTip(GameEntry.LogicData.RecruitData.RecruitId_Hero, 1100141);
        }

        //选择心愿英雄
        private void OnBtnSelectFinalHeroClick()
        {
            Game.GameEntry.UI.OpenUIForm(EnumUIForm.UISelectFinalHeroForm);
        }

        //查看或切换心愿英雄
        private void OnBtnFinalHeroClick()
        {
            //判断是否可以领取心愿英雄
            var isOk = GameEntry.LogicData.RecruitData.IsCanReceiveWishHero(recruitId);
            if (isOk)
            {
                GameEntry.LogicData.RecruitData.C2SRecruitDrawWish(recruitId, (msg) =>
                {
                    List<reward> rewards = new()
                    {
                        new reward()
                        {
                            item_id = (itemid)msg.HeroId,
                            num = 1
                        }
                    };
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);
                });
            }
            else
            {
                Game.GameEntry.UI.OpenUIForm(EnumUIForm.UISelectFinalHeroForm);
            }
        }

        private void CheckWishHeroView()
        {
            var manager = GameEntry.LogicData.RecruitData;
            var wishUnlock = manager.CheckWishUnlock();
            m_goWishNode.SetActive(wishUnlock);
            if (!wishUnlock)
            {
                return;
            }
            var isSelected = manager.HasSelectWishHero();
            if (isSelected)
            {
                var heroId = manager.GetWishHero();
                var heroData = manager.GetHeroDataById(heroId);
                if (heroData != null)
                {
                    m_imgFinalHero.SetImage(heroData.hero_head);
                }
            }

            m_btnFinalHero.gameObject.SetActive(isSelected);
            m_btnSelectFinalHero.gameObject.SetActive(!isSelected);
        }

        //心愿英雄领取进度
        public void SetWishHeroProgress()
        {
            var manager = GameEntry.LogicData.RecruitData;
            var wishUnlock = manager.CheckWishUnlock();
            m_goWishNode.SetActive(wishUnlock);
            if (!wishUnlock)
            {
                return;
            }
            if (recruitType != recruittype.recruittype_hero) return;
            var value1 = manager.GetWishTimes(recruitId);
            var value2 = manager.GetWishConfigCount(recruitId);
            m_sliderWish.value = (float)value1 / value2;
            m_txtWish.text = $"<size=70>{value1}</size>/{value2}";
        }

        //展示英雄招募立绘
        private void ShowHeroVertical()
        {
            if (!GameEntry.LDLTable.HaseTable<recruit_drops>()) return;
            var data = GameEntry.LDLTable.GetTable<recruit_drops>();
            var result = from cell in data
                where (cell.drop_group_id == 10011 && GameEntry.LogicData.RecruitData.CheckCellUnlock(cell.id))
                orderby cell.view_order descending 
                select cell.drop_reward.item_id;
                
            var heroId = (int)result.FirstOrDefault();
            ToolScriptExtend.ShowHeroSpine(heroId, m_spuiRole);
        }
        
        #endregion

        #region 幸存者招募

        //幸存者保底说明
        private void OnBtnSurvivalBaoDiClick()
        {
            ShowBaoDiTip(GameEntry.LogicData.RecruitData.RecruitId_Survivor, 1100142);
        }

        private void OnBtnSurvivalHelpClick()
        {
            Game.GameEntry.UI.OpenUIForm(EnumUIForm.UIRecruitRatioForm,recruitId);
        }

        #endregion

        #region 保底进度

        //设置保底数据显示
        private void SetBaoDiData(UIButton btn, int id)
        {
            var count2 = GameEntry.LogicData.RecruitData.GetGuaranteeCount(id);
            var count1 = GameEntry.LogicData.RecruitData.GetGuardTimes(id);
            var root = btn.gameObject.transform;
            var fill = root.Find("fill").GetComponent<Image>();
            var txt = root.Find("txt").GetComponent<UIText>();
            fill.fillAmount = (float)count1 / count2;
            txt.text = $"{count1}/{count2}";
        }

        #endregion

        #region 英雄预览

        private void ShowPreviewHero()
        {
            preHeroList.Clear();
            ToolScriptExtend.ClearAllChild(m_transPreRoot);
            var dataList = GameEntry.LogicData.RecruitData.GetPreviewHeroList(recruitId);
            preHeroList.AddRange(dataList);
            var needShowPre = recruitType != recruittype.recruittype_survivor && preHeroList.Count > 0;
            m_goPreview.SetActive(needShowPre);
            isTickPre = needShowPre;
            if (isTickPre) 
            {
                var curData = preHeroList[curPreIndex];
                var openTimestamp = GameEntry.LDLNet.GetOpenServiceTimestamp() + curData.on_time * 86400;
                curPreTimerCount = openTimestamp - (int)TimeComponent.Now;
                sb.Clear();
                var time = GameEntry.LogicData.RecruitData.FormatTime(curPreTimerCount);
                sb.Append(time);
                m_txtHeroPre.text = sb.ToString();
            }
            if (preHeroList.Count == 0)
            {
                return;
            }
            preNodeList.Clear();
            curPreIndex = -1;
            var count = preHeroList.Count;
            for (var i = 0; i < count; i++)
            {
                var obj = Instantiate(m_goPreNode, m_transPreRoot);
                var onNode = obj.transform.Find("on");
                var offNode = obj.transform.Find("off");
                obj.SetActive(i < 5);
                preNodeList.Add(new PreviewNode()
                    { rootNode = obj, onNode = onNode.gameObject, offNode = offNode.gameObject });
            }

            if (count > 0)
            {
                curPreIndex = 0;
                DragFunc();
            }
        }
        
        //新英雄预告
        public void BindTrigger()
        {
            var event1 = new EventTrigger.Entry
            {
                eventID = EventTriggerType.PointerDown
            };
            event1.callback.AddListener((eventData) =>
            {
                var data = eventData as PointerEventData;
                offsetX = data.position.x;
            });

            var event2 = new EventTrigger.Entry
            {
                eventID = EventTriggerType.PointerUp
            };
            event2.callback.AddListener((eventData) =>
            {
                var data = eventData as PointerEventData;
                var value = offsetX - data.position.x;

                if (Mathf.Abs(value) > 30)
                {
                    if (value < 0)
                    {
                        var temp = curPreIndex - 1;
                        if (temp >= 0 && temp < preNodeList.Count)
                        {
                            curPreIndex--;
                            DragFunc();
                        }
                    }
                    else if (value > 0)
                    {
                        var temp = curPreIndex + 1;
                        if (temp >= 0 && temp < preNodeList.Count)
                        {
                            curPreIndex++;
                            DragFunc();
                        }
                    }
                }
                else
                {
                    Game.GameEntry.UI.OpenUIForm(EnumUIForm.UIHeroPoolUpdateForm, $"{recruitId}|{curPreIndex}");
                }
            });

            EventTrigger trigger = m_goHeroPre.AddComponent<EventTrigger>();
            trigger.triggers.Clear();
            trigger.triggers.Add(event1);
            trigger.triggers.Add(event2);
        }

        private void DragFunc()
        {
            if (curPreIndex < 0 && curPreIndex >= preNodeList.Count)
            {
                return;
            }

            if (preNodeList.Count < 5)
            {
                for (var i = 0; i < preNodeList.Count; i++)
                {
                    var isCur = curPreIndex == i;
                    var node = preNodeList[i];
                    node.onNode.SetActive(isCur);
                    node.offNode.SetActive(!isCur);
                }
            }
            else
            {
                if (curPreIndex >= 5)
                {
                    for (var i = 0; i < 5; i++)
                    {
                        var node = preNodeList[i];
                        node.rootNode.SetActive(false);
                    }

                    for (var i = 5; i < preNodeList.Count; i++)
                    {
                        var isCur = curPreIndex == i;
                        var node = preNodeList[i];
                        node.rootNode.SetActive(true);
                        node.onNode.SetActive(isCur);
                        node.offNode.SetActive(!isCur);
                    }
                }
                else
                {
                    for (var i = 5; i < preNodeList.Count; i++)
                    {
                        var node = preNodeList[i];
                        node.rootNode.SetActive(false);
                    }

                    for (var i = 0; i < 5; i++)
                    {
                        var isCur = curPreIndex == i;
                        var node = preNodeList[i];
                        node.rootNode.SetActive(true);
                        node.onNode.SetActive(isCur);
                        node.offNode.SetActive(!isCur);
                    }
                }
            }
            SetPreviewHeroInfo();
        }

        private void SetPreviewHeroInfo()
        {
            var drops = preHeroList[curPreIndex];
            var heroData = GameEntry.LogicData.RecruitData.GetHeroDataById(drops.drop_reward.item_id);
            if (heroData != null)
            {
                m_imgHeroPre.SetImage(heroData.hero_head);
            }
            SwitchPreHeroCheck();
        }
        
        private void SwitchPreHeroCheck()
        {
            if (isTickPre)
            {
                var curData = preHeroList[curPreIndex];
                curPreTimerCount = GameEntry.LDLNet.GetOpenServiceTimestamp() + curData.on_time * 86400 - (int)TimeComponent.Now;
            }
        }
        #endregion
    }
}