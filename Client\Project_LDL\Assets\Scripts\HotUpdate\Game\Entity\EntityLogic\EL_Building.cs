using System;
using System.Collections.Generic;
using Build;
using Game.Hotfix.Config;
using GameFramework.Event;
using HotUpdate.Game.Module.BuildingModules;
using JetBrains.Annotations;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class EL_Building : Entity
    {
        public BuildingModule BuildingModule => m_BuildingModule;
        protected ED_Building m_Data;
        protected BuildingModule m_BuildingModule;
        protected EventDispatch<EL_BuildingEvent> m_EventDispatch = new EventDispatch<EL_BuildingEvent>();

        protected BuildingState? m_CurBuildingState = null;
        protected int? eIdDisplay;
        protected int? eIdUpgrade;
        protected uint? uavHudUid = null;
        protected int? uavDisplayUid;
        
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
        }

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);

            m_Data = (ED_Building)userData;
            m_BuildingModule = m_Data.buildingData;

            if (m_Data.Position != null)
            {
                transform.position = m_Data.Position.Value;
            }

            if (m_Data.parentEntityId != 0 && m_Data.parentTransformPath != null)
            {
                Game.GameEntry.Entity.AttachEntity(this.Entity.Id, m_Data.parentEntityId, m_Data.parentTransformPath);
                transform.localPosition = Vector3.zero;
            }

            OnStateChange(m_BuildingModule.GetBuildingState());
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnBuildingStateChange, OnBuildingStateChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnResourceQueueStateChange, OnResourceQueueStateChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnHospitalQueueStateChange, OnHospitalQueueStateChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnSoldierQueueStateChange, OnSoldierQueueStateChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnEquipmentQueueStateChange, OnEquipmentQueueStateChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnShopStateChange, OnShopStateChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnSurvivorChange, OnSurvivorChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnUnionHelpChange, OnUnionHelpChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnWallChange, OnWallChange);
            
            GameEntry.Event.Subscribe(OnBuildingLevelChangeEventArgs.EventId, OnBuildingLevelChangeEvent);

            //初始化派遣气泡
            bool canDispatch = false;
            if (m_BuildingModule.DispatchAble())
            {
                canDispatch = m_BuildingModule.CheckSurvivorDispatch();
            }

            if (!canDispatch)
            {
                //初始化是否要显示资源气泡
                var queue = GameEntry.LogicData.QueueData.GetResourceQueue((uint)m_BuildingModule.BuildingId);
                if (queue != null)
                    GameEntry.HUD.ShowHUD(this, EnumHUD.HUDProduceResource);

                //初始化医院气泡
                bool isShowHospitalHud = GameEntry.LogicData.SoliderData.GetIsShowHospitalHud();
                if (queue == null && m_BuildingModule.GetBuildingType() == buildtype.buildtype_hospital && isShowHospitalHud)
                {
                    GameEntry.HUD.ShowHUD(this, EnumHUD.HUDSoldierTreat);
                }

                //初始化商店气泡
                var isShowShopHud = GameEntry.LogicData.MallData.InitShopBubbleShow();
                if (queue == null && m_BuildingModule.GetBuildingType() == buildtype.buildtype_shop && isShowShopHud)
                {
                    GameEntry.HUD.ShowHUD(this, EnumHUD.HUDShop);
                }

                //初始化人才大厅气泡
                bool isHaveCanUp = GameEntry.LogicData.SurvivorData.CheckAllSurvivorIsCanUp();
                if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_talenthall && isHaveCanUp)
                {
                    GameEntry.HUD.ShowHUD(this, EnumHUD.HUDBuildingDispatch);
                }

                //初始化城墙消防气泡
                bool isShow = false;
                if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_gate && isShow)
                {
                    GameEntry.HUD.ShowHUD(this, EnumHUD.HUDOutfire);
                }
            }
            
            CheckHudRepairShow();
            
            //初始化无人机形象
            if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_uavcenter && m_BuildingModule.GetBuildingState() == BuildingState.Normal && eIdDisplay != null)
            {
                var path = GameEntry.LogicData.UAVData.GetUavDisplayPath();
                if (!string.IsNullOrEmpty(path))
                {
                    Vector3 position = new Vector3(5.2f, 1.5f, 2f);;
                    Quaternion rotation = Quaternion.Euler(-85, 180, 0);
                    Vector3 scale = new Vector3(0.8f,0.8f,0.8f);
                    uavDisplayUid = GameEntry.Entity.ShowCommonDisplay(path,this.Id,null,position,rotation,scale);
                }
            }
        }

        private void CheckHudRepairShow()
        {
            if (m_BuildingModule.LEVEL == 0 && m_BuildingModule.GetBuildingState() == BuildingState.Normal)
            {
                bool isShowHud = true;
                if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_team && m_BuildingModule.BuildingId % 100 != 4)
                {
                    for (var i = 0; i < m_BuildingModule.buildingCfg.build_demand.Count; i++)
                    {
                        int demandId = m_BuildingModule.buildingCfg.build_demand[i];
                        isShowHud = ToolScriptExtend.GetDemandUnlock(demandId);
                    }
                }

                if (isShowHud)
                {
                    GameEntry.HUD.ShowHUD(this,EnumHUD.HUDRepair);
                }
            }
        }

        private void OnBuildingLevelChangeEvent(object sender, GameEventArgs e)
        {
            if (e is OnBuildingLevelChangeEventArgs args)
            {
                CheckHudRepairShow();
            }
        }

        private void OnResourceQueueStateChange(object obj)
        {
            if (obj is ResourceQueueChangeState state)
            {
                if (state == ResourceQueueChangeState.Add)
                {
                    bool canDispatch = m_BuildingModule.CheckSurvivorDispatch();
                    if (canDispatch)
                    {
                        return;
                    }
                    GameEntry.HUD.ShowHUD(this,EnumHUD.HUDProduceResource);
                }    
            }
        }
        
        private void OnHospitalQueueStateChange(object obj)
        {
            if (obj is HospitalSoldierParams pParams)
            {
                if (pParams.m_State == HospitalQueueChangeState.Add)
                {
                    bool canDispatch = m_BuildingModule.CheckSurvivorDispatch();
                    if (canDispatch)
                    {
                        return;
                    }
                    GameEntry.HUD.ShowHUD(this,EnumHUD.HUDSoldierTreat);
                }   
                else if (pParams.m_State == HospitalQueueChangeState.TreatComplete)
                {
                    if (pParams.m_TreatSoldiers != null)
                    {
                        int totalNum = 0;
                        foreach (var treatSoldier in pParams.m_TreatSoldiers)
                        {
                            totalNum += treatSoldier.Value;
                        }

                        foreach (var treatSoldier in pParams.m_TreatSoldiers)
                        {
                            int soldierLevel = treatSoldier.Key;
                            int showNum = Mathf.FloorToInt((float)treatSoldier.Value / totalNum * 10);
                            PlayAddSoldierTween(soldierLevel,showNum);
                        }
                    }
                }
            }
        }        
        
        private void OnSoldierQueueStateChange(object obj)
        {
            if (obj is SoldierParams soldierParams)
            {
                // bool canDispatch = m_BuildingModule.CheckSurvivorDispatch();
                // if (canDispatch)
                // {
                //     return;
                // }

                if (soldierParams.m_State == SoldierQueueChangeState.Add)
                {
                    GameEntry.HUD.ShowHUD(this,EnumHUD.HUDSoldierTrain);
                }
                else if (soldierParams.m_State == SoldierQueueChangeState.SoldierChange)
                {
                    if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_ground)
                    {
                        if (eIdDisplay != null)
                        {
                            Entity gameEntity = GameEntry.Entity.GetGameEntity(eIdDisplay.Value);
                            if (gameEntity is EL_BuildingDisplaySoldier displaySoldier)
                            {
                                // todo 士兵更改刷新建筑上的士兵模型
                                bool isAdd = soldierParams.m_OpType == SoldierChangeType.Add;
                                if (isAdd)
                                {
                                    //displaySoldier.AddSoldierDisplay(soldierParams.soldierLevel, soldierParams.num);
                                    displaySoldier.ResetDisplayList();
                                }
                            }
                        }
                    }   
                    if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_barrack)
                    {
                        bool isAdd = soldierParams.m_OpType == SoldierChangeType.Add;
                        if (isAdd)
                        {
                            int soldierLevel = soldierParams.soldierLevel;
                            PlayAddSoldierTween(soldierLevel,soldierParams.num);
                        }                
                    }
                }
            }
        }

        private void OnEquipmentQueueStateChange(object obj)
        {
            if (obj is EquipmentQueueChangeState state)
            {
                if (state == EquipmentQueueChangeState.Add)
                {
                    bool canDispatch = m_BuildingModule.CheckSurvivorDispatch();
                    if (!canDispatch)
                    {
                        GameEntry.HUD.ShowHUD(this,EnumHUD.HUDEquipment);
                    }
                }    
            }
        }
        
        private void OnShopStateChange(object obj)
        {
            if (obj is ShopBuildingState state)
            {
                if (state == ShopBuildingState.CanGetReward)
                {
                    GameEntry.HUD.ShowHUD(this,EnumHUD.HUDShop);
                }    
            }
        }

        private void OnBuildingStateChange(object obj)
        {
            OnStateChange(m_BuildingModule.GetBuildingState());
        }
        
        private void OnSurvivorChange(object obj)
        {
            if (obj is SurvivorChangeState state)
            {
                if (state == SurvivorChangeState.Add)
                {
                    CheckOtherHUDHide();
                    GameEntry.HUD.ShowHUD(this,EnumHUD.HUDBuildingDispatch);
                }
                else if (state == SurvivorChangeState.Remove)
                {
                    CheckOtherHUDShow();
                }
            }
        }

        private void OnUnionHelpChange(object obj)
        {
            var helpList = m_BuildingModule.m_unionHelpList;
            if (helpList != null && helpList.Count > 0)
                GameEntry.HUD.ShowHUD(this, EnumHUD.HUDUnionHelp);
        }

        private void OnWallChange(object obj)
        {
            var isShow = true;
            if (isShow) GameEntry.HUD.ShowHUD(this, EnumHUD.HUDOutfire);
        }

        //派遣时需要关掉其他hud
        private void CheckOtherHUDHide()
        {
            // 资源类
            var queue = GameEntry.LogicData.QueueData.GetResourceQueue((uint)m_BuildingModule.BuildingId);
            if (queue != null)
            {
                m_BuildingModule.OnResourceQueueStateChange(ResourceQueueChangeState.Remove);
            }

            if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_hospital)
            {
                m_BuildingModule.OnHospitalQueueStateChange(new HospitalSoldierParams(HospitalQueueChangeState.Remove));
            }
            else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_barrack)
            {
                m_BuildingModule.OnSoldierQueueStateChange(new SoldierParams(SoldierQueueChangeState.Remove));
            }

        }       
        //派遣时结束时需要打开其他hud
        private void CheckOtherHUDShow()
        {
            // 资源类
            var queue = GameEntry.LogicData.QueueData.GetResourceQueue((uint)m_BuildingModule.BuildingId);
            if (queue != null)
            {
                m_BuildingModule.OnResourceQueueStateChange(ResourceQueueChangeState.Add);
            }

            if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_hospital)
            {
                int totalHospitalCount = GameEntry.LogicData.SoliderData.GetTotalHospitalCount();
                if (totalHospitalCount > 0)
                {
                    GameEntry.HUD.ShowHUD(this,EnumHUD.HUDSoldierTreat);
                }
            }
            else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_barrack)
            {
                GameEntry.HUD.ShowHUD(this,EnumHUD.HUDSoldierTrain);
            }
            else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_researchcenter)
            {
                GameEntry.HUD.ShowHUD(this,EnumHUD.HUDTech);
            }
            
        }

        private void PlayAddSoldierTween(int soldierLevel,int soldierNum)
        {
            string soldierDisplayPath = GameEntry.LogicData.SoliderData.GetSoldierDisplayPath(soldierLevel);
            if (!string.IsNullOrEmpty(soldierDisplayPath))
            {
                BuildingModule findBuild = GameEntry.LogicData.BuildingData.GetMinLevelBuildingModuleGround();
                var targetEntity = GameEntry.Entity.GetEntity(findBuild.UID);
                Vector3 targetPos = targetEntity.transform.position;
                int soldierDisplayNum = soldierNum <= 10 ? soldierNum : 10;
                for (int i = 0; i < soldierDisplayNum; i++)
                {
                    var timeKey = "SoldierChange" + i;
                    Timers.Instance.Remove(timeKey);
                    Timers.Instance.Add(timeKey, i * 0.8f, (param) =>
                    {
                        Game.GameEntry.Entity.ShowSoldierDisplay(soldierDisplayPath, this.Id,0,findBuild.UID,targetPos);
                    });
                }
            }
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            m_BuildingModule.RemoveEventListener(BuildingModuleEvent.OnBuildingStateChange,OnBuildingStateChange);
            m_BuildingModule.AddEventListener(BuildingModuleEvent.OnResourceQueueStateChange,OnResourceQueueStateChange);
            base.OnHide(isShutdown, userData);
            if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_barrack)
            {
                m_BuildingModule.OnSoldierQueueStateChange(new SoldierParams(SoldierQueueChangeState.Remove));
            }
            else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_equipfactory)
            {
                m_BuildingModule.OnEquipmentQueueStateChange(EquipmentQueueChangeState.Remove);
            }
            else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_shop)
            {
                m_BuildingModule.OnShopStateChange(ShopBuildingState.Remove);
            }
            else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_uavcenter)
            {
                if (uavHudUid != null)
                    GameEntry.HUD.HideHUD(uavHudUid.Value);
            }
        }

        public virtual void OnMenuBtnClick(build_menubutton cfg)
        {
            m_BuildingModule?.OnMenuClick(cfg);
        }

        public List<int> GetMenuList()
        {
            if (m_BuildingModule != null)
            {
                return m_BuildingModule.GetMenuList();
            }

            return new List<int>();
        }

        protected override string GetDebugInfo()
        {
            return base.GetDebugInfo() + "_ET_Building" + this.m_BuildingModule.BuildingId;
        }

        public BuildingModule GetBuildingModule()
        {
            return m_BuildingModule;
        }

        public void GetCenterWorldPos(out Vector3 pos)
        {
            var offset = m_BuildingModule.GetOffsetCenter();
            pos = default;
            pos.Set(transform.position.x + offset.x, transform.position.y, transform.position.z + offset.y);
        }

        public void OpenMenuLevelUp(bool isUpgrading)
        {
            var menuList = this.GetMenuList();
            if (menuList != null && menuList.Count > 0)
            {
                if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingMenuForm))
                {
                    //界面会自行处理
                }
                else
                {
                    UIBuildingMenuFormParam param = new UIBuildingMenuFormParam();
                    param.ElBuilding = this;
                    param.MenuList = menuList;
                    param.isPlayLevelUpTween = !isUpgrading;
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingMenuForm, param);
                }
            }
        }

        public override void OnClick()
        {
            base.OnClick();

            if (m_CurBuildingState == BuildingState.ConstructionComplete || m_CurBuildingState == BuildingState.UpgradeComplete)
            {
                m_BuildingModule.BuildQueueFinishReq();
                return;
            }
            
            //点击废墟
            if (m_BuildingModule.GetBuildingState() == BuildingState.Normal && m_BuildingModule.LEVEL == 0)
            {
                //GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingDetailForm, m_BuildingModule);
                GameEntry.LogicData.BuildingData.BuildUpgradeReq((uint)m_BuildingModule.BuildingId,UpgradeType.UpgradeNormal);
                return;
            }
            
            //点击商店
            if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_shop)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIGeneralShopForm);
                return;
            }
            
            //点击人才大厅
            if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_talenthall && m_CurBuildingState == BuildingState.Normal)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingSurvivorForm);
                return;
            }

            var menuList = this.GetMenuList();
            if (menuList != null && menuList.Count > 0)
            {
                if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingMenuForm))
                {
                    //界面会自行处理
                }
                else
                {
                    UIBuildingMenuFormParam param = new UIBuildingMenuFormParam();
                    param.ElBuilding = this;
                    param.MenuList = menuList;
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingMenuForm, param);
                }
            }
            else
            {
                //没有菜单
                if (GameEntry.UI.HasUIForm(EnumUIForm.UIBuildingMenuForm))
                {
                    //界面会自行处理
                }
                else
                {
                    m_BuildingModule?.OnClick();
                }
            }
        }

        public void OnMenuShow(bool show)
        {
            SendEvent(EL_BuildingEvent.OnMenuVisibleChange, show);
        }
        
        public bool CanMove()
        {
            return m_BuildingModule.CanMove();
        }
        
        protected override void OnAttachTo(EntityLogic parentEntity, Transform parentTransform, object userData)
        {
            base.OnAttachTo(parentEntity, parentTransform, userData);
            if (parentEntity is EL_BuidlingPreview)
            {
                //被拿起
            }
        }

        protected override void OnDetachFrom(EntityLogic parentEntity, object userData)
        {
            base.OnDetachFrom(parentEntity, userData);
            if (parentEntity is EL_BuidlingPreview)
            {
                //被放下
            }
        }

        protected void OnStateChange(BuildingState nextState)
        {
            //退出上一个状态
            if (m_CurBuildingState != null && m_CurBuildingState != nextState)
            {
                if ((m_CurBuildingState is BuildingState.Normal or BuildingState.Upgrading
                        or BuildingState.UnderConstruction) &&
                    (nextState is BuildingState.ConstructionComplete or BuildingState.UpgradeComplete))
                {
                    //普通状态变成完成状态
                    if (eIdDisplay != null)
                    {
                        Game.GameEntry.Entity.HideEntity(eIdDisplay.Value);
                        eIdDisplay = null;
                    }
                }else if ((nextState is BuildingState.Normal or BuildingState.Upgrading
                              or BuildingState.UnderConstruction) &&
                          (m_CurBuildingState is BuildingState.ConstructionComplete or BuildingState.UpgradeComplete))
                {
                    if (eIdDisplay != null)
                    {
                        Game.GameEntry.Entity.HideEntity(eIdDisplay.Value);
                        eIdDisplay = null;
                    }
                }
            }

            if (eIdUpgrade != null &&
                (nextState is BuildingState.Normal or BuildingState.ConstructionComplete
                    or BuildingState.UpgradeComplete))
            {
                Game.GameEntry.Entity.HideEntity(eIdUpgrade.Value);
                eIdUpgrade = null;
            }
            
            //进入下一个状态

            //加载外观
            if (eIdDisplay == null)
            {
                if ((nextState is BuildingState.Normal or BuildingState.Upgrading
                        or BuildingState.UnderConstruction))
                {
                    var path = m_BuildingModule.GetBuildingPrefabCfg().pre_location;
                    if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_ground)
                    {
                        eIdDisplay = Game.GameEntry.Entity.ShowBuildingDisplaySoldier(path, this.Id);
                    }
                    else
                    {
                        eIdDisplay = Game.GameEntry.Entity.ShowBuildingDisplay(path, this.Id);
                    }
                }
                else if ((nextState is BuildingState.ConstructionComplete or BuildingState.UpgradeComplete))
                {
                    var l = m_BuildingModule.GetGridAreaL();
                    var w = m_BuildingModule.GetGridAreaW();
                    var scale = Math.Min(Math.Max(l, w), 6);
                    
                    var path = "Assets/ResPackage/Prefab/Building/buildingOPComplete.prefab";
                    eIdDisplay =
                        Game.GameEntry.Entity.ShowBuildingDisplay(path, this.Id, Vector3.one * scale,
                            new Vector3(l / 2f, 0, w / 2f));
                }
            }

            if (eIdUpgrade == null)
            {
                if ((nextState is BuildingState.Upgrading or BuildingState.UnderConstruction))
                {
                    var l = m_BuildingModule.GetGridRepairAreaL();
                    var w = m_BuildingModule.GetGridRepairAreaW();
                    var scale = new Vector3(l, (l + w) / 2f, w);
                    var pos = m_BuildingModule.GetGridRepairOffset();
                    var path = "Assets/ResPackage/Prefab/Building/buildingOPUpgrading.prefab";
                    eIdUpgrade =
                        Game.GameEntry.Entity.ShowBuildingDisplay(path, this.Id, scale, pos);
                }
            }

            bool isSame = m_CurBuildingState != null && m_CurBuildingState == nextState;
            
            m_CurBuildingState = nextState;

            OnStateChangePop(isSame);
        }

        private void OnStateChangePop(bool isSame)
        {
            if (isSame) return;
            if (m_CurBuildingState == null) return;
            if (m_CurBuildingState == BuildingState.Normal)
            {
                
            }else if (m_CurBuildingState == BuildingState.Upgrading)
            {
                GameEntry.HUD.ShowHUD(this,EnumHUD.HUDBuildingBuzy);
            }else if (m_CurBuildingState == BuildingState.UpgradeComplete)
            {
                
            }else if (m_CurBuildingState == BuildingState.UnderConstruction)
            {
                GameEntry.HUD.ShowHUD(this,EnumHUD.HUDBuildingBuzy);
            }else if (m_CurBuildingState == BuildingState.ConstructionComplete)
            {
                
            }
            if (m_BuildingModule.GetBuildingState() == BuildingState.Normal 
                || m_BuildingModule.GetBuildingState() == BuildingState.Upgrading 
                || m_BuildingModule.GetBuildingState() == BuildingState.UpgradeComplete)
            {
                //初始化派遣气泡
                bool canDispatch = false;
                if (m_BuildingModule.DispatchAble())
                {
                    canDispatch = m_BuildingModule.CheckSurvivorDispatch();
                    if (canDispatch && m_BuildingModule.GetBuildingState() == BuildingState.Normal)
                    {
                        GameEntry.HUD.ShowHUD(this,EnumHUD.HUDBuildingDispatch);
                    }
                }

                if (!canDispatch)
                {
                    if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_equipfactory)
                    {
                        // 初始化装备工厂气泡
                        GameEntry.HUD.ShowHUD(this,EnumHUD.HUDEquipment);
                    }
                    else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_barrack)
                    {
                        // 初始化兵营气泡
                        GameEntry.HUD.ShowHUD(this,EnumHUD.HUDSoldierTrain);
                    }
                    else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_shop)
                    {
                        string checkKey = ToolScriptExtend.GetRolePlayerPrefsKeyById("CheckShopToday");
                        bool isTodayCheck = ToolScriptExtend.GetCommonTipsIsTodayCheckBox(checkKey);
                        if (!isTodayCheck)
                        {
                            // 初始化商店气泡
                            GameEntry.HUD.ShowHUD(this,EnumHUD.HUDShop);
                        }
                    }
                    else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_uavcenter)
                    {
                        // 初始化无人机
                        uavHudUid = GameEntry.HUD.ShowHUD(this,EnumHUD.HUDUav);
                    }
                    else if (m_BuildingModule.GetBuildingType() == buildtype.buildtype_researchcenter)
                    {
                        // 初始化研究中心气泡
                        GameEntry.HUD.ShowHUD(this,EnumHUD.HUDTech);
                    }
                }
            }
        }
        
        #region 事件

        public void AddEventListener(EL_BuildingEvent eventType, EventCallBack eventHandler)
        {
            m_EventDispatch.RegisterEvent(eventType, eventHandler);
        }

        public void RemoveEventListener(EL_BuildingEvent eventType, EventCallBack eventHanlder)
        {
            m_EventDispatch.UnRegisterEvent(eventType, eventHanlder);
        }

        protected void SendEvent(EL_BuildingEvent eventType, object obj)
        {
            m_EventDispatch.PostEvent(eventType, obj);
        }

        #endregion

        #region 测试代码

#if UNITY_EDITOR

        [Sirenix.OdinInspector.Button(Sirenix.OdinInspector.ButtonSizes.Large),
         Sirenix.OdinInspector.GUIColor(0.4f, 0.8f, 1)]
        public void ChangeState_Normal()
        {
            OnStateChange(BuildingState.Normal);
        }

        [Sirenix.OdinInspector.Button(Sirenix.OdinInspector.ButtonSizes.Large),
         Sirenix.OdinInspector.GUIColor(0.4f, 0.8f, 1)]
        public void ChangeState_UnderConstruction()
        {
            OnStateChange(BuildingState.UnderConstruction);
        }

        [Sirenix.OdinInspector.Button(Sirenix.OdinInspector.ButtonSizes.Large),
         Sirenix.OdinInspector.GUIColor(0.4f, 0.8f, 1)]
        public void ChangeState_ConstructionComplete()
        {
            OnStateChange(BuildingState.ConstructionComplete);
        }

        [Sirenix.OdinInspector.Button(Sirenix.OdinInspector.ButtonSizes.Large),
         Sirenix.OdinInspector.GUIColor(0.4f, 0.8f, 1)]
        public void ChangeState_Upgrading()
        {
            OnStateChange(BuildingState.Upgrading);
        }

        [Sirenix.OdinInspector.Button(Sirenix.OdinInspector.ButtonSizes.Large),
         Sirenix.OdinInspector.GUIColor(0.4f, 0.8f, 1)]
        public void ChangeState_UpgradeComplete()
        {
            OnStateChange(BuildingState.UpgradeComplete);
        }
#endif

        #endregion
    }
}