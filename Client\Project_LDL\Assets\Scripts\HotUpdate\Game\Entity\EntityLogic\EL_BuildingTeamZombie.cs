using System;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Game.Hotfix
{
    public enum ZombieState
    {
        None,
        Born,
        Idle,
        Walk,
        Run,
        Attack,
        Die,
    }

    public class ED_BuildingTeamZombie : EntityData
    {
        public Vector3 TargetPos;

        public ED_BuildingTeamZombie(int entityId, Vector3 targetPos) : base(entityId)
        {
            TargetPos = targetPos;
        }
    }

    public class EL_BuildingTeamZombie : Entity
    {
        private ZombieState m_State = ZombieState.Idle;

        private BattleSlotHandler m_BattleSlotHandler;
        private Dictionary<slot, Transform> m_SlotsDic;

        private Vector3 m_TargetPos;

        private Animator m_Animator;

        private float m_ActionDuration = 0;

        private float m_MoveSpeed = 0.5f;
        private float m_RunSpeed = 1.5f;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
        }

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);

            m_SlotsDic = new Dictionary<slot, Transform>();
            m_BattleSlotHandler = CachedTransform.GetComponent<BattleSlotHandler>();
            if (m_BattleSlotHandler && m_BattleSlotHandler.slots != null)
            {
                foreach (var slotData in m_BattleSlotHandler.slots)
                {
                    m_SlotsDic.TryAdd(slotData.Slot, slotData.Transform);
                }
            }

            m_Animator = CachedTransform.GetComponentInChildren<Animator>();

            ED_BuildingTeamZombie data = userData as ED_BuildingTeamZombie;
            if (data == null)
            {
                return;
            }

            m_TargetPos = transform.position + new Vector3(0, 0, data.TargetPos.z - transform.position.z-3);

            ChangeState(ZombieState.Born);
        }

        private void Update()
        {
            if (m_ActionDuration != -1)
            {
                m_ActionDuration -= Time.deltaTime;
                if (m_ActionDuration < 0)
                    m_ActionDuration = 0;
            }

            if (m_State == ZombieState.Born)
            {
                if (m_ActionDuration <= 0)
                {
                    ChangeState(ZombieState.Walk);
                }
            }
            else if (m_State == ZombieState.Idle)
            {
                
            }
            else if (m_State == ZombieState.Walk)
            {   
                if (m_ActionDuration <= 0)
                {
                    ChangeState(ZombieState.Run);
                }
                else
                {
                    SetPosition(transform.position + new Vector3(0, 0, m_MoveSpeed * Time.deltaTime));
                    if (transform.position.z > m_TargetPos.z)
                    {
                        ChangeState(ZombieState.Attack);
                    }
                }
            }
            else if (m_State == ZombieState.Run)
            {
                if (m_ActionDuration <= 0)
                {
                    ChangeState(ZombieState.Walk);
                }
                else
                {
                    SetPosition(transform.position + new Vector3(0, 0, m_RunSpeed * Time.deltaTime));
                    if (transform.position.z > m_TargetPos.z)
                    {
                        ChangeState(ZombieState.Attack);
                    }
                }
            }
            else if (m_State == ZombieState.Attack)
            {
                if (m_ActionDuration <= 0)
                {
                    ChangeState(ZombieState.Attack);
                }
            }
            else if (m_State == ZombieState.Die)
            {
                if (m_ActionDuration <= 0)
                {
                    GameEntry.Entity.HideEntity(this);
                }
            }
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
        }

        private void ChangeState(ZombieState state)
        {
            m_State = state;
            PlayAnimation(state);
            
            if (m_State == ZombieState.Born)
            {
                m_ActionDuration = 1;
            }
            else if (m_State == ZombieState.Idle)
            {
                m_ActionDuration = -1;
            }
            else if (m_State == ZombieState.Walk)
            {
                m_ActionDuration = 4;
            }
            else if (m_State == ZombieState.Run)
            {
                m_ActionDuration = Random.Range(1, 3);
            }
            else if (m_State == ZombieState.Attack)
            {
                m_ActionDuration = 3;
            }
            else if (m_State == ZombieState.Die)
            {
                m_ActionDuration = 2;
            }
        }

        public bool CanDie()
        {
            if(m_State == ZombieState.Born)
                return false;
            if(m_State == ZombieState.Die)
                return false;
            return true;
        }
        
        public void Die(float flyTime)
        {
            DOTween.To(() => 0, curValue =>
            {
                
            }, 1f, flyTime).OnComplete(() =>
            {
                ChangeState(ZombieState.Die);    
            }).SetEase(Ease.Linear);
        }
        
        private void PlayAnimation(ZombieState state)
        {
            string triggerName = string.Empty;
            switch (state)
            {
                case ZombieState.Born:
                    triggerName = "appear";
                    break;
                case ZombieState.Idle:
                    triggerName = "idle";
                    break;
                case ZombieState.Walk:
                    triggerName = "walk";
                    break;
                case ZombieState.Run:
                    triggerName = "run";
                    break;
                case ZombieState.Attack:
                    triggerName = "act";
                    break;
                case ZombieState.Die:
                    triggerName = "die";
                    break;
            }

            if (triggerName != string.Empty)
            {
                m_Animator.SetTrigger(triggerName);
            }
        }
    }
}