using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using Team;
using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public class EL_BuildingTeam : EL_Building
    {
        private TeamType curTeamType = TeamType.TeamNil;
        private Dictionary<EnumBattlePos, int> m_TeamDic;
        Dictionary<EnumBattlePos, int> teamEntityUid = new Dictionary<EnumBattlePos, int>();

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            InitEvent();

            int buildingTeamIndex = m_BuildingModule.BuildingId % 100;
            curTeamType = buildingTeamIndex switch
            {
                1 => TeamType.Common1,
                2 => TeamType.Common2,
                3 => TeamType.Common3,
                4 => TeamType.Common4,
            };
            ShowDisplay();
        }

        public void InitEvent()
        {
            if (!GameEntry.Event.Check(TeamChangeEventArgs.EventId, OnTeamChange))
                GameEntry.Event.Subscribe(TeamChangeEventArgs.EventId, OnTeamChange);
        }

        private void OnTeamChange(object sender, GameEventArgs e)
        {
            if (e is TeamChangeEventArgs teamChangeArgs)
            {
                foreach (var (enumBattlePos, displayUid) in teamEntityUid)
                {
                    Game.GameEntry.Entity.HideEntity(displayUid);
                }

                teamEntityUid.Clear();
                ShowDisplay();
            }
        }

        protected override void OnAttached(EntityLogic childEntity, Transform parentTransform, object userData)
        {
            base.OnAttached(childEntity, parentTransform, userData);
            int buildingTeamIndex = m_BuildingModule.BuildingId % 100;
            if (eIdDisplay != null)
            {
                var disPlayEntity = childEntity.CachedTransform;
                if (disPlayEntity != null)
                {
                    Transform ground = disPlayEntity.Find($"xiaoduiGround_{buildingTeamIndex}");
                    bool isUnlock = m_BuildingModule.LEVEL >= 1;
                    if (ground != null)
                    {
                        // bool isUnlock = ToolScriptExtend.GetDemandUnlock(m_BuildingModule.buildingCfg.build_demand[0]);
                        ground.gameObject.SetActive(isUnlock);
                    }

                    if (buildingTeamIndex == 4)
                    {
                        GameObject unLockObj = disPlayEntity.transform.Find("xiaoduiGround_xuxian").gameObject;
                        if (unLockObj != null)
                        {
                            unLockObj.SetActive(!isUnlock);
                        }
                    }
                }
            }
        }

        private void ShowDisplay()
        {
            //初始化英雄形象
            m_TeamDic = GameEntry.LogicData.TeamData.GetTeamDic(curTeamType);
            if (eIdDisplay != null)
            {
                foreach (var (pos, heroId) in m_TeamDic)
                {
                    var battleRole = GameEntry.LDLTable.GetTableById<battle_role>(heroId);
                    var heroCfg = GameEntry.LDLTable.GetTableById<hero_config>(heroId);
                    ED_BuildingTeamHero data = new ED_BuildingTeamHero(Game.GameEntry.Entity.GenerateSerialId(),
                        heroCfg.bullet,
                        eIdDisplay.Value, $"team_{(int)pos}");
                    
                    int displayUid =
                        GameEntry.Entity.ShowCommonDisplay(battleRole.res_location, data, typeof(EL_BuildingTeamHero));
                    teamEntityUid[pos] = displayUid;

                }
            }
        }

        public int GetHeroCnt()
        {
            return m_TeamDic.Count;
        }
        
        public List<EL_BuildingTeamHero> GetCanAtkHero()
        {
            List<EL_BuildingTeamHero> list = new List<EL_BuildingTeamHero>();
            foreach (var item in teamEntityUid)
            {
                var entity = GameEntry.Entity.GetEntity(item.Value);
                if (entity != null && entity.Logic is EL_BuildingTeamHero hero)
                {
                    if (hero.CanAtk())
                    {
                        list.Add(hero);
                    }
                }
            }

            return list;
        }
    }
}