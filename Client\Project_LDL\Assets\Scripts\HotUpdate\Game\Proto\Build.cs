// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: build.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Build {

  /// <summary>Holder for reflection information generated from build.proto</summary>
  public static partial class BuildReflection {

    #region Descriptor
    /// <summary>File descriptor for build.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static BuildReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "CgtidWlsZC5wcm90bxIFYnVpbGQaDWFydGljbGUucHJvdG8iQgoFQnVpbGQS",
            "DwoHYnVpbGRObxgBIAEoDRISCgpidWlsZExldmVsGAIgASgNEgkKAXgYAyAB",
            "KA0SCQoBeRgEIAEoDSIiCgdTb2xkaWVyEgoKAmlkGAEgASgNEgsKA251bRgC",
            "IAEoDSL0AQoJUXVldWVBcmdzEg8KB2J1aWxkTm8YASABKA0SEgoKYnVpbGRM",
            "ZXZlbBgCIAEoDRISCgpyZXNvdXJjZU5vGAMgASgNEhMKC3Jlc291cmNlTnVt",
            "GAQgASgNEhEKCXNvbGRpZXJObxgFIAEoDRISCgpzb2xkaWVyTnVtGAYgASgN",
            "EhUKDWVxdWlwbWVudENvZGUYByABKA0SFAoMZXF1aXBtZW50TnVtGAggASgN",
            "Eg8KB3RlY2hfaWQYCSABKA0SEgoKdGVjaF9sZXZlbBgKIAEoDRIgCghzb2xk",
            "aWVycxgLIAMoCzIOLmJ1aWxkLlNvbGRpZXIirAEKBVF1ZXVlEhAKCHF1ZXVl",
            "VUlEGAEgASgNEhEKCXF1ZXVlVHlwZRgCIAEoDRIPCgdidWlsZE5vGAMgASgN",
            "EgwKBGhlbHAYBCABKA0SEwoLY3JlYXRlX3RpbWUYBSABKAQSEwoLZmluaXNo",
            "X3RpbWUYBiABKAQSFwoPYWNjZWxlcmF0ZV90aW1lGAcgASgEEhwKAnFhGAgg",
            "ASgLMhAuYnVpbGQuUXVldWVBcmdzIl4KEEJ1aWxkUXVldWVSZXN1bHQSGwoF",
            "YnVpbGQYASABKAsyDC5idWlsZC5CdWlsZBIbCgVxdWV1ZRgCIAEoCzIMLmJ1",
            "aWxkLlF1ZXVlEhAKCHF1ZXVlVUlEGAMgASgNIi0KDkJ1aWxkQ3JlYXRlUmVx",
            "EhsKBWJ1aWxkGAEgASgLMgwuYnVpbGQuQnVpbGQiOgoPQnVpbGRDcmVhdGVS",
            "ZXNwEicKBnJlc3VsdBgBIAEoCzIXLmJ1aWxkLkJ1aWxkUXVldWVSZXN1bHQi",
            "SwoPQnVpbGRVcGdyYWRlUmVxEg8KB2J1aWxkTm8YASABKA0SJwoLdXBncmFk",
            "ZVR5cGUYAiABKA4yEi5idWlsZC5VcGdyYWRlVHlwZSJKChBCdWlsZFVwZ3Jh",
            "ZGVSZXNwEicKBnJlc3VsdBgBIAEoCzIXLmJ1aWxkLkJ1aWxkUXVldWVSZXN1",
            "bHQSDQoFaW5kZXgYAiABKA0iKwoMQnVpbGRNb3ZlUmVxEhsKBWJ1aWxkGAEg",
            "ASgLMgwuYnVpbGQuQnVpbGQiLAoNQnVpbGRNb3ZlUmVzcBIbCgVidWlsZBgB",
            "IAEoCzIMLmJ1aWxkLkJ1aWxkIjgKE0J1aWxkUXVldWVGaW5pc2hSZXESDwoH",
            "YnVpbGRObxgBIAEoDRIQCghxdWV1ZVVJRBgCIAEoDSI/ChRCdWlsZFF1ZXVl",
            "RmluaXNoUmVzcBInCgZyZXN1bHQYASABKAsyFy5idWlsZC5CdWlsZFF1ZXVl",
            "UmVzdWx0Io8BChdCdWlsZFF1ZXVlQWNjZWxlcmF0ZVJlcRIPCgdidWlsZE5v",
            "GAEgASgNEhAKCHF1ZXVlVUlEGAIgASgNEiIKCGFydGljbGVzGAMgAygLMhAu",
            "YXJ0aWNsZS5BcnRpY2xlEi0KDmFjY2VsZXJhdGVUeXBlGAQgASgOMhUuYnVp",
            "bGQuQWNjZWxlcmF0ZVR5cGUiVgoYQnVpbGRRdWV1ZUFjY2VsZXJhdGVSZXNw",
            "Eg8KB2J1aWxkTm8YASABKA0SEAoIcXVldWVVSUQYAiABKA0SFwoPYWNjZWxl",
            "cmF0ZV90aW1lGAMgASgEIjYKEUJ1aWxkUXVldWVIZWxwUmVxEg8KB2J1aWxk",
            "Tm8YASABKA0SEAoIcXVldWVVSUQYAiABKA0iNwoSQnVpbGRRdWV1ZUhlbHBS",
            "ZXNwEg8KB2J1aWxkTm8YASABKA0SEAoIcXVldWVVSUQYAiABKA0iVQoXUHVz",
            "aEJ1aWxkSGVscEFjY2VsZXJhdGUSDwoHYnVpbGRObxgBIAEoDRIQCghxdWV1",
            "ZVVJRBgCIAEoDRIXCg9hY2NlbGVyYXRlX3RpbWUYAyABKAQiKQoJUHVzaEJ1",
            "aWxkEhwKBmJ1aWxkcxgBIAMoCzIMLmJ1aWxkLkJ1aWxkKswBCglRdWV1ZVR5",
            "cGUSDQoJQnVpbGRfTmlsEAASEAoMQnVpbGRfQ3JlYXRlEAESEQoNQnVpbGRf",
            "VXBncmFkZRACEhIKDkJ1aWxkX1Jlc291cmNlEAMSFgoSQnVpbGRfRXF1aXBQ",
            "cm9kdWNlEAQSFwoTQnVpbGRfU29sZGllckNyZWF0ZRAFEhoKFkJ1aWxkX1Nv",
            "bGRpZXJQcm9tb3Rpb24QBhIaChZCdWlsZF9Tb2xkaWVyVHJlYXRtZW50EAcS",
            "DgoKQnVpbGRfVGVjaBAIKlEKDkFjY2VsZXJhdGVUeXBlEhIKDkFjY2VsZXJh",
            "dGVfTmlsEAASEwoPQWNjZWxlcmF0ZV9JdGVtEAESFgoSQWNjZWxlcmF0ZV9E",
            "aWFtb25kEAIqRwoLVXBncmFkZVR5cGUSDwoLVXBncmFkZV9OaWwQABISCg5V",
            "cGdyYWRlX05vcm1hbBABEhMKD1VwZ3JhZGVfRGlhbW9uZBACQhhaFnNlcnZl",
            "ci9hcGkvcGIvcGJfYnVpbGRiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { global::Article.ArticleReflection.Descriptor, },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Build.QueueType), typeof(global::Build.AccelerateType), typeof(global::Build.UpgradeType), }, null, new pbr::GeneratedClrTypeInfo[] {
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.Build), global::Build.Build.Parser, new[]{ "BuildNo", "BuildLevel", "X", "Y" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.Soldier), global::Build.Soldier.Parser, new[]{ "Id", "Num" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.QueueArgs), global::Build.QueueArgs.Parser, new[]{ "BuildNo", "BuildLevel", "ResourceNo", "ResourceNum", "SoldierNo", "SoldierNum", "EquipmentCode", "EquipmentNum", "TechId", "TechLevel", "Soldiers" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.Queue), global::Build.Queue.Parser, new[]{ "QueueUID", "QueueType", "BuildNo", "Help", "CreateTime", "FinishTime", "AccelerateTime", "Qa" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildQueueResult), global::Build.BuildQueueResult.Parser, new[]{ "Build", "Queue", "QueueUID" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildCreateReq), global::Build.BuildCreateReq.Parser, new[]{ "Build" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildCreateResp), global::Build.BuildCreateResp.Parser, new[]{ "Result" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildUpgradeReq), global::Build.BuildUpgradeReq.Parser, new[]{ "BuildNo", "UpgradeType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildUpgradeResp), global::Build.BuildUpgradeResp.Parser, new[]{ "Result", "Index" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildMoveReq), global::Build.BuildMoveReq.Parser, new[]{ "Build" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildMoveResp), global::Build.BuildMoveResp.Parser, new[]{ "Build" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildQueueFinishReq), global::Build.BuildQueueFinishReq.Parser, new[]{ "BuildNo", "QueueUID" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildQueueFinishResp), global::Build.BuildQueueFinishResp.Parser, new[]{ "Result" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildQueueAccelerateReq), global::Build.BuildQueueAccelerateReq.Parser, new[]{ "BuildNo", "QueueUID", "Articles", "AccelerateType" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildQueueAccelerateResp), global::Build.BuildQueueAccelerateResp.Parser, new[]{ "BuildNo", "QueueUID", "AccelerateTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildQueueHelpReq), global::Build.BuildQueueHelpReq.Parser, new[]{ "BuildNo", "QueueUID" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.BuildQueueHelpResp), global::Build.BuildQueueHelpResp.Parser, new[]{ "BuildNo", "QueueUID" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.PushBuildHelpAccelerate), global::Build.PushBuildHelpAccelerate.Parser, new[]{ "BuildNo", "QueueUID", "AccelerateTime" }, null, null, null, null),
            new pbr::GeneratedClrTypeInfo(typeof(global::Build.PushBuild), global::Build.PushBuild.Parser, new[]{ "Builds" }, null, null, null, null)
          }));
    }
    #endregion

  }
  #region Enums
  public enum QueueType {
    /// <summary>
    /// 占位
    /// </summary>
    [pbr::OriginalName("Build_Nil")] BuildNil = 0,
    /// <summary>
    ///建造队列
    /// </summary>
    [pbr::OriginalName("Build_Create")] BuildCreate = 1,
    /// <summary>
    ///升级队列
    /// </summary>
    [pbr::OriginalName("Build_Upgrade")] BuildUpgrade = 2,
    /// <summary>
    ///资源队列
    /// </summary>
    [pbr::OriginalName("Build_Resource")] BuildResource = 3,
    /// <summary>
    ///装备制造
    /// </summary>
    [pbr::OriginalName("Build_EquipProduce")] BuildEquipProduce = 4,
    /// <summary>
    ///士兵制造
    /// </summary>
    [pbr::OriginalName("Build_SoldierCreate")] BuildSoldierCreate = 5,
    /// <summary>
    ///士兵晋级
    /// </summary>
    [pbr::OriginalName("Build_SoldierPromotion")] BuildSoldierPromotion = 6,
    /// <summary>
    ///士兵治疗
    /// </summary>
    [pbr::OriginalName("Build_SoldierTreatment")] BuildSoldierTreatment = 7,
    /// <summary>
    ///科技
    /// </summary>
    [pbr::OriginalName("Build_Tech")] BuildTech = 8,
  }

  public enum AccelerateType {
    /// <summary>
    /// 占位
    /// </summary>
    [pbr::OriginalName("Accelerate_Nil")] AccelerateNil = 0,
    /// <summary>
    ///使用道具
    /// </summary>
    [pbr::OriginalName("Accelerate_Item")] AccelerateItem = 1,
    /// <summary>
    ///立刻完成,使用钻石购买剩余时间
    /// </summary>
    [pbr::OriginalName("Accelerate_Diamond")] AccelerateDiamond = 2,
  }

  public enum UpgradeType {
    /// <summary>
    /// 占位
    /// </summary>
    [pbr::OriginalName("Upgrade_Nil")] UpgradeNil = 0,
    /// <summary>
    ///普通升级
    /// </summary>
    [pbr::OriginalName("Upgrade_Normal")] UpgradeNormal = 1,
    /// <summary>
    ///钻石升级
    /// </summary>
    [pbr::OriginalName("Upgrade_Diamond")] UpgradeDiamond = 2,
  }

  #endregion

  #region Messages
  /// <summary>
  ///建筑结构
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Build : pb::IMessage<Build>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Build> _parser = new pb::MessageParser<Build>(() => new Build());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Build> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[0]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Build() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Build(Build other) : this() {
      buildNo_ = other.buildNo_;
      buildLevel_ = other.buildLevel_;
      x_ = other.x_;
      y_ = other.y_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Build Clone() {
      return new Build(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///build_config配置表编号,唯一标识
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "buildLevel" field.</summary>
    public const int BuildLevelFieldNumber = 2;
    private uint buildLevel_;
    /// <summary>
    ///建筑等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildLevel {
      get { return buildLevel_; }
      set {
        buildLevel_ = value;
      }
    }

    /// <summary>Field number for the "x" field.</summary>
    public const int XFieldNumber = 3;
    private uint x_;
    /// <summary>
    ///x轴坐标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint X {
      get { return x_; }
      set {
        x_ = value;
      }
    }

    /// <summary>Field number for the "y" field.</summary>
    public const int YFieldNumber = 4;
    private uint y_;
    /// <summary>
    ///y轴坐标
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Y {
      get { return y_; }
      set {
        y_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Build);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Build other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (BuildLevel != other.BuildLevel) return false;
      if (X != other.X) return false;
      if (Y != other.Y) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (BuildLevel != 0) hash ^= BuildLevel.GetHashCode();
      if (X != 0) hash ^= X.GetHashCode();
      if (Y != 0) hash ^= Y.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (BuildLevel != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(BuildLevel);
      }
      if (X != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(X);
      }
      if (Y != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(Y);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (BuildLevel != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(BuildLevel);
      }
      if (X != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(X);
      }
      if (Y != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(Y);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (BuildLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildLevel);
      }
      if (X != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(X);
      }
      if (Y != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Y);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Build other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.BuildLevel != 0) {
        BuildLevel = other.BuildLevel;
      }
      if (other.X != 0) {
        X = other.X;
      }
      if (other.Y != 0) {
        Y = other.Y;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            BuildLevel = input.ReadUInt32();
            break;
          }
          case 24: {
            X = input.ReadUInt32();
            break;
          }
          case 32: {
            Y = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            BuildLevel = input.ReadUInt32();
            break;
          }
          case 24: {
            X = input.ReadUInt32();
            break;
          }
          case 32: {
            Y = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Soldier : pb::IMessage<Soldier>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Soldier> _parser = new pb::MessageParser<Soldier>(() => new Soldier());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Soldier> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[1]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Soldier() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Soldier(Soldier other) : this() {
      id_ = other.id_;
      num_ = other.num_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Soldier Clone() {
      return new Soldier(this);
    }

    /// <summary>Field number for the "id" field.</summary>
    public const int IdFieldNumber = 1;
    private uint id_;
    /// <summary>
    /// build_soldier
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Id {
      get { return id_; }
      set {
        id_ = value;
      }
    }

    /// <summary>Field number for the "num" field.</summary>
    public const int NumFieldNumber = 2;
    private uint num_;
    /// <summary>
    /// 数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Num {
      get { return num_; }
      set {
        num_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Soldier);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Soldier other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (Id != other.Id) return false;
      if (Num != other.Num) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (Id != 0) hash ^= Id.GetHashCode();
      if (Num != 0) hash ^= Num.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Id);
      }
      if (Num != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Num);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (Id != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(Id);
      }
      if (Num != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Num);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (Id != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Id);
      }
      if (Num != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Num);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Soldier other) {
      if (other == null) {
        return;
      }
      if (other.Id != 0) {
        Id = other.Id;
      }
      if (other.Num != 0) {
        Num = other.Num;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            Id = input.ReadUInt32();
            break;
          }
          case 16: {
            Num = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            Id = input.ReadUInt32();
            break;
          }
          case 16: {
            Num = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///队列数据结构
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class QueueArgs : pb::IMessage<QueueArgs>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<QueueArgs> _parser = new pb::MessageParser<QueueArgs>(() => new QueueArgs());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<QueueArgs> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[2]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueueArgs() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueueArgs(QueueArgs other) : this() {
      buildNo_ = other.buildNo_;
      buildLevel_ = other.buildLevel_;
      resourceNo_ = other.resourceNo_;
      resourceNum_ = other.resourceNum_;
      soldierNo_ = other.soldierNo_;
      soldierNum_ = other.soldierNum_;
      equipmentCode_ = other.equipmentCode_;
      equipmentNum_ = other.equipmentNum_;
      techId_ = other.techId_;
      techLevel_ = other.techLevel_;
      soldiers_ = other.soldiers_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public QueueArgs Clone() {
      return new QueueArgs(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑与升级队列数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "buildLevel" field.</summary>
    public const int BuildLevelFieldNumber = 2;
    private uint buildLevel_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildLevel {
      get { return buildLevel_; }
      set {
        buildLevel_ = value;
      }
    }

    /// <summary>Field number for the "resourceNo" field.</summary>
    public const int ResourceNoFieldNumber = 3;
    private uint resourceNo_;
    /// <summary>
    ///资源队列数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ResourceNo {
      get { return resourceNo_; }
      set {
        resourceNo_ = value;
      }
    }

    /// <summary>Field number for the "resourceNum" field.</summary>
    public const int ResourceNumFieldNumber = 4;
    private uint resourceNum_;
    /// <summary>
    ///资源数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint ResourceNum {
      get { return resourceNum_; }
      set {
        resourceNum_ = value;
      }
    }

    /// <summary>Field number for the "soldierNo" field.</summary>
    public const int SoldierNoFieldNumber = 5;
    private uint soldierNo_;
    /// <summary>
    ///士兵制造与晋级与治疗队列数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint SoldierNo {
      get { return soldierNo_; }
      set {
        soldierNo_ = value;
      }
    }

    /// <summary>Field number for the "soldierNum" field.</summary>
    public const int SoldierNumFieldNumber = 6;
    private uint soldierNum_;
    /// <summary>
    ///士兵数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint SoldierNum {
      get { return soldierNum_; }
      set {
        soldierNum_ = value;
      }
    }

    /// <summary>Field number for the "equipmentCode" field.</summary>
    public const int EquipmentCodeFieldNumber = 7;
    private uint equipmentCode_;
    /// <summary>
    ///装备编号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint EquipmentCode {
      get { return equipmentCode_; }
      set {
        equipmentCode_ = value;
      }
    }

    /// <summary>Field number for the "equipmentNum" field.</summary>
    public const int EquipmentNumFieldNumber = 8;
    private uint equipmentNum_;
    /// <summary>
    ///装备数量
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint EquipmentNum {
      get { return equipmentNum_; }
      set {
        equipmentNum_ = value;
      }
    }

    /// <summary>Field number for the "tech_id" field.</summary>
    public const int TechIdFieldNumber = 9;
    private uint techId_;
    /// <summary>
    /// 科技组id
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint TechId {
      get { return techId_; }
      set {
        techId_ = value;
      }
    }

    /// <summary>Field number for the "tech_level" field.</summary>
    public const int TechLevelFieldNumber = 10;
    private uint techLevel_;
    /// <summary>
    /// 科技等级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint TechLevel {
      get { return techLevel_; }
      set {
        techLevel_ = value;
      }
    }

    /// <summary>Field number for the "soldiers" field.</summary>
    public const int SoldiersFieldNumber = 11;
    private static readonly pb::FieldCodec<global::Build.Soldier> _repeated_soldiers_codec
        = pb::FieldCodec.ForMessage(90, global::Build.Soldier.Parser);
    private readonly pbc::RepeatedField<global::Build.Soldier> soldiers_ = new pbc::RepeatedField<global::Build.Soldier>();
    /// <summary>
    /// 治疗队列数据
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Build.Soldier> Soldiers {
      get { return soldiers_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as QueueArgs);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(QueueArgs other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (BuildLevel != other.BuildLevel) return false;
      if (ResourceNo != other.ResourceNo) return false;
      if (ResourceNum != other.ResourceNum) return false;
      if (SoldierNo != other.SoldierNo) return false;
      if (SoldierNum != other.SoldierNum) return false;
      if (EquipmentCode != other.EquipmentCode) return false;
      if (EquipmentNum != other.EquipmentNum) return false;
      if (TechId != other.TechId) return false;
      if (TechLevel != other.TechLevel) return false;
      if(!soldiers_.Equals(other.soldiers_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (BuildLevel != 0) hash ^= BuildLevel.GetHashCode();
      if (ResourceNo != 0) hash ^= ResourceNo.GetHashCode();
      if (ResourceNum != 0) hash ^= ResourceNum.GetHashCode();
      if (SoldierNo != 0) hash ^= SoldierNo.GetHashCode();
      if (SoldierNum != 0) hash ^= SoldierNum.GetHashCode();
      if (EquipmentCode != 0) hash ^= EquipmentCode.GetHashCode();
      if (EquipmentNum != 0) hash ^= EquipmentNum.GetHashCode();
      if (TechId != 0) hash ^= TechId.GetHashCode();
      if (TechLevel != 0) hash ^= TechLevel.GetHashCode();
      hash ^= soldiers_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (BuildLevel != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(BuildLevel);
      }
      if (ResourceNo != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ResourceNo);
      }
      if (ResourceNum != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(ResourceNum);
      }
      if (SoldierNo != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(SoldierNo);
      }
      if (SoldierNum != 0) {
        output.WriteRawTag(48);
        output.WriteUInt32(SoldierNum);
      }
      if (EquipmentCode != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(EquipmentCode);
      }
      if (EquipmentNum != 0) {
        output.WriteRawTag(64);
        output.WriteUInt32(EquipmentNum);
      }
      if (TechId != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(TechId);
      }
      if (TechLevel != 0) {
        output.WriteRawTag(80);
        output.WriteUInt32(TechLevel);
      }
      soldiers_.WriteTo(output, _repeated_soldiers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (BuildLevel != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(BuildLevel);
      }
      if (ResourceNo != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(ResourceNo);
      }
      if (ResourceNum != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(ResourceNum);
      }
      if (SoldierNo != 0) {
        output.WriteRawTag(40);
        output.WriteUInt32(SoldierNo);
      }
      if (SoldierNum != 0) {
        output.WriteRawTag(48);
        output.WriteUInt32(SoldierNum);
      }
      if (EquipmentCode != 0) {
        output.WriteRawTag(56);
        output.WriteUInt32(EquipmentCode);
      }
      if (EquipmentNum != 0) {
        output.WriteRawTag(64);
        output.WriteUInt32(EquipmentNum);
      }
      if (TechId != 0) {
        output.WriteRawTag(72);
        output.WriteUInt32(TechId);
      }
      if (TechLevel != 0) {
        output.WriteRawTag(80);
        output.WriteUInt32(TechLevel);
      }
      soldiers_.WriteTo(ref output, _repeated_soldiers_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (BuildLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildLevel);
      }
      if (ResourceNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ResourceNo);
      }
      if (ResourceNum != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(ResourceNum);
      }
      if (SoldierNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(SoldierNo);
      }
      if (SoldierNum != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(SoldierNum);
      }
      if (EquipmentCode != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(EquipmentCode);
      }
      if (EquipmentNum != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(EquipmentNum);
      }
      if (TechId != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(TechId);
      }
      if (TechLevel != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(TechLevel);
      }
      size += soldiers_.CalculateSize(_repeated_soldiers_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(QueueArgs other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.BuildLevel != 0) {
        BuildLevel = other.BuildLevel;
      }
      if (other.ResourceNo != 0) {
        ResourceNo = other.ResourceNo;
      }
      if (other.ResourceNum != 0) {
        ResourceNum = other.ResourceNum;
      }
      if (other.SoldierNo != 0) {
        SoldierNo = other.SoldierNo;
      }
      if (other.SoldierNum != 0) {
        SoldierNum = other.SoldierNum;
      }
      if (other.EquipmentCode != 0) {
        EquipmentCode = other.EquipmentCode;
      }
      if (other.EquipmentNum != 0) {
        EquipmentNum = other.EquipmentNum;
      }
      if (other.TechId != 0) {
        TechId = other.TechId;
      }
      if (other.TechLevel != 0) {
        TechLevel = other.TechLevel;
      }
      soldiers_.Add(other.soldiers_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            BuildLevel = input.ReadUInt32();
            break;
          }
          case 24: {
            ResourceNo = input.ReadUInt32();
            break;
          }
          case 32: {
            ResourceNum = input.ReadUInt32();
            break;
          }
          case 40: {
            SoldierNo = input.ReadUInt32();
            break;
          }
          case 48: {
            SoldierNum = input.ReadUInt32();
            break;
          }
          case 56: {
            EquipmentCode = input.ReadUInt32();
            break;
          }
          case 64: {
            EquipmentNum = input.ReadUInt32();
            break;
          }
          case 72: {
            TechId = input.ReadUInt32();
            break;
          }
          case 80: {
            TechLevel = input.ReadUInt32();
            break;
          }
          case 90: {
            soldiers_.AddEntriesFrom(input, _repeated_soldiers_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            BuildLevel = input.ReadUInt32();
            break;
          }
          case 24: {
            ResourceNo = input.ReadUInt32();
            break;
          }
          case 32: {
            ResourceNum = input.ReadUInt32();
            break;
          }
          case 40: {
            SoldierNo = input.ReadUInt32();
            break;
          }
          case 48: {
            SoldierNum = input.ReadUInt32();
            break;
          }
          case 56: {
            EquipmentCode = input.ReadUInt32();
            break;
          }
          case 64: {
            EquipmentNum = input.ReadUInt32();
            break;
          }
          case 72: {
            TechId = input.ReadUInt32();
            break;
          }
          case 80: {
            TechLevel = input.ReadUInt32();
            break;
          }
          case 90: {
            soldiers_.AddEntriesFrom(ref input, _repeated_soldiers_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///队列结构
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class Queue : pb::IMessage<Queue>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<Queue> _parser = new pb::MessageParser<Queue>(() => new Queue());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<Queue> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[3]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Queue() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Queue(Queue other) : this() {
      queueUID_ = other.queueUID_;
      queueType_ = other.queueType_;
      buildNo_ = other.buildNo_;
      help_ = other.help_;
      createTime_ = other.createTime_;
      finishTime_ = other.finishTime_;
      accelerateTime_ = other.accelerateTime_;
      qa_ = other.qa_ != null ? other.qa_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public Queue Clone() {
      return new Queue(this);
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 1;
    private uint queueUID_;
    /// <summary>
    ///队列Uid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    /// <summary>Field number for the "queueType" field.</summary>
    public const int QueueTypeFieldNumber = 2;
    private uint queueType_;
    /// <summary>
    ///队列类型 1:建造队列 2:升级队列 3:资源队列
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueType {
      get { return queueType_; }
      set {
        queueType_ = value;
      }
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 3;
    private uint buildNo_;
    /// <summary>
    ///绑定某个建筑上面
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "help" field.</summary>
    public const int HelpFieldNumber = 4;
    private uint help_;
    /// <summary>
    ///是否帮助 0:未帮助 1:已帮助
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Help {
      get { return help_; }
      set {
        help_ = value;
      }
    }

    /// <summary>Field number for the "create_time" field.</summary>
    public const int CreateTimeFieldNumber = 5;
    private ulong createTime_;
    /// <summary>
    /// 创建时间戳,秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong CreateTime {
      get { return createTime_; }
      set {
        createTime_ = value;
      }
    }

    /// <summary>Field number for the "finish_time" field.</summary>
    public const int FinishTimeFieldNumber = 6;
    private ulong finishTime_;
    /// <summary>
    /// 完成时间戳,秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong FinishTime {
      get { return finishTime_; }
      set {
        finishTime_ = value;
      }
    }

    /// <summary>Field number for the "accelerate_time" field.</summary>
    public const int AccelerateTimeFieldNumber = 7;
    private ulong accelerateTime_;
    /// <summary>
    ///加速时间,秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong AccelerateTime {
      get { return accelerateTime_; }
      set {
        accelerateTime_ = value;
      }
    }

    /// <summary>Field number for the "qa" field.</summary>
    public const int QaFieldNumber = 8;
    private global::Build.QueueArgs qa_;
    /// <summary>
    ///队列参数
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.QueueArgs Qa {
      get { return qa_; }
      set {
        qa_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as Queue);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(Queue other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (QueueUID != other.QueueUID) return false;
      if (QueueType != other.QueueType) return false;
      if (BuildNo != other.BuildNo) return false;
      if (Help != other.Help) return false;
      if (CreateTime != other.CreateTime) return false;
      if (FinishTime != other.FinishTime) return false;
      if (AccelerateTime != other.AccelerateTime) return false;
      if (!object.Equals(Qa, other.Qa)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      if (QueueType != 0) hash ^= QueueType.GetHashCode();
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (Help != 0) hash ^= Help.GetHashCode();
      if (CreateTime != 0UL) hash ^= CreateTime.GetHashCode();
      if (FinishTime != 0UL) hash ^= FinishTime.GetHashCode();
      if (AccelerateTime != 0UL) hash ^= AccelerateTime.GetHashCode();
      if (qa_ != null) hash ^= Qa.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (QueueUID != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(QueueUID);
      }
      if (QueueType != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueType);
      }
      if (BuildNo != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(BuildNo);
      }
      if (Help != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(Help);
      }
      if (CreateTime != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(CreateTime);
      }
      if (FinishTime != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(FinishTime);
      }
      if (AccelerateTime != 0UL) {
        output.WriteRawTag(56);
        output.WriteUInt64(AccelerateTime);
      }
      if (qa_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Qa);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (QueueUID != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(QueueUID);
      }
      if (QueueType != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueType);
      }
      if (BuildNo != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(BuildNo);
      }
      if (Help != 0) {
        output.WriteRawTag(32);
        output.WriteUInt32(Help);
      }
      if (CreateTime != 0UL) {
        output.WriteRawTag(40);
        output.WriteUInt64(CreateTime);
      }
      if (FinishTime != 0UL) {
        output.WriteRawTag(48);
        output.WriteUInt64(FinishTime);
      }
      if (AccelerateTime != 0UL) {
        output.WriteRawTag(56);
        output.WriteUInt64(AccelerateTime);
      }
      if (qa_ != null) {
        output.WriteRawTag(66);
        output.WriteMessage(Qa);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      if (QueueType != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueType);
      }
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (Help != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Help);
      }
      if (CreateTime != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(CreateTime);
      }
      if (FinishTime != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(FinishTime);
      }
      if (AccelerateTime != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(AccelerateTime);
      }
      if (qa_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Qa);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(Queue other) {
      if (other == null) {
        return;
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      if (other.QueueType != 0) {
        QueueType = other.QueueType;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.Help != 0) {
        Help = other.Help;
      }
      if (other.CreateTime != 0UL) {
        CreateTime = other.CreateTime;
      }
      if (other.FinishTime != 0UL) {
        FinishTime = other.FinishTime;
      }
      if (other.AccelerateTime != 0UL) {
        AccelerateTime = other.AccelerateTime;
      }
      if (other.qa_ != null) {
        if (qa_ == null) {
          Qa = new global::Build.QueueArgs();
        }
        Qa.MergeFrom(other.Qa);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueType = input.ReadUInt32();
            break;
          }
          case 24: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 32: {
            Help = input.ReadUInt32();
            break;
          }
          case 40: {
            CreateTime = input.ReadUInt64();
            break;
          }
          case 48: {
            FinishTime = input.ReadUInt64();
            break;
          }
          case 56: {
            AccelerateTime = input.ReadUInt64();
            break;
          }
          case 66: {
            if (qa_ == null) {
              Qa = new global::Build.QueueArgs();
            }
            input.ReadMessage(Qa);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueType = input.ReadUInt32();
            break;
          }
          case 24: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 32: {
            Help = input.ReadUInt32();
            break;
          }
          case 40: {
            CreateTime = input.ReadUInt64();
            break;
          }
          case 48: {
            FinishTime = input.ReadUInt64();
            break;
          }
          case 56: {
            AccelerateTime = input.ReadUInt64();
            break;
          }
          case 66: {
            if (qa_ == null) {
              Qa = new global::Build.QueueArgs();
            }
            input.ReadMessage(Qa);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///队列完成结果
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildQueueResult : pb::IMessage<BuildQueueResult>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildQueueResult> _parser = new pb::MessageParser<BuildQueueResult>(() => new BuildQueueResult());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildQueueResult> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[4]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueResult() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueResult(BuildQueueResult other) : this() {
      build_ = other.build_ != null ? other.build_.Clone() : null;
      queue_ = other.queue_ != null ? other.queue_.Clone() : null;
      queueUID_ = other.queueUID_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueResult Clone() {
      return new BuildQueueResult(this);
    }

    /// <summary>Field number for the "build" field.</summary>
    public const int BuildFieldNumber = 1;
    private global::Build.Build build_;
    /// <summary>
    ///新增或者更新的建筑
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.Build Build {
      get { return build_; }
      set {
        build_ = value;
      }
    }

    /// <summary>Field number for the "queue" field.</summary>
    public const int QueueFieldNumber = 2;
    private global::Build.Queue queue_;
    /// <summary>
    ///新增队列
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.Queue Queue {
      get { return queue_; }
      set {
        queue_ = value;
      }
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 3;
    private uint queueUID_;
    /// <summary>
    ///完成的队列
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildQueueResult);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildQueueResult other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Build, other.Build)) return false;
      if (!object.Equals(Queue, other.Queue)) return false;
      if (QueueUID != other.QueueUID) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (build_ != null) hash ^= Build.GetHashCode();
      if (queue_ != null) hash ^= Queue.GetHashCode();
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (queue_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Queue);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (queue_ != null) {
        output.WriteRawTag(18);
        output.WriteMessage(Queue);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(24);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (build_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Build);
      }
      if (queue_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Queue);
      }
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildQueueResult other) {
      if (other == null) {
        return;
      }
      if (other.build_ != null) {
        if (build_ == null) {
          Build = new global::Build.Build();
        }
        Build.MergeFrom(other.Build);
      }
      if (other.queue_ != null) {
        if (queue_ == null) {
          Queue = new global::Build.Queue();
        }
        Queue.MergeFrom(other.Queue);
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
          case 18: {
            if (queue_ == null) {
              Queue = new global::Build.Queue();
            }
            input.ReadMessage(Queue);
            break;
          }
          case 24: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
          case 18: {
            if (queue_ == null) {
              Queue = new global::Build.Queue();
            }
            input.ReadMessage(Queue);
            break;
          }
          case 24: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///请求建造建筑
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildCreateReq : pb::IMessage<BuildCreateReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildCreateReq> _parser = new pb::MessageParser<BuildCreateReq>(() => new BuildCreateReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildCreateReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[5]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildCreateReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildCreateReq(BuildCreateReq other) : this() {
      build_ = other.build_ != null ? other.build_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildCreateReq Clone() {
      return new BuildCreateReq(this);
    }

    /// <summary>Field number for the "build" field.</summary>
    public const int BuildFieldNumber = 1;
    private global::Build.Build build_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.Build Build {
      get { return build_; }
      set {
        build_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildCreateReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildCreateReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Build, other.Build)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (build_ != null) hash ^= Build.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (build_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Build);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildCreateReq other) {
      if (other == null) {
        return;
      }
      if (other.build_ != null) {
        if (build_ == null) {
          Build = new global::Build.Build();
        }
        Build.MergeFrom(other.Build);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
        }
      }
    }
    #endif

  }

  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildCreateResp : pb::IMessage<BuildCreateResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildCreateResp> _parser = new pb::MessageParser<BuildCreateResp>(() => new BuildCreateResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildCreateResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[6]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildCreateResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildCreateResp(BuildCreateResp other) : this() {
      result_ = other.result_ != null ? other.result_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildCreateResp Clone() {
      return new BuildCreateResp(this);
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 1;
    private global::Build.BuildQueueResult result_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.BuildQueueResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildCreateResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildCreateResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Result, other.Result)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (result_ != null) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildCreateResp other) {
      if (other == null) {
        return;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::Build.BuildQueueResult();
        }
        Result.MergeFrom(other.Result);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///请求升级建筑
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildUpgradeReq : pb::IMessage<BuildUpgradeReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildUpgradeReq> _parser = new pb::MessageParser<BuildUpgradeReq>(() => new BuildUpgradeReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildUpgradeReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[7]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildUpgradeReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildUpgradeReq(BuildUpgradeReq other) : this() {
      buildNo_ = other.buildNo_;
      upgradeType_ = other.upgradeType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildUpgradeReq Clone() {
      return new BuildUpgradeReq(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///配置表编号
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "upgradeType" field.</summary>
    public const int UpgradeTypeFieldNumber = 2;
    private global::Build.UpgradeType upgradeType_ = global::Build.UpgradeType.UpgradeNil;
    /// <summary>
    ///1:普通升级 2:钻石升级
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.UpgradeType UpgradeType {
      get { return upgradeType_; }
      set {
        upgradeType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildUpgradeReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildUpgradeReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (UpgradeType != other.UpgradeType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (UpgradeType != global::Build.UpgradeType.UpgradeNil) hash ^= UpgradeType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (UpgradeType != global::Build.UpgradeType.UpgradeNil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) UpgradeType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (UpgradeType != global::Build.UpgradeType.UpgradeNil) {
        output.WriteRawTag(16);
        output.WriteEnum((int) UpgradeType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (UpgradeType != global::Build.UpgradeType.UpgradeNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) UpgradeType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildUpgradeReq other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.UpgradeType != global::Build.UpgradeType.UpgradeNil) {
        UpgradeType = other.UpgradeType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            UpgradeType = (global::Build.UpgradeType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            UpgradeType = (global::Build.UpgradeType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///返回升级建筑
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildUpgradeResp : pb::IMessage<BuildUpgradeResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildUpgradeResp> _parser = new pb::MessageParser<BuildUpgradeResp>(() => new BuildUpgradeResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildUpgradeResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[8]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildUpgradeResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildUpgradeResp(BuildUpgradeResp other) : this() {
      result_ = other.result_ != null ? other.result_.Clone() : null;
      index_ = other.index_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildUpgradeResp Clone() {
      return new BuildUpgradeResp(this);
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 1;
    private global::Build.BuildQueueResult result_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.BuildQueueResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    /// <summary>Field number for the "index" field.</summary>
    public const int IndexFieldNumber = 2;
    private uint index_;
    /// <summary>
    ///队列索引
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint Index {
      get { return index_; }
      set {
        index_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildUpgradeResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildUpgradeResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Result, other.Result)) return false;
      if (Index != other.Index) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (result_ != null) hash ^= Result.GetHashCode();
      if (Index != 0) hash ^= Index.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (Index != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (Index != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(Index);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (Index != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(Index);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildUpgradeResp other) {
      if (other == null) {
        return;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::Build.BuildQueueResult();
        }
        Result.MergeFrom(other.Result);
      }
      if (other.Index != 0) {
        Index = other.Index;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
          case 16: {
            Index = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
          case 16: {
            Index = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///请求移动建筑
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildMoveReq : pb::IMessage<BuildMoveReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildMoveReq> _parser = new pb::MessageParser<BuildMoveReq>(() => new BuildMoveReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildMoveReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[9]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildMoveReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildMoveReq(BuildMoveReq other) : this() {
      build_ = other.build_ != null ? other.build_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildMoveReq Clone() {
      return new BuildMoveReq(this);
    }

    /// <summary>Field number for the "build" field.</summary>
    public const int BuildFieldNumber = 1;
    private global::Build.Build build_;
    /// <summary>
    ///建筑信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.Build Build {
      get { return build_; }
      set {
        build_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildMoveReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildMoveReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Build, other.Build)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (build_ != null) hash ^= Build.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (build_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Build);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildMoveReq other) {
      if (other == null) {
        return;
      }
      if (other.build_ != null) {
        if (build_ == null) {
          Build = new global::Build.Build();
        }
        Build.MergeFrom(other.Build);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///返回移动建筑
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildMoveResp : pb::IMessage<BuildMoveResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildMoveResp> _parser = new pb::MessageParser<BuildMoveResp>(() => new BuildMoveResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildMoveResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[10]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildMoveResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildMoveResp(BuildMoveResp other) : this() {
      build_ = other.build_ != null ? other.build_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildMoveResp Clone() {
      return new BuildMoveResp(this);
    }

    /// <summary>Field number for the "build" field.</summary>
    public const int BuildFieldNumber = 1;
    private global::Build.Build build_;
    /// <summary>
    ///建筑信息
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.Build Build {
      get { return build_; }
      set {
        build_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildMoveResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildMoveResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Build, other.Build)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (build_ != null) hash ^= Build.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (build_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Build);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (build_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Build);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildMoveResp other) {
      if (other == null) {
        return;
      }
      if (other.build_ != null) {
        if (build_ == null) {
          Build = new global::Build.Build();
        }
        Build.MergeFrom(other.Build);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (build_ == null) {
              Build = new global::Build.Build();
            }
            input.ReadMessage(Build);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///请求队列完成
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildQueueFinishReq : pb::IMessage<BuildQueueFinishReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildQueueFinishReq> _parser = new pb::MessageParser<BuildQueueFinishReq>(() => new BuildQueueFinishReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildQueueFinishReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[11]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueFinishReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueFinishReq(BuildQueueFinishReq other) : this() {
      buildNo_ = other.buildNo_;
      queueUID_ = other.queueUID_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueFinishReq Clone() {
      return new BuildQueueFinishReq(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑No
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 2;
    private uint queueUID_;
    /// <summary>
    ///队列Uid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildQueueFinishReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildQueueFinishReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (QueueUID != other.QueueUID) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildQueueFinishReq other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///返回队列完成
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildQueueFinishResp : pb::IMessage<BuildQueueFinishResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildQueueFinishResp> _parser = new pb::MessageParser<BuildQueueFinishResp>(() => new BuildQueueFinishResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildQueueFinishResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[12]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueFinishResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueFinishResp(BuildQueueFinishResp other) : this() {
      result_ = other.result_ != null ? other.result_.Clone() : null;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueFinishResp Clone() {
      return new BuildQueueFinishResp(this);
    }

    /// <summary>Field number for the "result" field.</summary>
    public const int ResultFieldNumber = 1;
    private global::Build.BuildQueueResult result_;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.BuildQueueResult Result {
      get { return result_; }
      set {
        result_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildQueueFinishResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildQueueFinishResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (!object.Equals(Result, other.Result)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (result_ != null) hash ^= Result.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (result_ != null) {
        output.WriteRawTag(10);
        output.WriteMessage(Result);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (result_ != null) {
        size += 1 + pb::CodedOutputStream.ComputeMessageSize(Result);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildQueueFinishResp other) {
      if (other == null) {
        return;
      }
      if (other.result_ != null) {
        if (result_ == null) {
          Result = new global::Build.BuildQueueResult();
        }
        Result.MergeFrom(other.Result);
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            if (result_ == null) {
              Result = new global::Build.BuildQueueResult();
            }
            input.ReadMessage(Result);
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///请求队列加速
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildQueueAccelerateReq : pb::IMessage<BuildQueueAccelerateReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildQueueAccelerateReq> _parser = new pb::MessageParser<BuildQueueAccelerateReq>(() => new BuildQueueAccelerateReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildQueueAccelerateReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[13]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueAccelerateReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueAccelerateReq(BuildQueueAccelerateReq other) : this() {
      buildNo_ = other.buildNo_;
      queueUID_ = other.queueUID_;
      articles_ = other.articles_.Clone();
      accelerateType_ = other.accelerateType_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueAccelerateReq Clone() {
      return new BuildQueueAccelerateReq(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑No
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 2;
    private uint queueUID_;
    /// <summary>
    ///队列Uid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    /// <summary>Field number for the "articles" field.</summary>
    public const int ArticlesFieldNumber = 3;
    private static readonly pb::FieldCodec<global::Article.Article> _repeated_articles_codec
        = pb::FieldCodec.ForMessage(26, global::Article.Article.Parser);
    private readonly pbc::RepeatedField<global::Article.Article> articles_ = new pbc::RepeatedField<global::Article.Article>();
    /// <summary>
    ///加速物品
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Article.Article> Articles {
      get { return articles_; }
    }

    /// <summary>Field number for the "accelerateType" field.</summary>
    public const int AccelerateTypeFieldNumber = 4;
    private global::Build.AccelerateType accelerateType_ = global::Build.AccelerateType.AccelerateNil;
    /// <summary>
    ///加速类型
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public global::Build.AccelerateType AccelerateType {
      get { return accelerateType_; }
      set {
        accelerateType_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildQueueAccelerateReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildQueueAccelerateReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (QueueUID != other.QueueUID) return false;
      if(!articles_.Equals(other.articles_)) return false;
      if (AccelerateType != other.AccelerateType) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      hash ^= articles_.GetHashCode();
      if (AccelerateType != global::Build.AccelerateType.AccelerateNil) hash ^= AccelerateType.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      articles_.WriteTo(output, _repeated_articles_codec);
      if (AccelerateType != global::Build.AccelerateType.AccelerateNil) {
        output.WriteRawTag(32);
        output.WriteEnum((int) AccelerateType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      articles_.WriteTo(ref output, _repeated_articles_codec);
      if (AccelerateType != global::Build.AccelerateType.AccelerateNil) {
        output.WriteRawTag(32);
        output.WriteEnum((int) AccelerateType);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      size += articles_.CalculateSize(_repeated_articles_codec);
      if (AccelerateType != global::Build.AccelerateType.AccelerateNil) {
        size += 1 + pb::CodedOutputStream.ComputeEnumSize((int) AccelerateType);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildQueueAccelerateReq other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      articles_.Add(other.articles_);
      if (other.AccelerateType != global::Build.AccelerateType.AccelerateNil) {
        AccelerateType = other.AccelerateType;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 26: {
            articles_.AddEntriesFrom(input, _repeated_articles_codec);
            break;
          }
          case 32: {
            AccelerateType = (global::Build.AccelerateType) input.ReadEnum();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 26: {
            articles_.AddEntriesFrom(ref input, _repeated_articles_codec);
            break;
          }
          case 32: {
            AccelerateType = (global::Build.AccelerateType) input.ReadEnum();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///返回队列加速
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildQueueAccelerateResp : pb::IMessage<BuildQueueAccelerateResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildQueueAccelerateResp> _parser = new pb::MessageParser<BuildQueueAccelerateResp>(() => new BuildQueueAccelerateResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildQueueAccelerateResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[14]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueAccelerateResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueAccelerateResp(BuildQueueAccelerateResp other) : this() {
      buildNo_ = other.buildNo_;
      queueUID_ = other.queueUID_;
      accelerateTime_ = other.accelerateTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueAccelerateResp Clone() {
      return new BuildQueueAccelerateResp(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑No
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 2;
    private uint queueUID_;
    /// <summary>
    ///队列Uid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    /// <summary>Field number for the "accelerate_time" field.</summary>
    public const int AccelerateTimeFieldNumber = 3;
    private ulong accelerateTime_;
    /// <summary>
    ///加速时间,秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong AccelerateTime {
      get { return accelerateTime_; }
      set {
        accelerateTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildQueueAccelerateResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildQueueAccelerateResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (QueueUID != other.QueueUID) return false;
      if (AccelerateTime != other.AccelerateTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      if (AccelerateTime != 0UL) hash ^= AccelerateTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (AccelerateTime != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(AccelerateTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (AccelerateTime != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(AccelerateTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      if (AccelerateTime != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(AccelerateTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildQueueAccelerateResp other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      if (other.AccelerateTime != 0UL) {
        AccelerateTime = other.AccelerateTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 24: {
            AccelerateTime = input.ReadUInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 24: {
            AccelerateTime = input.ReadUInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///请求队列帮助
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildQueueHelpReq : pb::IMessage<BuildQueueHelpReq>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildQueueHelpReq> _parser = new pb::MessageParser<BuildQueueHelpReq>(() => new BuildQueueHelpReq());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildQueueHelpReq> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[15]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueHelpReq() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueHelpReq(BuildQueueHelpReq other) : this() {
      buildNo_ = other.buildNo_;
      queueUID_ = other.queueUID_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueHelpReq Clone() {
      return new BuildQueueHelpReq(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑No
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 2;
    private uint queueUID_;
    /// <summary>
    ///队列Uid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildQueueHelpReq);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildQueueHelpReq other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (QueueUID != other.QueueUID) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildQueueHelpReq other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  ///返回队列帮助
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class BuildQueueHelpResp : pb::IMessage<BuildQueueHelpResp>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<BuildQueueHelpResp> _parser = new pb::MessageParser<BuildQueueHelpResp>(() => new BuildQueueHelpResp());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<BuildQueueHelpResp> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[16]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueHelpResp() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueHelpResp(BuildQueueHelpResp other) : this() {
      buildNo_ = other.buildNo_;
      queueUID_ = other.queueUID_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public BuildQueueHelpResp Clone() {
      return new BuildQueueHelpResp(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑No
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 2;
    private uint queueUID_;
    /// <summary>
    ///队列Uid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as BuildQueueHelpResp);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(BuildQueueHelpResp other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (QueueUID != other.QueueUID) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(BuildQueueHelpResp other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 推送建筑帮助加速
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushBuildHelpAccelerate : pb::IMessage<PushBuildHelpAccelerate>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushBuildHelpAccelerate> _parser = new pb::MessageParser<PushBuildHelpAccelerate>(() => new PushBuildHelpAccelerate());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushBuildHelpAccelerate> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[17]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushBuildHelpAccelerate() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushBuildHelpAccelerate(PushBuildHelpAccelerate other) : this() {
      buildNo_ = other.buildNo_;
      queueUID_ = other.queueUID_;
      accelerateTime_ = other.accelerateTime_;
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushBuildHelpAccelerate Clone() {
      return new PushBuildHelpAccelerate(this);
    }

    /// <summary>Field number for the "buildNo" field.</summary>
    public const int BuildNoFieldNumber = 1;
    private uint buildNo_;
    /// <summary>
    ///建筑No
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint BuildNo {
      get { return buildNo_; }
      set {
        buildNo_ = value;
      }
    }

    /// <summary>Field number for the "queueUID" field.</summary>
    public const int QueueUIDFieldNumber = 2;
    private uint queueUID_;
    /// <summary>
    ///队列Uid
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public uint QueueUID {
      get { return queueUID_; }
      set {
        queueUID_ = value;
      }
    }

    /// <summary>Field number for the "accelerate_time" field.</summary>
    public const int AccelerateTimeFieldNumber = 3;
    private ulong accelerateTime_;
    /// <summary>
    ///加速时间,秒
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public ulong AccelerateTime {
      get { return accelerateTime_; }
      set {
        accelerateTime_ = value;
      }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushBuildHelpAccelerate);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushBuildHelpAccelerate other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if (BuildNo != other.BuildNo) return false;
      if (QueueUID != other.QueueUID) return false;
      if (AccelerateTime != other.AccelerateTime) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      if (BuildNo != 0) hash ^= BuildNo.GetHashCode();
      if (QueueUID != 0) hash ^= QueueUID.GetHashCode();
      if (AccelerateTime != 0UL) hash ^= AccelerateTime.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (AccelerateTime != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(AccelerateTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      if (BuildNo != 0) {
        output.WriteRawTag(8);
        output.WriteUInt32(BuildNo);
      }
      if (QueueUID != 0) {
        output.WriteRawTag(16);
        output.WriteUInt32(QueueUID);
      }
      if (AccelerateTime != 0UL) {
        output.WriteRawTag(24);
        output.WriteUInt64(AccelerateTime);
      }
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      if (BuildNo != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(BuildNo);
      }
      if (QueueUID != 0) {
        size += 1 + pb::CodedOutputStream.ComputeUInt32Size(QueueUID);
      }
      if (AccelerateTime != 0UL) {
        size += 1 + pb::CodedOutputStream.ComputeUInt64Size(AccelerateTime);
      }
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushBuildHelpAccelerate other) {
      if (other == null) {
        return;
      }
      if (other.BuildNo != 0) {
        BuildNo = other.BuildNo;
      }
      if (other.QueueUID != 0) {
        QueueUID = other.QueueUID;
      }
      if (other.AccelerateTime != 0UL) {
        AccelerateTime = other.AccelerateTime;
      }
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 24: {
            AccelerateTime = input.ReadUInt64();
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 8: {
            BuildNo = input.ReadUInt32();
            break;
          }
          case 16: {
            QueueUID = input.ReadUInt32();
            break;
          }
          case 24: {
            AccelerateTime = input.ReadUInt64();
            break;
          }
        }
      }
    }
    #endif

  }

  /// <summary>
  /// 推送建筑
  /// </summary>
  [global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
  public sealed partial class PushBuild : pb::IMessage<PushBuild>
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      , pb::IBufferMessage
  #endif
  {
    private static readonly pb::MessageParser<PushBuild> _parser = new pb::MessageParser<PushBuild>(() => new PushBuild());
    private pb::UnknownFieldSet _unknownFields;
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pb::MessageParser<PushBuild> Parser { get { return _parser; } }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public static pbr::MessageDescriptor Descriptor {
      get { return global::Build.BuildReflection.Descriptor.MessageTypes[18]; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    pbr::MessageDescriptor pb::IMessage.Descriptor {
      get { return Descriptor; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushBuild() {
      OnConstruction();
    }

    partial void OnConstruction();

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushBuild(PushBuild other) : this() {
      builds_ = other.builds_.Clone();
      _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public PushBuild Clone() {
      return new PushBuild(this);
    }

    /// <summary>Field number for the "builds" field.</summary>
    public const int BuildsFieldNumber = 1;
    private static readonly pb::FieldCodec<global::Build.Build> _repeated_builds_codec
        = pb::FieldCodec.ForMessage(10, global::Build.Build.Parser);
    private readonly pbc::RepeatedField<global::Build.Build> builds_ = new pbc::RepeatedField<global::Build.Build>();
    /// <summary>
    /// 变更的建筑
    /// </summary>
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public pbc::RepeatedField<global::Build.Build> Builds {
      get { return builds_; }
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override bool Equals(object other) {
      return Equals(other as PushBuild);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public bool Equals(PushBuild other) {
      if (ReferenceEquals(other, null)) {
        return false;
      }
      if (ReferenceEquals(other, this)) {
        return true;
      }
      if(!builds_.Equals(other.builds_)) return false;
      return Equals(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override int GetHashCode() {
      int hash = 1;
      hash ^= builds_.GetHashCode();
      if (_unknownFields != null) {
        hash ^= _unknownFields.GetHashCode();
      }
      return hash;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public override string ToString() {
      return pb::JsonFormatter.ToDiagnosticString(this);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void WriteTo(pb::CodedOutputStream output) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      output.WriteRawMessage(this);
    #else
      builds_.WriteTo(output, _repeated_builds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(output);
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
      builds_.WriteTo(ref output, _repeated_builds_codec);
      if (_unknownFields != null) {
        _unknownFields.WriteTo(ref output);
      }
    }
    #endif

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public int CalculateSize() {
      int size = 0;
      size += builds_.CalculateSize(_repeated_builds_codec);
      if (_unknownFields != null) {
        size += _unknownFields.CalculateSize();
      }
      return size;
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(PushBuild other) {
      if (other == null) {
        return;
      }
      builds_.Add(other.builds_);
      _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
    }

    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    public void MergeFrom(pb::CodedInputStream input) {
    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
      input.ReadRawMessage(this);
    #else
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
            break;
          case 10: {
            builds_.AddEntriesFrom(input, _repeated_builds_codec);
            break;
          }
        }
      }
    #endif
    }

    #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
    [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
    void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
      uint tag;
      while ((tag = input.ReadTag()) != 0) {
      if ((tag & 7) == 4) {
        // Abort on any end group tag.
        return;
      }
      switch(tag) {
          default:
            _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
            break;
          case 10: {
            builds_.AddEntriesFrom(ref input, _repeated_builds_codec);
            break;
          }
        }
      }
    }
    #endif

  }

  #endregion

}

#endregion Designer generated code
