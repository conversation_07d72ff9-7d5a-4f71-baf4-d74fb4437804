using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIBuildingWagonForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnTip;
        [SerializeField] private UIButton m_btnRank;
        [SerializeField] private UIButton m_btnTarget;
        [SerializeField] private UIButton m_btnExit;
        [SerializeField] private UIButton m_btnExplore;
        [SerializeField] private UIButton m_btnAuto;
        [SerializeField] private UIButton m_btnReward;

        [SerializeField] private UIText m_txtTarget;

        [SerializeField] private UIImage m_imgProgress;

        [SerializeField] private RectTransform m_rectBg;
        [SerializeField] private GameObject m_goTargetRed;
        [SerializeField] private RectTransform m_rectProgressBg;
        [SerializeField] private Transform m_transDark;
        [SerializeField] private Transform m_transLevel;

        void InitBind()
        {
            m_btnTip.onClick.AddListener(OnBtnTipClick);
            m_btnRank.onClick.AddListener(OnBtnRankClick);
            m_btnTarget.onClick.AddListener(OnBtnTargetClick);
            m_btnExit.onClick.AddListener(OnBtnExitClick);
            m_btnExplore.onClick.AddListener(OnBtnExploreClick);
            m_btnAuto.onClick.AddListener(OnBtnAutoClick);
            m_btnReward.onClick.AddListener(OnBtnRewardClick);
        }
    }
}
