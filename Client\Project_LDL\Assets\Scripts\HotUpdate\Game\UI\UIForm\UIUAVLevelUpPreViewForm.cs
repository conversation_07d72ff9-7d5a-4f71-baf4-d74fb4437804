using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UIUAVLevelUpPreViewForm : UGuiFormEx
    {
        #region 成员变量

        private List<uav_level> m_LevelList;
        private List<Transform> uavLevelObjList = new List<Transform>();
        private UAVModule m_UavMoudule;
        
        #endregion
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_UavMoudule = GameEntry.LogicData.UAVData.UavModule;
            ResetUI();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            m_UavMoudule = null;
            uavLevelObjList.Clear();
            m_scrollviewList.content.gameObject.DestroyAllChild();
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void ResetUI()
        {
            m_LevelList = GetLevlList();
            for (int i = 0; i < m_LevelList.Count; i++)
            {
                Transform curItemTransform;
                if (uavLevelObjList.Count < i + 1)
                {
                    GameObject itemObj = Instantiate(m_goLevelItem, m_scrollviewList.content);
                    itemObj.SetActive(true);
                    curItemTransform = itemObj.transform;
                    uavLevelObjList.Add(itemObj.transform);
                }
                else
                {
                    curItemTransform = uavLevelObjList[i];
                }

                UpdateLevelList(i, curItemTransform);
            }
        }

        private void UpdateLevelList(int index, Transform itemTrans)
        {
            uav_level uavlevelConfig = m_LevelList[index];
            if (uavlevelConfig == null)
            {
                return;
            }
            var rectTransform         = itemTrans.GetComponent<RectTransform>();
            UIImage levelBg           = itemTrans.Find("levelBg").GetComponent<UIImage>();
            UIText txtLevel           = itemTrans.Find("levelBg/txtLevel").GetComponent<UIText>();
            UIImage imgSkillBg        = itemTrans.Find("imgSkillBg").GetComponent<UIImage>();
            UIImage imgSkillIcon      = itemTrans.Find("imgSkillBg/btnSkillIcon/imgSkillIcon").GetComponent<UIImage>();
            Transform goSkillStar     = itemTrans.Find("imgSkillBg/btnSkillIcon/goSkillStar");
            
            UIImage imgSkinInfo       = itemTrans.Find("imgSkinInfo").GetComponent<UIImage>();
            UIText txtUnLock          = itemTrans.Find("imgSkinInfo/txtUnLock").GetComponent<UIText>();
            UIText txtLock            = itemTrans.Find("imgSkinInfo/txtLock").GetComponent<UIText>();
            UIText txtSkinName        = itemTrans.Find("imgSkinInfo/txtSkinName").GetComponent<UIText>();
            UIText txtSkinDesc        = itemTrans.Find("imgSkinInfo/txtSkinDesc").GetComponent<UIText>();
            UIImage imgSkinIcon       = itemTrans.Find("imgSkinInfo/imgSkinIcon").GetComponent<UIImage>();
            Transform spuiGround       = itemTrans.Find("imgSkinInfo/spuiGround");
            
            Transform sliderLevelBg   = itemTrans.Find("sliderLevelBg");
            Slider sliderLevel        = itemTrans.Find("sliderLevelBg/sliderLevel").GetComponent<Slider>();

            UIButton btnClickSkin     = itemTrans.Find("imgSkillBg/btnSkillIcon").GetComponent<UIButton>();

            if (index == m_LevelList.Count - 1)
            {
                rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, rectTransform.sizeDelta.y + 600);
                sliderLevel.gameObject.SetActive(false);
                sliderLevelBg.gameObject.SetActive(false);
            }
            
            int configLevel = uavlevelConfig.level;
            txtLevel.text = configLevel.ToString();
            int curUavLevel = m_UavMoudule.Level;
            bool isUnlock = curUavLevel >= configLevel;
            spuiGround.gameObject.SetActive(isUnlock);
            bool isUnLockSkinLevel = GameEntry.LogicData.UAVData.IsUnLockSkinLevel(configLevel);
            string levelBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_yulan_jindutiaozhizhen2.png";
            if (isUnlock && isUnLockSkinLevel)
            {
                levelBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_yulan_jindutiaozhizhen1.png";
            }
            else if(isUnLockSkinLevel)
            {
                levelBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_yulan_jindutiaozhizhen3.png";
            }
            levelBg.SetImage(levelBgPath);
            // 算进度值
            int levelDif = curUavLevel - configLevel;
            float sliderValue = 0;
            if (levelDif > 0 && levelDif < 10)
            {
                sliderValue = (float)levelDif / 10;
            }
            else if (levelDif >= 10)
            {
                sliderValue = 1;
            }
            else
            {
                sliderLevel.value = 0;
            }
            sliderLevel.value = sliderValue;
            imgSkinInfo.gameObject.SetActive(isUnLockSkinLevel);
            string SkinBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_yulan_jindutiao_dikuang2.png";
            if (isUnlock)
            {
                SkinBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_yulan_jindutiao_dikuang1.png";
            }
            imgSkinInfo.SetImage(SkinBgPath);
            txtUnLock.gameObject.SetActive(isUnlock);
            txtLock.gameObject.SetActive(!isUnlock);
            txtUnLock.text = ToolScriptExtend.GetLang(1250);
            txtLock.text = ToolScriptExtend.GetLangFormat(1251,configLevel.ToString());
            if (isUnLockSkinLevel)
            {
                uav_skin uavSkinConfig = GameEntry.LogicData.UAVData.GetSkinConfigByUnLockLevel(configLevel);
                imgSkinIcon.SetImage(uavSkinConfig.picture,true);
                txtSkinName.text = ToolScriptExtend.GetLang(uavSkinConfig.name);
                txtSkinDesc.text = ToolScriptExtend.GetLang(uavSkinConfig.describe);
            }
            

            bool isUpSkillStarLevel = GameEntry.LogicData.UAVData.IsUpSkillStarLevel(configLevel);
            bool isShowSkill = configLevel == 1 || isUpSkillStarLevel;
            int star = 0;
            imgSkillBg.gameObject.SetActive(isShowSkill);
            if (isShowSkill)
            {
                uav_skill skillConfig = GameEntry.LogicData.UAVData.GetSkillConfigByUpLevel(configLevel);
                star = skillConfig.id - 1;
                if (skillConfig != null)
                {
                    imgSkillIcon.SetImage(skillConfig.picture);
                    RefreshStarInfo(goSkillStar.gameObject,skillConfig.id - 1);
                }
                string skillBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_yulan_daojukuang2.png";
                if (isUnlock)
                {
                    skillBgPath = "Sprite/ui_jianzhu_wurenji/wurenji_yulan_daojukuang1.png";
                }
                imgSkillBg.SetImage(skillBgPath);
            }
            
            btnClickSkin.onClick.RemoveAllListeners();
            btnClickSkin.onClick.AddListener((() =>
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIUAVSkillTip,new UIUAVSkillTipParams(uavlevelConfig,star,btnClickSkin.gameObject));
            }));
        }
        
        protected void RefreshStarInfo(GameObject parent,int starNum)
        {
            var count = parent.transform.childCount;
            for (int i = 0; i < count; i++)
            {
                var starSp = parent.transform.GetChild(i).GetComponent<UIImage>();
                starSp.gameObject.SetActive(i < starNum);
                string pathStr;                
                if (i < starNum)
                {
                    pathStr = "Sprite/ui_hero/herojunxian_jinengxiangqing_icon_star1.png";
                }
                else
                {
                    pathStr = "Sprite/ui_hero/herojunxian_jinengxiangqing_icon_star2.png";
                }
                var skillStar = parent.transform.GetChild(i).GetComponent<UIImage>();
                skillStar.SetImage(pathStr);
            }
        }

        private List<uav_level> GetLevlList()
        {
            List<uav_level> list = new List<uav_level>();
            List<uav_level> uavLevels = GameEntry.LDLTable.GetTable<uav_level>();
            for (var i = 0; i < uavLevels.Count; i++)
            {
                uav_level uavLevelConfig = uavLevels[i];
                int configLevel = uavLevelConfig.level;
                if (configLevel == 1 || (configLevel % 10 == 0 && uavLevelConfig.stage == 0))
                {
                    list.Add(uavLevelConfig);
                }
            }

            return list;
        }

        private void OnBtnExitClick()
        {
            Close();
        }
    }
}
