using System.Collections.Generic;
using Battle;
using JetBrains.Annotations;
using UnityEngine;

namespace Game.Hotfix
{
    public class Battle5v5ParamTradeTruck : Battle5v5ParamBase
    {
        public List<TeamHero> DefHeros => m_DefHeros;
        public long TruckId => m_TruckId;

        [CanBeNull] private Battle.Report m_Report;

        private List<TeamHero> m_DefHeros;
        private long m_TruckId;

        public Battle5v5ParamTradeTruck(List<TeamHero> list, long truckId = 0)
        {
            m_DefHeros = list;
            m_TruckId = truckId;
        }
        
        public override bool IsFinish()
        {
            return m_Report != null;
        }
        
        public void SetRecord(Battle.Report report)
        {
            m_Report = report;
        }
    }
}