using System.Collections.Generic;
using UnityEngine;
using Game.Hotfix.Config;
using System;
using Item;
namespace Game.Hotfix
{
    public class BagData
    {
        private List<ItemModule> m_ItemModules = new();
        //private Dictionary<int, ItemModule> m_ItemModulesDic = new();
        
        public void Init(Roledata.RoleItem roleItem)
        {
            if(roleItem == null){
                return;
            }
            m_ItemModules = new List<ItemModule>();
            //m_ItemModulesDic = new Dictionary<int, ItemModule>();

            foreach (var item in roleItem.Items)
            {
                ItemModule itemModule = new ItemModule();
                if(item.Amount>0){
                    itemModule.SetData((itemid)item.Code,item.Amount);
                    m_ItemModules.Add(itemModule);
                }               
            }
        }
        public void ChangeItem(ItemModule itemModule){
            foreach (var module in m_ItemModules)
            {
                if(module.ItemId == itemModule.ItemId){
                    module.Count = itemModule.Count;
                    if(module.Count <= 0){
                        m_ItemModules.Remove(itemModule);
                    }
                    GameEntry.Event.Fire(BagChangeEventArgs.EventId, BagChangeEventArgs.Create());
                    return;
                }
            }
            m_ItemModules.Add(itemModule);
            GameEntry.Event.Fire(BagChangeEventArgs.EventId, BagChangeEventArgs.Create());
        }
        public List<ItemModule> GetDataByTab(int tab){    
            itemclass itemclass = (itemclass)tab + 1;
            List<ItemModule> tab_ItemMoudes = new List<ItemModule>();
            foreach (var item in m_ItemModules)
            {
              item_config config = item.GetItemConfig();
              if(config.item_class == itemclass){
                if(item.Count > 0){
                    tab_ItemMoudes.Add(item);
                }               
              }
            }
            tab_ItemMoudes.Sort((a, b) => b.GetItemConfig().index.CompareTo(a.GetItemConfig().index));
            if (itemclass == itemclass.itemclass_equipment)
            {
                tab_ItemMoudes.AddRange(GameEntry.EquipmentData.GetEquipmentListAndSort());
            }
            return tab_ItemMoudes;
        }
        // group = config.item_show_type
        public List<ItemModule> GetDataByGroup(int group){
            List<ItemModule> group_ItemMoudes = new List<ItemModule>();
            foreach (var item in m_ItemModules)
            {
              item_config config = item.GetItemConfig();
              if((int)config.item_show_type == group){
                 group_ItemMoudes.Add(item);            
              }
            }
            return group_ItemMoudes;
        }
        public long GetAmountById(itemid itemId){
            foreach (var item in m_ItemModules)
            {
                if(itemId == item.ItemId){
                    return item.Count;
                }
            }
            
            return 0;
        }
        public long GetAmountById(ItemModule itemModule){
              foreach (var item in m_ItemModules)
            {
                if(itemModule.ItemId == item.ItemId){
                    return item.Count;
                }
            }
            return 0;
        }

        public List<ItemModule> GetDataBySubType(itemsubtype subType)
        {
            List<ItemModule> list = new List<ItemModule>();
        
            foreach (var item in m_ItemModules)
            {
                item_config config = item.GetItemConfig();
                if(config.item_subtype == subType)
                {
                    list.Add(item);
                }
            }
            return list;
        }

        public bool GetResoureIsEnough(itemid itemId,long num)
        {
            long amount = GetAmountById(itemId);
            return amount >= num;
        }
        
        public int GetDiamondBuyRescource(itemid itemId,long needNum)
        {
            int needDiamond = 0;
            int configId = itemId switch
            {
                itemid.itemid_2 => 41,
                itemid.itemid_3 => 40,
                itemid.itemid_4 => 42,
                _ => 0
            };
            if (configId != 0)
            {
                global_setting globalSetting = GameEntry.LDLTable.GetTableById<global_setting>(configId);
                if (globalSetting != null)
                {
                    int configNum = Int32.Parse(globalSetting.value[0]);
                    long amount = GetAmountById(itemId);
                    amount = (long)Mathf.Min(amount, needNum);
                    float remainNum = Mathf.Abs(needNum - amount);
                    needDiamond = Mathf.CeilToInt(remainNum/configNum);
                }
            }

            return needDiamond;
        }
        
        public void OnReqItemUse(List<ItemModule> list,Action<Item.ItemUseResp> action = null)
        {
            List<Item.ItemUse> itemuseList = new List<Item.ItemUse>();
            foreach (var item in list)
            {
                Item.ItemUse itemUse = new Item.ItemUse();
                itemUse.Code = (PbGameconfig.itemid)item.ItemId;
                itemUse.Amount = (uint)item.Count;
                itemUse.ChooseId = (uint)item.ChooseId;
                itemuseList.Add(itemUse);
            }
            
            var req = new Item.ItemUseReq
            {
                List ={itemuseList}
            };

            GameEntry.LDLNet.Send(Protocol.MessageID.ItemUse, req, (message) =>
            {
                var resp = (Item.ItemUseResp)message;
                List<reward> rewards = new();
                foreach (var item in resp.Rewards)
                {
                    rewards.Add(new reward()
                    {
                        item_id = (itemid)item.Code,
                        num = item.Amount
                    });
                }
                action?.Invoke(resp);
                GameEntry.UI.OpenUIForm(EnumUIForm.UIRewardGetForm, rewards);                          
            });
        }
        public void OnReqBuyItemByDiamond(itemid id,long count,Action<ItemBuyResp>action){
            ItemBuyReq res = new ItemBuyReq();
            res.Code = (PbGameconfig.itemid)id;
            res.Amount = count;
            GameEntry.LDLNet.Send(Protocol.MessageID.ItemBuy,res,(message) =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {                           
                            Content = ToolScriptExtend.GetLang(1100224)
                        });
                    var resp = (ItemBuyResp)message;
                    action?.Invoke(resp);
                }
            );          
        }
        public void ResourceGetWay(params ItemModule[] args){
            if(args.LongLength <= 0)
            {
              return;
            }
            itemlackhandle type = 0;
            type = args[0].GetItemConfig().item_lack_handle;
            if(type == itemlackhandle.item_lack_handle_text){
                GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
                        {                           
                            Content = ToolScriptExtend.GetLang(int.Parse(args[0].GetItemConfig().item_lack_handle_value[0]))
                        });
            }else if(type == itemlackhandle.item_lack_handle_access_button)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UIGetWayForm,args);
            }          
        }
        public List<ItemModule> GetListByGroup(itemshowtype item_show_type){
            List<ItemModule> list = new List<ItemModule>();
            foreach (var module in m_ItemModules)
            {
              if(module.GetItemConfig().item_show_type == item_show_type && module.Count > 0){
                    list.Add(module);
                }   
            }
            return list;
        }
    }
}
 