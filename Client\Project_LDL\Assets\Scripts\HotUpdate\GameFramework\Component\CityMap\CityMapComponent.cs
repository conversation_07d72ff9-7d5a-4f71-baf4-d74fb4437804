using System;
using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Serialization;

namespace Game.Hotfix
{
    public struct UIPointer
    {
        /// <summary>
        /// The pointer info
        /// </summary>
        public PointerInfo pointer;

        /// <summary>
        /// The ray for this pointer
        /// </summary>
        public Ray ray;

        /// <summary>
        /// The raycast hit object into the 3D scene
        /// </summary>
        public RaycastHit? raycast;

        /// <summary>
        /// True if this pointer started over a UI element or anything the event system catches
        /// </summary>
        public bool overUI;
    }

    public enum eBuildingPlacementRejectionReason
    {
        Unknown,//未知
        OutLogic,//超出逻辑范围
        OnTheRoad,//在路上
        OutBuildingArea,//超出建造区域
        NotUnlock,//未解锁
    }

    public class CityMapComponent : BaseSceneComponent
    {
        [FormerlySerializedAs("AreaGenerator")] public MainCityAreaGenerator mainCityAreaGenerator;
        [FormerlySerializedAs("Fog")]public MainCityFog mainCityFog;
        private MapOperationController m_MapOpCtrl;
        private MapZombieAttackCtrl m_ZombieAttackCtrl;

        private int? m_PvePathAgentId;
        private CommonEffect m_PvePathCurPosEffect;
        private CommonEffect m_PvePathNextPosEffect;
        
        private List<int> m_WallEntityList;
        
        protected override int GetSceneId()
        {
            return (int)SceneDefine.MainScene;
        }

        protected override void OnStart()
        {
            base.OnStart();
            m_MapOpCtrl = new MapOperationController();
            m_ZombieAttackCtrl = new MapZombieAttackCtrl();
            m_WallEntityList = new List<int>();
            
            Init();
            
            Game.GameEntry.Event.Subscribe(OnNewBuildingCreateEventArgs.EventId,OnNewBuildingCreate);
            Game.GameEntry.Event.Subscribe(OnAreaStateChangeEventArgs.EventId,OnAreaStateChangeEvent);
            Game.GameEntry.Event.Subscribe(OnPvePathTriggeredEventArgs.EventId,OnPvePathTriggeredEvent);
            Game.GameEntry.Event.Subscribe(On5V5BattleBackEventArgs.EventId,On5V5BattleBackEvent);
        }

        protected override void OnDestroy()
        {
            if (m_MapOpCtrl != null)
            {
                m_MapOpCtrl.Destroy();
                m_MapOpCtrl = null;
            }

            if (m_ZombieAttackCtrl != null)
            {
                m_ZombieAttackCtrl.Destroy();
                m_ZombieAttackCtrl = null;
            }
            
            Game.GameEntry.Event.Unsubscribe(On5V5BattleBackEventArgs.EventId,On5V5BattleBackEvent);
            Game.GameEntry.Event.Unsubscribe(OnNewBuildingCreateEventArgs.EventId,OnNewBuildingCreate);
            Game.GameEntry.Event.Unsubscribe(OnAreaStateChangeEventArgs.EventId,OnAreaStateChangeEvent);
            Game.GameEntry.Event.Unsubscribe(OnPvePathTriggeredEventArgs.EventId,OnPvePathTriggeredEvent);
            base.OnDestroy();
            GameEntry.CityMap = null;
        }

        private void On5V5BattleBackEvent(object sender, GameEventArgs e)
        {
            if (e is On5V5BattleBackEventArgs args)
            {
                Battle5v5ParamBase param = args.Param;
                if (param is Battle5v5ParamDungeon paramDungeon)
                {
                    if (paramDungeon.IsWin() && paramDungeon.PvePathModule!=null)
                    {
                        //路点判断
                        var pvePathData =GameEntry.LogicData.PvePathData;
                        
                        var battlePvePathModule = paramDungeon.PvePathModule;
                        if (pvePathData.CurPathModuleDisplay.GetNext() == battlePvePathModule.Id)
                        {
                            if (pvePathData.CanTriggerEvent(battlePvePathModule, true))
                            {
                                pvePathData.TriggerEventFinish(battlePvePathModule);
                            }
                        }
                        
                        
                    }
                }
            }
        }
        
        private void OnPvePathTriggeredEvent(object sender, GameEventArgs e)
        {
            if (e is OnPvePathTriggeredEventArgs ne)
            {
                var oldModule = ne.OldModule;
                var newModule = ne.NewModule;
                StartCoroutine(PvePathAnimation(oldModule,newModule));
            }
        }

        private void OnAreaStateChangeEvent(object sender, GameEventArgs e)
        {
            var ne = (OnAreaStateChangeEventArgs)e;
            if (ne != null)
            {
                if (ne.NewState == MainCityAreaState.Unlockable)
                {
                    //切换到可以解锁
                    CreateAreaUnlockEntity(ne.MainCityAreaModule);
                    mainCityFog.AddBound(ne.MainCityAreaModule.Bounds);
                }else if (ne.NewState == MainCityAreaState.Unlocked)
                {
                    //移除所有装饰
                    var decorations = ne.MainCityAreaModule.Decorations;
                    foreach (var iAreaDecorationModule in decorations)
                    {
                        if (iAreaDecorationModule.Uid != null)
                        {
                            Game.GameEntry.Entity.HideEntity(iAreaDecorationModule.Uid.Value);
                            iAreaDecorationModule.SetUid(null);
                        }
                    }
                    mainCityAreaGenerator.AddBound(ne.MainCityAreaModule.Bounds);
                    mainCityFog.AddBound(ne.MainCityAreaModule.Bounds);
                    //切换到 已经解锁
                    RebuildWall();
                    //
                    ReBuildGround();
                }
            }
        }

        private void OnNewBuildingCreate(object sender, GameEventArgs e)
        {
            var ne = (OnNewBuildingCreateEventArgs)e;
            if (ne != null)
            {
                //创建building
                ED_Building edBuilding = new ED_Building(ne.BuildingModule.UID);
                edBuilding.buildingData = ne.BuildingModule;
                edBuilding.Position = ne.BuildingModule.GetWorldPosition();
                Game.GameEntry.Entity.ShowBuilding(edBuilding);
            }
        }

        private void Init()
        {
            try
            {
                InitArea();
                InitWall();
                InitBuilding();
                InitPvePath();
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private void InitWall()
        {
            RebuildWall();
        }

        private void RebuildWall()
        {
            foreach (var wallId in m_WallEntityList)
                GameEntry.Entity.HideEntity(wallId);
            m_WallEntityList.Clear();
            
            GameEntry.LogicData.GridData.CalculateWall();
            var wallDatas = GameEntry.LogicData.GridData.WallDataList;
            for (int i = 0; i < wallDatas.Count; i++)
            {
                var wallData = wallDatas[i];
                int wallId = Game.GameEntry.Entity.ShowWall(wallData);
                m_WallEntityList.Add(wallId);
            }
        } 
        
        private void InitBuilding()
        {
            var buildingData = GameEntry.LogicData.BuildingData;
            foreach (var buildingModule in buildingData)  
            {
                ED_Building edBuilding = new ED_Building(buildingModule.UID);
                edBuilding.buildingData = buildingModule;
                edBuilding.Position = buildingModule.GetWorldPosition();
                Game.GameEntry.Entity.ShowBuilding(edBuilding);
            }

            CheckTrainStationBuilding();
        }

        private void InitPvePath()
        {
            var pvePathData = GameEntry.LogicData.PvePathData;
            if (pvePathData.IsFinish)
                return;
            
            if (pvePathData.CurPathModule is { AreaId: not null })
            {
                var module =
                    GameEntry.LogicData.MainCityAreaData.GetModuleById(pvePathData.CurPathModule.AreaId.Value);
                if (module != null)
                {
                    mainCityFog.AddBound(module.Bounds);
                }
            }
            
            foreach (var pvePathModule in pvePathData)
            {
                if (pvePathModule.CanShow())
                {
                    Game.GameEntry.Entity.ShowPvePathGround(pvePathModule);
                    if (pvePathData.CurPathModule!=null && pvePathData.CurPathModule.Id + 1 == pvePathModule.Id)
                    {
                        //这是下一个
                        SetNextPosEffect(pvePathModule);
                    }
                }
                    
            }
            
            //创建角色
            var curPathModule = pvePathData.CurPathModuleDisplay;
            if (curPathModule != null)
            {
                var agentUid = Game.GameEntry.Entity.ShowPvePathAgent(curPathModule);
                m_PvePathAgentId = agentUid;

                SetCurPosEffect(curPathModule.GetPositionCenter(true));
            }
        }

        #region 地面路点动画

        IEnumerator PvePathAnimation(PvePathModule oldModule,PvePathModule newModule)
        {
            PvePathData pvePathData = GameEntry.LogicData.PvePathData;
            PvePathModule? nextModule = null;
            
            var nextId = newModule.GetNext();
            if (nextId > 0)
                nextModule = pvePathData.GetPvePathModuleById(nextId);
            
            mainCityFog.AddBound(newModule.GetBounds());
            
            GameEntry.Camera.LookAtPosition(newModule.GetPosition());
            
            yield return new WaitForEndOfFrame();
            if (newModule != null)
            {
                //旧的先播放 移除 动画
                yield return oldModule.OnLeaveDisplay();

                newModule.ShowObj(false);

                HideNextPosEffect();
                
                //如果是 关卡 创建宝箱
                //TODO


                //角色开始移动
                var agentUid = m_PvePathAgentId;
                if (agentUid != null)
                {
                    var moveDuration = 2f;
                    while(GameEntry.Entity.IsLoadingEntity(agentUid.Value))
                    {
                        yield return new WaitForEndOfFrame();
                    }
                    var entity = GameEntry.Entity.GetEntity(agentUid.Value);
                    if (entity != null && entity.Logic != null && entity.Logic is EL_PvePathAgent agent)
                    {
                        agent.MoveTo(newModule, moveDuration);
                        yield return new WaitForSeconds(moveDuration);
                    }
                }

                //角色到达位置
                pvePathData.OnAgentArrive(newModule);
                
                SetNextPosEffect(nextModule);
                SetCurPosEffect(newModule.GetPositionCenter(true));
                
                //如果 是目的地 后面的地块 进入解锁状态
                var backList = pvePathData.GetIncludeBackGrounds(newModule);
                for (int i = 0; i < backList.Count; i++)
                {
                    var blackModule = backList[i];
                    var duration = blackModule.PlayGroundExitAnimation();
                    //相机给焦点 TODO
                    GameEntry.Camera.LookAtPosition(blackModule.GetPosition());
                    yield return new WaitForSeconds(duration);
                    blackModule.ShowGround(false);
                }

                pvePathData.OnDisplayArrive(newModule);
                
                yield return newModule.OnArriveDisplay();
                
                var unlockAreaId = newModule.GetUnlockArea(); 
                if (unlockAreaId!=null)
                {
                    GameEntry.LogicData.MainCityAreaData.ChangeStateToUnlockAble(unlockAreaId.Value);
                }

                if (nextModule != null)
                {
                    nextModule.OnPreviousArriveDisplay();
                }
                else
                {
                    yield return new WaitForSeconds(1);
                    
                    newModule.ShowGround(false);
                        
                    if (m_PvePathAgentId!=null && GameEntry.Entity.HasEntity(m_PvePathAgentId.Value))
                        GameEntry.Entity.HideEntity(m_PvePathAgentId.Value);
                    
                    if(m_PvePathCurPosEffect!=null)
                        GameEntry.Effect.RemoveEffect(m_PvePathCurPosEffect);
                    if(m_PvePathNextPosEffect!=null)
                        GameEntry.Effect.RemoveEffect(m_PvePathNextPosEffect);
                    
                }
            }

        }

        private void SetCurPosEffect(Vector3 pos)
        {
            if (m_PvePathCurPosEffect == null)
            {
                m_PvePathCurPosEffect = GameEntry.Effect.CreateEffect(100004, null, pos);
            }
            else
            {
                m_PvePathCurPosEffect.SetPosition(pos);
            }
        }

        private void SetNextPosEffect(PvePathModule module)
        {
            HideNextPosEffect();

            if (module == null)
                return;
            
            var effectId = module.GetEffectId();
            if (effectId != null)
                m_PvePathNextPosEffect =
                    GameEntry.Effect.CreateEffect(effectId.Value, null, module.GetPositionCenter(true));
        }

        private void HideNextPosEffect()
        {
            if (m_PvePathNextPosEffect != null)
            {
                GameEntry.Effect.RemoveEffect(m_PvePathNextPosEffect);
                m_PvePathNextPosEffect = null;
            }
        }
        

        #endregion
        
        #region 区域相关
        private void InitArea()
        {
            var areaData = GameEntry.LogicData.MainCityAreaData;
            foreach (var areaModule in areaData)
            {
                if (areaModule.AreaState == MainCityAreaState.Locked)
                {
                    //创建装饰 
                    CreateAreaDecoration(areaModule);
                }else if (areaModule.AreaState == MainCityAreaState.Unlockable)
                {
                    //创建装饰
                    CreateAreaDecoration(areaModule);
                    //创建点击区域 用来解锁
                    CreateAreaUnlockEntity(areaModule);
                    mainCityFog.AddBound(areaModule.Bounds);
                }else if (areaModule.AreaState == MainCityAreaState.Unlocked)
                {
                    //已经解锁了 什么都不用创建
                    mainCityAreaGenerator.AddBound(areaModule.Bounds);
                    mainCityFog.AddBound(areaModule.Bounds);
                }
            }
            
            ReBuildGround();
        }

        /// <summary>
        /// 创建装饰
        /// </summary>
        /// <param name="module"></param>
        private void CreateAreaDecoration(MainCityAreaModule module)
        {
            var list = module.Decorations;
            foreach (var item in list)
            {
                Game.GameEntry.Entity.ShowMainCityAreaDecoration(item);
            }
        }

        /// <summary>
        /// 创建点击区域 用来解锁
        /// </summary>
        /// <param name="module"></param>
        private void CreateAreaUnlockEntity(MainCityAreaModule module)
        {
            Game.GameEntry.Entity.ShowMainCityAreaUnlock(module);
        }

        private void ReBuildGround()
        {
            mainCityAreaGenerator.Rebuild();
        }
        
        #endregion

        public bool CanBuild(Vector3 pos,BuildingModule buildingModule,out eBuildingPlacementRejectionReason reason)
        {
            reason = eBuildingPlacementRejectionReason.Unknown;
            
            int length = buildingModule.GetGridAreaL();
            int width = buildingModule.GetGridAreaW();
            if (length <= 0) return false;
            if (width <= 0) return false;
            
            var totalCnt = length * width;
            var curCnt = 0;

            var gridData = GameEntry.LogicData.GridData;
            for (int i = 0; i < length; i++)
            {
                for (int j = 0; j < width; j++)
                {
                    var gridType = gridData.GetGridType(new Vector2Int((int)pos.x + i, (int)pos.z + j));
                    if(!gridType.HasFlag(eMainCityGridType.Logic))
                    {
                        reason = eBuildingPlacementRejectionReason.OutLogic;
                        return false;
                    }

                    if (gridType.HasFlag(eMainCityGridType.NoBuilding))
                    {
                        reason = eBuildingPlacementRejectionReason.OnTheRoad;
                        return false;
                    }
                    
                    if (!gridType.HasFlag(eMainCityGridType.BuildAble))
                    {
                        reason = eBuildingPlacementRejectionReason.OutBuildingArea;
                        return false;
                    }
                    
                    curCnt++;
                }
            }

            if (curCnt != totalCnt)
            {
                return false;
            }
            
            curCnt = 0;
            for (int i = 0; i < length; i++)
            {
                for (int j = 0; j < width; j++)
                {
                    var buildingModuleGround = GameEntry.LogicData.GridData.GetBuilding((int)(pos.x + i), (int)(pos.z + j));
                    if(buildingModuleGround!=null && buildingModuleGround.UID!=buildingModule.UID)
                    {
                        return false;
                    }

                    curCnt++;
                }
            }
            
            return curCnt == totalCnt;
        }

        protected override void OnUpdate(float dt)
        {
            base.OnUpdate(dt);
            m_MapOpCtrl?.Update();
            m_ZombieAttackCtrl?.Update();
        }

        protected override void OnLateUpdate()
        {
            base.OnLateUpdate();
            m_MapOpCtrl?.LateUpdate();
        }

        #region 获取空闲地块

        private List<Vector2Int> PathPath;
        public Vector2Int? GetEmptyGridPos(BuildingModule buildingModule)
        {
            Vector2Int baseGridPos = new Vector2Int(70, 70);
            Vector3 temp = Vector3.zero;
            Func<Vector2Int,bool> isTargetNode = (point) =>
            {
                temp.x = point.x;
                temp.y = 0;
                temp.z = point.y;
                if (CanBuild(temp, buildingModule, out eBuildingPlacementRejectionReason reason))
                {
                    return true;
                }
                return false;
            };
            
            Func<Vector2Int,bool> isValidNode = (point) =>
            {
                var type = GameEntry.LogicData.GridData.GetGridType(point);
                if (type.HasFlag(eMainCityGridType.BuildAble))
                    return true;
                return false;
            };

            var path = MapGridUtils.PathFind(baseGridPos, isTargetNode, isValidNode);
            if (path is { Count: > 0 })
            {
                PathPath = path;
                return path[^1];
            }
            else
            {
                PathPath = null;
            }
            return null;
        }

        #endregion
        
        #region 地图操作相关
        
        public void Build(int id,int level)
        {
            m_MapOpCtrl.BuildMode(id,level);
        }

        public void BuildCancel()
        {
            m_MapOpCtrl.BuildCancel();
        }

        public void BuildConfirm()
        {
            m_MapOpCtrl.BuildConfirm();
        }

        public void BuildingBeginMove(EL_Building building)
        {
            m_MapOpCtrl.BuildingBeginMove(building);
        }

        public void BuildingMoveCancel()
        {
            m_MapOpCtrl.BuildingMoveCancel();
        }

        public void BuildingMoveConfirm()
        {
            m_MapOpCtrl.BuildingMoveConfirm();
        }
        #endregion

#if UNITY_EDITOR
        void OnDrawGizmos()
        {
            GameEntry.LogicData?.GridData.OnDrawGizmos();

            if (PathPath != null)
            {
                Color prevCol = Gizmos.color;
                Gizmos.color = Color.red;//Color.yellow;
                for (int i = 0; i < PathPath.Count; i++)
                {
                    var v3Temp1 = new Vector3();
                    v3Temp1.x = PathPath[i].x;
                    v3Temp1.y = 0;
                    v3Temp1.z = PathPath[i].y;
                    Gizmos.DrawCube(v3Temp1, Vector3.one);    
                }
                
                Gizmos.color = prevCol;
            }
        }
#endif

        public BuildingModule trainStationTemp;

        void CheckTrainStationBuilding()
        {
            BuildingModule trainStation = GameEntry.LogicData.BuildingData.FindBuildingById(7201);
            if (trainStation == null)
            {
                ColorLog.Green("火车站建筑不存在, 临时补充一个");
                trainStation = BuildingModule.Create(7201, 1, 1);
                trainStationTemp = trainStation;
                innercity_initialbuild innercity = GameEntry.LDLTable.GetTableById<innercity_initialbuild>(7201);
                if (innercity != null)
                {
                    trainStation.SetGridPos(innercity.initial_location.x, innercity.initial_location.y);
                }
                ED_Building edBuilding = new(trainStation.UID)
                {
                    buildingData = trainStation,
                    Position = trainStation.GetWorldPosition()
                };
                Game.GameEntry.Entity.ShowBuilding(edBuilding);
            }
        }
    }
}