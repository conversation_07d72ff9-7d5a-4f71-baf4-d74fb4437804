using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;
using Game.Hotfix.Config;

namespace Game.Hotfix
{
    public partial class UITradeTruckForm : UGuiFormEx
    {
        ObjectPool<GameObject> playerInfoPool;
        readonly List<GameObject> activePlayerInfo = new();

        readonly List<UITruckItem> myTruckItems = new();
        readonly List<UITruckItem> otherTruckItems = new();

        readonly ArrayList tradeItems = new();
        readonly List<UITruckItem> activeTruckItems = new();

        int gridRows = 4;
        int gridCols = 6;
        const int TRUCK_COUNT = 15;

        readonly float OFFSET_RANGE_X = 15f;
        readonly float OFFSET_RANGE_Y = 15f;

        bool[,] occupiedCells;

        UITruckItem curSelectedTruck;
        UITrainItem curSelectedTrain;

        bool isShowOther;

        int otherTruckIndex;
        int myTruckIndex;

        bool hasMyTrain;
        bool hasOtherTrain;

        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            InitPanel();
            DefaultDisplay();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            isShowOther = true;
            m_togOtherTruck.isOn = true;
            m_goMyTrainItem.SetActive(false);
            m_goOtherTrainItem.SetActive(false);

            // 请求我的货车列表
            GameEntry.TradeTruckData.RequestTruckList(1, false, (result) =>
            {
                ColorLog.Pink("我的货车列表", result);
                RefreshMyTruck();
                RefreshTradeCount();

                hasMyTrain = GameEntry.TradeTruckData.myTrain != null;

                TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
                ColorLog.Pink("火车状态", trainStatus);
                m_goMyTrainItem.SetActive(hasMyTrain && !isShowOther && trainStatus == TrainStatus.Depart);

                UITrainItem myTrainItem = m_goMyTrainItem.GetComponent<UITrainItem>();
                myTrainItem.trainData = GameEntry.TradeTruckData.myTrain;
            });

            // 请求他人货车列表
            ColorLog.Pink("是否过滤本战区", GameEntry.TradeTruckData.IsFilterTruck);
            GameEntry.TradeTruckData.RequestTruckList(2, GameEntry.TradeTruckData.IsFilterTruck, (result) =>
            {
                ColorLog.Pink("他人货车列表", result);
                hasOtherTrain = GameEntry.TradeTruckData.otherTrain != null;
                RefreshOtherTruck();
                PlaceTrucksOnGrid();
                RefreshPlunderCount();

                m_goOtherTrainItem.SetActive(hasOtherTrain && isShowOther);

                UITrainItem otherTrainItem = m_goOtherTrainItem.GetComponent<UITrainItem>();
                otherTrainItem.trainData = GameEntry.TradeTruckData.otherTrain;

                if (m_goOtherTrainItem.activeInHierarchy)
                {
                    Transform playerInfo = m_goOtherTrainItem.transform.Find("playerInfo");
                    if (otherTrainItem.trainData != null
                    && otherTrainItem.trainData.Boxcar.Count > 0
                    && otherTrainItem.trainData.Boxcar[0].Passengers.Count > 0)
                    {
                        ulong roleId = otherTrainItem.trainData.Boxcar[0].Passengers[0].RoleId;
                        RefreshTruckPlayerInfo(playerInfo, roleId);
                    }
                }
            });

            RefreshPanel();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            m_goTruckCard.SetActive(false);
            m_transTruckSelected.gameObject.SetActive(false);

            foreach (var item in activePlayerInfo)
            {
                playerInfoPool.Release(item);
            }
            activePlayerInfo.Clear();
        }

        protected override void OnDepthChanged(int uiGroupDepth, int depthInUIGroup)
        {
            base.OnDepthChanged(uiGroupDepth, depthInUIGroup);

            SetParticleSystemSortingOrder(m_goTruckItem, Depth);

            m_goPlayerInfo.GetComponent<Canvas>().sortingOrder = Depth + 3;
            m_goTruckCard.GetComponent<Canvas>().sortingOrder = Depth + 4;
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);

            if (userData is bool flag)
            {
                m_goMyTruck.SetActive(flag);
                return;
            }

            RefreshMyTruck();
            m_goTruckCard.SetActive(false);
            m_transTruckSelected.gameObject.SetActive(false);
            RefreshTradeCount();
            RefreshPlunderCount();
        }

        protected override void OnUpdate(float elapseSeconds, float realElapseSeconds)
        {
            base.OnUpdate(elapseSeconds, realElapseSeconds);

            if (m_goTruckCard.activeInHierarchy)
            {
                if (curSelectedTruck != null && curSelectedTruck.truckData != null)
                {
                    long remainTime = curSelectedTruck.truckData.ArrivalTime - (long)TimeComponent.Now;
                    if (remainTime < 0)
                    {
                        m_txtTruckCardArriveTime.text = ToolScriptExtend.GetLang(1226);
                    }
                    else
                    {
                        m_txtTruckCardArriveTime.text = ToolScriptExtend.GetLang(1123) + TimeHelper.FormatGameTimeWithDays((int)remainTime);
                    }
                }
                else if (curSelectedTrain != null && curSelectedTrain.trainData != null)
                {
                    long remainTime = curSelectedTrain.trainData.ArrivalTime - (long)TimeComponent.Now;
                    if (remainTime < 0)
                    {
                        m_txtTruckCardArriveTime.text = ToolScriptExtend.GetLang(1226);
                    }
                    else
                    {
                        m_txtTruckCardArriveTime.text = ToolScriptExtend.GetLang(1123) + TimeHelper.FormatGameTimeWithDays((int)remainTime);
                    }
                }
            }
        }

        private void OnBtnAddCountClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = "功能暂未开放",
            });
        }

        private void OnBtnResetClick()
        {
            // 请求他人货车列表
            ColorLog.Pink("是否过滤本战区", GameEntry.TradeTruckData.IsFilterTruck);
            GameEntry.TradeTruckData.RequestTruckList(2, GameEntry.TradeTruckData.IsFilterTruck, (result) =>
            {
                ColorLog.Pink("他人货车列表", result);
                RefreshOtherTruck();
                PlaceTrucksOnGrid();

                hasOtherTrain = GameEntry.TradeTruckData.otherTrain != null;
                m_goOtherTrainItem.SetActive(hasOtherTrain && isShowOther);

                UITrainItem otherTrainItem = m_goOtherTrainItem.GetComponent<UITrainItem>();
                otherTrainItem.trainData = GameEntry.TradeTruckData.otherTrain;

                if (m_goOtherTrainItem.activeInHierarchy)
                {
                    Transform playerInfo = m_goOtherTrainItem.transform.Find("playerInfo");
                    if (otherTrainItem.trainData != null
                    && otherTrainItem.trainData.Boxcar.Count > 0
                    && otherTrainItem.trainData.Boxcar[0].Passengers.Count > 0)
                    {
                        ulong roleId = otherTrainItem.trainData.Boxcar[0].Passengers[0].RoleId;
                        RefreshTruckPlayerInfo(playerInfo, roleId);
                    }
                }
            });

            m_goTruckCard.SetActive(false);
            m_transTruckSelected.gameObject.SetActive(false);
        }

        private void OnBtnHistoryClick()
        {
            GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckHistoryForm);
        }

        private void OnBtnBackClick()
        {
            Close();
        }

        private void OnBtnPlunderOtherClick()
        {
            Close();
            GameEntry.LogicData.WorldMapData.GotoWorldMap();
            if (curSelectedTruck != null)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckPlunderForm, curSelectedTruck);
            }
            else if (curSelectedTrain != null)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckPlunderForm, curSelectedTrain);
            }
        }

        private void OnBtnViewMyClick()
        {
            Close();
            GameEntry.LogicData.WorldMapData.GotoWorldMap();
            if (curSelectedTruck != null)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckPlunderForm, curSelectedTruck);
            }
            else if (curSelectedTrain != null)
            {
                GameEntry.UI.OpenUIForm(EnumUIForm.UITradeTruckPlunderForm, curSelectedTrain);
            }
        }

        private void OnBtnShareClick()
        {

        }

        private void OnBtnCardLeftClick()
        {
            SwitchNextTruck(-1);
        }

        private void OnBtnCardRightClick()
        {
            SwitchNextTruck(1);
        }

        void InitPanel()
        {
            playerInfoPool = new ObjectPool<GameObject>(OnCreate, OnGet, OnRelease, OnObjectDestroy);
            m_togOtherTruck.onValueChanged.AddListener(OnTogOtherTruck);
            m_togMyTruck.onValueChanged.AddListener(OnTogMyTruck);

            m_togFilterTruck.isOn = GameEntry.TradeTruckData.IsFilterTruck;
            m_togFilterTruck.onValueChanged.AddListener(OnTogFilterTruck);

            m_goTruckItem.SetActive(false);
            for (int i = 0; i < 4; i++)
            {
                GameObject item = Instantiate(m_goTruckItem, m_goMyTruck.transform);
                item.SetActive(true);
                UITruckItem truckItem = item.GetComponent<UITruckItem>();
                truckItem.isOther = false;
                truckItem.index = i;
                truckItem.callback = OnClickTruck;
                truckItem.Init();
                myTruckItems.Add(truckItem);

                truckItem.truckState = TruckState.Idle;
                if (i == 3) truckItem.truckState = TruckState.Lock;
            }

            for (int i = 0; i < TRUCK_COUNT; i++)
            {
                GameObject item = Instantiate(m_goTruckItem, m_goOtherTruck.transform);
                item.SetActive(true);
                UITruckItem truckItem = item.GetComponent<UITruckItem>();
                truckItem.isOther = true;
                truckItem.callback = OnClickTruck;
                truckItem.Init();
                otherTruckItems.Add(truckItem);
            }

            UITrainItem otherTrainItem = m_goOtherTrainItem.GetComponent<UITrainItem>();
            otherTrainItem.isOther = true;
            otherTrainItem.callback = OnClickTrain;
            otherTrainItem.Init();

            UITrainItem myTrainItem = m_goMyTrainItem.GetComponent<UITrainItem>();
            myTrainItem.isOther = false;
            myTrainItem.callback = OnClickTrain;
            myTrainItem.Init();
        }

        void OnTogOtherTruck(bool isOn)
        {
            m_goMyPlunder.SetActive(isOn);
            m_btnReset.gameObject.SetActive(isOn);
            m_goOtherTruck.SetActive(isOn);
            m_goTruckCard.SetActive(false);
            m_transTruckSelected.gameObject.SetActive(false);
            if (isOn)
            {
                isShowOther = true;
                RefreshPlunderCount();
                m_goPlayerInfo.SetActive(true);
                m_goOtherTrainItem.SetActive(hasOtherTrain && isShowOther);
                m_goMyTrainItem.SetActive(false);
            }
        }

        void OnTogMyTruck(bool isOn)
        {
            m_goMyTrade.SetActive(isOn);
            m_goMyTruck.SetActive(isOn);
            m_goTruckCard.SetActive(false);
            m_transTruckSelected.gameObject.SetActive(false);
            if (isOn)
            {
                isShowOther = false;
                RefreshTradeCount();
                m_goPlayerInfo.SetActive(false);
                m_goOtherTrainItem.SetActive(false);

                TrainStatus trainStatus = GameEntry.TradeTruckData.GetTrainStatus();
                ColorLog.Pink("火车状态", trainStatus);
                m_goMyTrainItem.SetActive(hasMyTrain && !isShowOther && trainStatus == TrainStatus.Depart);
            }
        }

        void OnTogFilterTruck(bool isOn)
        {
            GameEntry.TradeTruckData.IsFilterTruck = isOn;
            // 请求他人货车列表
            ColorLog.Pink("是否过滤本战区", GameEntry.TradeTruckData.IsFilterTruck);
            GameEntry.TradeTruckData.RequestTruckList(2, GameEntry.TradeTruckData.IsFilterTruck, (result) =>
            {
                ColorLog.Pink("他人货车列表", result);
                RefreshOtherTruck();
                PlaceTrucksOnGrid();

                hasOtherTrain = GameEntry.TradeTruckData.otherTrain != null;
                m_goOtherTrainItem.SetActive(hasOtherTrain && isShowOther);

                UITrainItem otherTrainItem = m_goOtherTrainItem.GetComponent<UITrainItem>();
                otherTrainItem.trainData = GameEntry.TradeTruckData.otherTrain;

                if (m_goOtherTrainItem.activeInHierarchy)
                {
                    Transform playerInfo = m_goOtherTrainItem.transform.Find("playerInfo");
                    if (otherTrainItem.trainData != null
                    && otherTrainItem.trainData.Boxcar.Count > 0
                    && otherTrainItem.trainData.Boxcar[0].Passengers.Count > 0)
                    {
                        ulong roleId = otherTrainItem.trainData.Boxcar[0].Passengers[0].RoleId;
                        RefreshTruckPlayerInfo(playerInfo, roleId);
                    }
                }
            });
            m_goTruckCard.SetActive(false);
            m_transTruckSelected.gameObject.SetActive(false);
        }

        void DefaultDisplay()
        {
            m_goMyTrade.SetActive(false);
            m_goMyPlunder.SetActive(true);
            m_goMyTruck.SetActive(false);
            m_goTruckCard.SetActive(false);
            m_goNoTradeCount.SetActive(false);

            m_togOtherTruck.isOn = true;
        }

        void RefreshPanel()
        {
            RefreshMyTruck();
            RefreshOtherTruck();
            RefreshTradeCount();
            RefreshPlunderCount();
        }

        /// <summary>
        /// 刷新我的货车
        /// </summary>
        void RefreshMyTruck()
        {
            int index = 1;
            foreach (var item in myTruckItems)
            {
                int needTeamIndex = index;
                // 解锁 2 小队之后获得 1、2 车位
                if (index == 1)
                {
                    needTeamIndex = 2;
                }
                bool teamIsUnlock = GameEntry.LogicData.BuildingData.GetTeamIsUnlock(needTeamIndex);
                item.truckState = teamIsUnlock ? TruckState.Idle : TruckState.Lock;

                for (int i = 0; i < GameEntry.TradeTruckData.MyTruckList.Count; i++)
                {
                    // 找到匹配的数据
                    if (item.index == GameEntry.TradeTruckData.MyTruckList[i].Position)
                    {
                        if (GameEntry.TradeTruckData.MyTruckList[i].TradeId == 0) continue;
                        item.truckData = GameEntry.TradeTruckData.MyTruckList[i];

                        // 运输中
                        if (item.truckData.ArrivalTime > 0)
                        {
                            item.truckState = TruckState.Depart;

                            // 已到站
                            long remainTime = item.truckData.ArrivalTime - (long)TimeComponent.Now;
                            if (remainTime <= 0)
                            {
                                item.truckState = TruckState.Reward;
                            }
                        }

                        // 货车品质
                        trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(item.truckData.TradeId);
                        if (truckConfig != null)
                        {
                            car_quality quality = truckConfig.car_quality;
                            item.quality = (int)quality;
                        }
                    }
                }

                // 刷新车位显示
                item.Refresh();
                SetParticleSystemSortingOrder(item.gameObject, Depth);

                index++;
            }

            // 添加已激活的车位
            activeTruckItems.Clear();
            foreach (var item in myTruckItems)
            {
                if (item.truckState == TruckState.Depart || item.truckState == TruckState.Reward)
                {
                    activeTruckItems.Add(item);
                }
            }
        }

        void RefreshOtherTruck()
        {
            int index = 0;
            foreach (UITruckItem item in otherTruckItems)
            {
                if (index < GameEntry.TradeTruckData.OtherTruckList.Count)
                {
                    item.truckData = GameEntry.TradeTruckData.OtherTruckList[index];
                    // 货车品质
                    trade truckConfig = GameEntry.TradeTruckData.GetTruckConfigByID(item.truckData.TradeId);
                    car_quality quality = truckConfig.car_quality;
                    item.quality = (int)quality;
                    item.Refresh();
                    SetParticleSystemSortingOrder(item.gameObject, Depth);
                    index++;
                    item.gameObject.SetActive(true);
                }
                else
                {
                    item.gameObject.SetActive(false);
                }
            }
        }

        void RefreshPlunderCount()
        {
            int count = GameEntry.TradeTruckData.TruckPlunderTodayCount;
            int countMax = 4;
            if (count >= countMax) count = countMax;
            m_txtTodayPlunderCount.text = $"{ToolScriptExtend.GetLang(1115)}{count}/{countMax}";
            m_goNoTradeCount.SetActive(!isShowOther && count >= countMax);
        }

        void RefreshTradeCount()
        {
            int count = GameEntry.TradeTruckData.TruckTradeTodayCount;
            int countMax = 4;
            if (count >= countMax) count = countMax;
            m_txtTodayTradeCount.text = $"{ToolScriptExtend.GetLang(1121)}{count}/{countMax}";
            m_goNoTradeCount.SetActive(!isShowOther && count >= countMax);
        }

        void OnClickTruck(UITruckItem truck)
        {
            bool isOther = truck.isOther;
            TruckState truckState = truck.truckState;

            if (isOther)
            {
                m_goTruckCard.SetActive(true);
                // otherTruckIndex = truck.index;

                List<Trade.TradeGoods> rewards = new(truck.truckData.Boxcar[0].Goods);
                RefreshReward(rewards);

                m_transTruckSelected.gameObject.SetActive(true);
                AnchorUIToTarget(m_transTruckSelected.transform, truck.transform, new Vector2(0, -17f));
            }
            else
            {
                if (truckState == TruckState.Depart || truckState == TruckState.Reward)
                {
                    m_goTruckCard.SetActive(true);
                    List<Trade.TradeGoods> rewards = new(truck.truckData.Boxcar[0].Goods);
                    RefreshReward(rewards);

                    m_transTruckSelected.gameObject.SetActive(true);
                    AnchorUIToTarget(m_transTruckSelected.transform, truck.transform, new Vector2(0, -17f));
                }
                else if (truckState == TruckState.Lock || truckState == TruckState.Idle)
                {
                    return;
                }
            }

            curSelectedTruck = truck;
            curSelectedTrain = null;

            m_imgTruckCardQualityBg.SetImage($"Sprite/ui_maoyi/maoyi_car_dikuang{truck.quality}.png", false);
            m_imgTruckCardIcon.SetImage($"Sprite/ui_maoyi/maoyi_dis_car{truck.quality}.png", true);
            m_imgTruckCardIcon.GetComponent<RectTransform>().anchoredPosition = new Vector2(-24f, -14f);
            m_imgTruckCardQuality.SetImage(ToolScriptExtend.GetQualityIcon(truck.quality), true);

            m_btnPlunderOther.gameObject.SetActive(isOther);
            m_btnViewMy.gameObject.SetActive(!isOther);

            if (truck.truckData != null)
            {
                GameEntry.RoleData.RequestRoleQueryLocalSingle(truck.truckData.RoleId, (roleBrief) =>
                {
                    ColorLog.Pink("查询玩家信息", roleBrief);
                    if (roleBrief != null)
                    {
                        m_txtTruckCardLevel.text = $"Lv.{roleBrief.Level}";
                        m_txtTruckCardName.text = roleBrief.Name;
                        m_txtTruckCardPower.text = ToolScriptExtend.FormatNumberWithSeparator(roleBrief.Power);
                    }
                });
            }
        }

        void OnClickTrain(UITrainItem train)
        {
            curSelectedTruck = null;
            curSelectedTrain = train;

            m_goTruckCard.SetActive(true);
            m_transTruckSelected.gameObject.SetActive(true);
            AnchorUIToTarget(m_transTruckSelected.transform, train.transform, new Vector2(0, -117f));

            m_imgTruckCardQualityBg.SetImage($"Sprite/ui_maoyi/maoyi_car_dikuang5.png", false);
            m_imgTruckCardIcon.SetImage($"Sprite/ui_maoyi/maoyi_dis_car6.png", false);
            m_imgTruckCardIcon.GetComponent<RectTransform>().anchoredPosition = new Vector2(-24f, 0f);
            m_imgTruckCardQuality.SetImage(ToolScriptExtend.GetQualityIcon(5), true);

            if (train.isOther)
            {
                m_btnPlunderOther.gameObject.SetActive(true);
                m_btnViewMy.gameObject.SetActive(false);
            }
            else
            {
                m_btnPlunderOther.gameObject.SetActive(false);
                m_btnViewMy.gameObject.SetActive(true);
            }

            if (train.trainData != null)
            {
                List<Trade.TradeGoods> rewards = new();
                for (int i = 0; i < train.trainData.Boxcar.Count; i++)
                {
                    rewards.AddRange(train.trainData.Boxcar[i].Goods);
                }
                RefreshReward(rewards);

                GameEntry.RoleData.RequestRoleQueryLocalSingle(train.trainData.RoleId, (roleBrief) =>
                {
                    ColorLog.Pink("查询玩家信息", roleBrief);
                    if (roleBrief != null)
                    {
                        m_txtTruckCardLevel.text = $"Lv.{roleBrief.Level}";
                        m_txtTruckCardName.text = roleBrief.Name;
                        m_txtTruckCardPower.text = ToolScriptExtend.FormatNumberWithSeparator(roleBrief.Power);
                    }
                });
            }
        }

        void SwitchNextTruck(int direction)
        {
            if (isShowOther)
            {
                if (tradeItems.Count == 0) return;
                otherTruckIndex += direction;
                if (otherTruckIndex < 0) otherTruckIndex = tradeItems.Count - 1;
                if (otherTruckIndex >= tradeItems.Count) otherTruckIndex = 0;
                var item = tradeItems[otherTruckIndex];
                if (item is UITruckItem)
                {
                    OnClickTruck(item as UITruckItem);
                }
                else if (item is UITrainItem)
                {
                    OnClickTrain(item as UITrainItem);
                }
            }
            else
            {
                myTruckIndex += direction;
                if (myTruckIndex < 0) myTruckIndex = activeTruckItems.Count - 1;
                if (myTruckIndex >= activeTruckItems.Count) myTruckIndex = 0;
                OnClickTruck(activeTruckItems[myTruckIndex]);
            }
        }

        void RefreshReward(List<Trade.TradeGoods> rewards)
        {
            foreach (Transform item in m_transContentReward)
            {
                item.gameObject.SetActive(false);
            }

            rewards.Sort((a, b) => a.Rob.CompareTo(b.Rob));

            for (int i = 0; i < rewards.Count; i++)
            {
                bool rob = rewards[i].Rob;
                if (i < m_transContentReward.childCount)
                {
                    m_transContentReward.GetChild(i).gameObject.SetActive(true);
                    UIItemModule uiItemModule = m_transContentReward.GetChild(i).GetChild(0).GetComponent<UIItemModule>();
                    uiItemModule.SetData((itemid)rewards[i].Code, rewards[i].Amount);
                    uiItemModule.InitConfigData();
                    uiItemModule.DisplayInfo();
                    uiItemModule.plunder.SetActive(rob);
                }
                else
                {
                    Transform item = Instantiate(m_transReward, m_transContentReward);
                    BagManager.CreatItem(item, (itemid)rewards[i].Code, rewards[i].Amount, (item) =>
                    {
                        item.GetComponent<UIButton>().useTween = false;
                        item.SetClick(item.OpenTips);
                        item.SetScale(0.5f);
                        item.plunder.SetActive(rob);
                    });
                }
            }
        }

        void PlaceTrucksOnGrid()
        {
            CalculateGridDimensions();

            foreach (var item in activePlayerInfo)
            {
                playerInfoPool.Release(item);
            }
            activePlayerInfo.Clear();

            int colMax = gridCols;
            if (hasOtherTrain)
            {
                colMax = 5;
            }

            occupiedCells = new bool[gridRows, colMax];

            RectTransform containerRect = m_goOtherTruck.GetComponent<RectTransform>();
            float cellWidth = containerRect.rect.width / gridCols;
            float cellHeight = containerRect.rect.height / gridRows;

            foreach (UITruckItem truck in otherTruckItems)
            {
                int row, col;
                do
                {
                    row = Random.Range(0, gridRows);
                    col = Random.Range(0, colMax);
                } while (occupiedCells[row, col]);

                occupiedCells[row, col] = true;

                float baseX = -containerRect.rect.width / 2 + cellWidth * (col + 0.5f);
                float baseY = containerRect.rect.height / 2 - cellHeight * (row + 0.5f);

                float offsetX = Random.Range(-OFFSET_RANGE_X, OFFSET_RANGE_X);
                float offsetY = Random.Range(-OFFSET_RANGE_Y, OFFSET_RANGE_Y);

                RectTransform truckRect = truck.GetComponent<RectTransform>();
                truckRect.anchoredPosition = new Vector2(baseX + offsetX, baseY + offsetY);

                truck.index = row * gridCols + col;
                truck.Refresh();

                if (truck.gameObject.activeInHierarchy && truck.quality == 5)
                {
                    GameObject obj = playerInfoPool.Get();
                    obj.transform.SetParent(m_goPlayerInfo.transform);
                    obj.transform.localPosition = Vector3.zero;
                    obj.transform.localScale = Vector3.one;
                    RefreshTruckPlayerInfo(obj.transform, truck.truckData.RoleId);
                    AnchorUIToTarget(obj.transform, truck.transform);
                    activePlayerInfo.Add(obj);
                }
            }

            otherTruckItems.Sort((a, b) => a.index.CompareTo(b.index));

            if (isShowOther)
            {
                tradeItems.Clear();
                otherTruckIndex = 0;
                int index = 0;
                for (int i = 0; i < otherTruckItems.Count; i++)
                {
                    if (otherTruckItems[i].gameObject.activeInHierarchy)
                    {
                        tradeItems.Add(otherTruckItems[i]);
                        otherTruckItems[i].index = index;
                        index++;
                    }
                }
                if (hasOtherTrain)
                {
                    tradeItems.Add(m_goOtherTrainItem.GetComponent<UITrainItem>());
                }
            }
        }

        void CalculateGridDimensions()
        {
            RectTransform containerRect = m_goOtherTruck.GetComponent<RectTransform>();
            float aspectRatio = (float)Screen.width / Screen.height;

            if (aspectRatio >= 0.7f) // 3:4
            {
                gridRows = 3;
                gridCols = 6;

                containerRect.offsetMin = new Vector2(150f, 620f);
            }
            else if (aspectRatio >= 0.6f) // 10:16
            {
                gridRows = 4;
                gridCols = 6;

                containerRect.offsetMin = new Vector2(150f, 620f);
            }
            else if (aspectRatio >= 0.49f) // 9:16
            {
                gridRows = 4;
                gridCols = 6;

                containerRect.offsetMin = new Vector2(150f, 760f);
            }
            else if (aspectRatio >= 0.45f) // 9:20
            {
                gridRows = 6;
                gridCols = 6;

                containerRect.offsetMin = new Vector2(150f, 760f);
            }
            else // Default fallback for other aspect ratios
            {
                gridRows = 6;
                gridCols = 6;

                containerRect.offsetMin = new Vector2(150f, 760f);
            }

            int totalCells = gridRows * gridCols;
            if (totalCells < TRUCK_COUNT)
            {
                while (gridRows * gridCols < TRUCK_COUNT)
                {
                    if (gridRows <= gridCols)
                        gridRows++;
                    else
                        gridCols++;
                }
            }
        }

        void RefreshTruckPlayerInfo(Transform parent, ulong roldId)
        {
            UIText txtName = parent.Find("name/txtName").GetComponent<UIText>();
            GameEntry.RoleData.RequestRoleQueryLocalSingle(roldId, (roleBrief) =>
            {
                ColorLog.Pink("查询玩家信息", roleBrief);
                if (roleBrief != null)
                {
                    GameEntry.LogicData.UnionData.OnReqUnionBrief(roleBrief.UnionId, (data) =>
                    {
                        if (data != null)
                        {
                            txtName.text = $"<color=\"#00c0ff\">[{data.ShortName}]</color>{roleBrief.Name}";
                        }
                    });
                }
            });
        }

        public void AnchorUIToTarget(Transform self, Transform target, Vector2 offset = default)
        {
            Canvas canvas = GetComponentInParent<Canvas>();
            RectTransform selfRect = self.GetComponent<RectTransform>();
            if (this == null || selfRect == null || canvas == null) return;

            Vector3 worldPos = target.TransformPoint(target.position);
            Vector2 screenPos = RectTransformUtility.WorldToScreenPoint(canvas.renderMode == RenderMode.ScreenSpaceOverlay ? null : canvas.worldCamera, worldPos);
            Vector2 localPos = ScreenToLocalInCanvas(canvas, selfRect.parent as RectTransform, screenPos);
            selfRect.localPosition = localPos + offset;
        }

        Vector2 ScreenToLocalInCanvas(Canvas canvas, RectTransform targetParent, Vector2 screenPosition)
        {
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                targetParent,
                screenPosition,
                canvas.renderMode == RenderMode.ScreenSpaceOverlay ? null : canvas.worldCamera,
                out Vector2 localPoint
            );
            return localPoint;
        }

        GameObject OnCreate()
        {
            GameObject obj = Instantiate(m_goPlayerInfoItem);
            return obj;
        }

        void OnGet(GameObject obj)
        {
            obj.SetActive(true);
        }

        void OnRelease(GameObject obj)
        {
            obj.SetActive(false);
        }

        void OnObjectDestroy(GameObject obj)
        {
            Destroy(obj);
        }
    }
}
