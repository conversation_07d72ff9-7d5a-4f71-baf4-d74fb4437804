%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Battle_mat_anniuliuguang2 12
  m_Shader: {fileID: 4800000, guid: a4d9929ce9100df4a8d0e0771ba6f2d3, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: a1d8e6deb1cd52f4ab5dd7f5f3c408d0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample0:
        m_Texture: {fileID: 2800000, guid: 2918be393fb959e41b7204c7ce350925, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TextureSample1:
        m_Texture: {fileID: 2800000, guid: dbeb3adaf92e5d64d94ade96d3e159c4, type: 3}
        m_Scale: {x: 0.68, y: 0.34}
        m_Offset: {x: 0, y: 0.3}
    - _TextureSample2:
        m_Texture: {fileID: 2800000, guid: 81b29c050d0f8ea47b5a4eb0233b9f88, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _ColorMask: 15
    - _Float0: 0.6
    - _Float2: -2
    - _Interval: 1
    - _InvFade: 1
    - _SpeedX: 1
    - _SpeedY: 0
    - _Stencil: 0
    - _StencilComp: 8
    - _StencilOp: 0
    - _StencilReadMask: 255
    - _StencilWriteMask: 255
    - _UseUIAlphaClip: 0
    m_Colors:
    - _Area: {r: -5000, g: -5000, b: 5000, a: 5000}
    - _Color0: {r: 0.7490196, g: 0.70980614, b: 0.667758, a: 1}
    - _Color1: {r: 0.7490197, g: 0.42423195, b: 0.074195355, a: 1}
    - _TintColor: {r: 0.6039216, g: 0.6039216, b: 0.6039216, a: 0.52156866}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
