using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPioneerRewardForm : UGuiFormEx
    {
        [SerializeField] private Button m_btnClose;

        [SerializeField] private Slider m_sliderProgress;

        [SerializeField] private GameObject m_goRoot;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goRewardItem;

        void InitBind()
        {
            m_btnClose.onClick.AddListener(OnBtnCloseClick);
        }
    }
}
