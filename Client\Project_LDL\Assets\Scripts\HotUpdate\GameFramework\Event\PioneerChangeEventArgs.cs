using GameFramework;
using GameFramework.Event;

namespace Game.Hotfix
{
    public class PioneerChangeEventArgs:GameEventArgs
    {
        public static readonly int EventId = typeof(PioneerChangeEventArgs).GetHashCode();

        public PioneerChangeEventArgs()
        {
            
        }

        public override int Id
        {
            get
            {
                return EventId;
            }
        }

        public static PioneerChangeEventArgs Create()
        {
            PioneerChangeEventArgs args = ReferencePool.Acquire<PioneerChangeEventArgs>();
            return args;
        }

        public override void Clear()
        {
            
        }
    }
}