using System;
using System.Collections.Generic;
using System.Linq;
using Fight;
using Google.Protobuf.Collections;
using Roledata;
using Team;
using UnityEngine;

namespace Game.Hotfix
{
    public class TeamData
    {
        private Dictionary<TeamType, List<FormationHero>> m_Teams;
        private Dictionary<TeamType, TeamStatus> teamStatusDic;
        public List<DefendTeam> defendTeamList;

        public void Init(RoleTeam roleTeam)
        {
            m_Teams = new Dictionary<TeamType, List<FormationHero>>();
            teamStatusDic = new();
            defendTeamList = new();

            if (roleTeam == null)
                return;

            foreach (var team in roleTeam.Teams)
            {
                if (m_Teams.TryAdd(team.TeamType, team.Heroes.ToList()))
                {

                }
                SetTeamStatus(team.TeamType, team.Status);
            }
            GameEntry.LogicData.HeroData.SetHeroTeamId();

            defendTeamList = roleTeam.DefendTeams.ToList();
            InitEvent();
        }

        private void InitEvent()
        {
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushFormationTeam, message =>
            {
                var data = (PushFormationTeam)message;
                OnFormationTeamChange(data.Teams);
            });
            NetEventDispatch.Instance.RegisterEvent((int)Protocol.MessageID.PushTeamStatus, message =>
            {
                var data = (PushTeamStatus)message;
                SetTeamStatus(data.TeamType, data.Status);
            });
        }

        private void OnFormationTeamChange(RepeatedField<FormationTeam> formationTeams)
        {
            // 直接调用公共的数据更新方法
            UpdateTeamDataAndFireEvents(formationTeams.ToList());
        }

        public List<FormationHero> GetTeam(TeamType teamType)
        {
            if (m_Teams.TryGetValue(teamType, out var team))
            {
                return team;
            }

            return null;
        }

        public Dictionary<EnumBattlePos, int> GetTeamDic(TeamType teamType)
        {
            Dictionary<EnumBattlePos, int> dic = new Dictionary<EnumBattlePos, int>();

            if (m_Teams.TryGetValue(teamType, out var team))
            {
                if (team == null)
                {
                    return null;
                }
                foreach (var hero in team)
                {
                    if (hero != null)
                    {

                        dic.TryAdd((EnumBattlePos)hero.Pos, (int)hero.HeroId);

                    }
                }
            }
            return dic;
        }

        public bool IsTeamTypeUnlock(TeamType teamType)
        {
            if (teamType == TeamType.Common3 || teamType == TeamType.Common4)
                return false;
            return true;
        }

        /// <summary>
        /// 判断是否是火车布阵队伍类型
        /// </summary>
        /// <param name="teamType">队伍类型</param>
        /// <returns>是否是火车布阵队伍类型</returns>
        private bool IsTrainFormationTeam(TeamType teamType)
        {
            return teamType == TeamType.TradeTrainAttack1 ||
                   teamType == TeamType.TradeTrainAttack2 ||
                   teamType == TeamType.TradeTrainAttack3 ||
                   teamType == TeamType.TradeTrainDefend1 ||
                   teamType == TeamType.TradeTrainDefend2 ||
                   teamType == TeamType.TradeTrainDefend3;
        }

        /// <summary>
        /// 更新队伍数据并触发事件
        /// </summary>
        /// <param name="formationTeams">编队列表</param>
        private void UpdateTeamDataAndFireEvents(List<FormationTeam> formationTeams)
        {
            foreach (var team in formationTeams)
            {
                List<FormationHero> newList = team.Heroes.ToList();
                bool shouldFireEvent = false;

                if (m_Teams.TryGetValue(team.TeamType, out var oldList))
                {
                    // 已存在的编队，检查是否有变化
                    bool isEqual = oldList.SequenceEqual(newList);
                    if (!isEqual)
                    {
                        shouldFireEvent = true;
                    }
                }
                else
                {
                    // 新的编队类型，总是触发事件
                    shouldFireEvent = true;
                }

                // 先更新字典数据
                m_Teams[team.TeamType] = newList;
                SetTeamStatus(team.TeamType, team.Status);
                GameEntry.LogicData.HeroData.SetHeroTeamId();

                // 再触发事件，确保事件处理函数能获取到最新数据
                if (shouldFireEvent)
                {
                    GameEntry.Event.Fire(TeamChangeEventArgs.EventId, TeamChangeEventArgs.Create(team.TeamType));
                }
            }
        }

        private void SetTeamStatus(TeamType teamType, TeamStatus status)
        {
            teamStatusDic[teamType] = status;
        }

        public TeamStatus GetTeamStatus(TeamType teamType)
        {
            teamStatusDic.TryGetValue(teamType, out var status);
            return status;
        }

        public string GetStatusIcon(TeamStatus status)
        {
            return status switch
            {
                TeamStatus.Garrison => "Sprite/ui_jianzhu_duilie/jianzhu_duilie_touxiangk_jiaobiao1.png",
                TeamStatus.Battle => "Sprite/ui_jianzhu_chengqiang/jianzhu_chengqiang_touxiangk_jiaobiao2.png",
                TeamStatus.Collect => "Sprite/ui_jianzhu_chengqiang/jianzhu_chengqiang_touxiangk_jiaobiao3.png",
                TeamStatus.Gathering => "Sprite/ui_jianzhu_chengqiang/jianzhu_chengqiang_touxiangk_jiaobiao4.png",
                TeamStatus.Moving => "Sprite/ui_jianzhu_chengqiang/jianzhu_chengqiang_touxiangk_jiaobiao5.png",
                _ => "Sprite/ui_jianzhu_duilie/jianzhu_duilie_touxiangk_jiaobiao1.png",
            };
        }

        public void TeamModify(FormationTeam formationTeam, Action callback)
        {
            var list = new List<FormationTeam>();
            list.Add(formationTeam);
            TeamModify(list, callback);
        }

        public void TeamModify(List<FormationTeam> formationTeams, Action callback)
        {
            // 检查是否包含火车布阵队伍类型
            bool hasTrainTeam = formationTeams.Any(team => IsTrainFormationTeam(team.TeamType));

            if (hasTrainTeam)
            {
                // 使用火车布阵协议
                if (GameEntry.TradeTruckData.myTrain != null)
                {
                    GameEntry.TradeTruckData.RequestTrainFormation(formationTeams, GameEntry.TradeTruckData.myTrain.Id, (resp) =>
                    {
                        if (resp != null)
                        {
                            // 更新本地数据并触发事件
                            UpdateTeamDataAndFireEvents(formationTeams);
                            callback?.Invoke();
                        }
                    });
                }
                else
                {
                    // 没有火车数据，直接回调
                    callback?.Invoke();
                }
            }
            else
            {
                // 使用默认的队伍修改协议
                TeamModifyReq req = new TeamModifyReq();
                req.Teams.AddRange(formationTeams);

                GameEntry.LDLNet.Send(Protocol.MessageID.TeamModify, req, (message) =>
                {
                    if (message is TeamModifyResp)
                    {
                        // 更新本地数据并触发事件
                        UpdateTeamDataAndFireEvents(formationTeams);
                        callback?.Invoke();
                    }
                });
            }
        }

        /// <summary>
        /// 防守队伍配置
        /// </summary>
        /// <param name="teams"></param>
        /// <param name="callback"></param>
        public void TeamDefendModify(List<DefendTeam> teams, Action callback)
        {
            var req = new TeamDefendModifyReq();
            req.Teams.AddRange(teams);

            GameEntry.LDLNet.Send(Protocol.MessageID.TeamDefendModify, req, (message) =>
            {
                defendTeamList = teams;
                callback?.Invoke();
            });
        }
        
        /// <summary>
        /// 请求阵容查询
        /// </summary>
        /// <param name="teamTypes">队伍类型列表</param>
        /// <param name="roleId">查询的玩家 id</param>
        /// <param name="serverId">查询的服务器 id</param>
        /// <param name="callback">回调</param>
        public void RequestTeamQuery(List<TeamType> teamTypes, ulong roleId, uint serverId, Action<TeamQueryResp> callback = null)
        {
            TeamQueryReq req = new()
            {
                TeamTypes = { teamTypes },
                RoleId = roleId,
                ServerId = serverId
            };

            Debug.Log($"请求阵容查询: RoleId={roleId}, ServerId={serverId}, TeamTypes={string.Join(",", teamTypes)}");

            GameEntry.LDLNet.Send(Protocol.MessageID.TeamQuery, req, (message) =>
            {
                TeamQueryResp resp = message as TeamQueryResp;
                if (resp != null)
                {
                    Debug.Log($"阵容查询响应: Teams数量={resp.Teams?.Count ?? 0}");
                    if (resp.Teams != null && resp.Teams.Count > 0)
                    {
                        Debug.Log($"第一个队伍英雄数量: {resp.Teams[0].Heroes?.Count ?? 0}");
                    }
                }
                else
                {
                    Debug.LogError("阵容查询响应为空");
                }
                callback?.Invoke(resp);
            });
        }
    }
}
