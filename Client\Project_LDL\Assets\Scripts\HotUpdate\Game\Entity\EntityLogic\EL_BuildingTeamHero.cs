using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using Game.Hotfix.Config;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Game.Hotfix
{
    class TeamHeroBullet
    {
        public int FireEffect;
        public int BulletEffect;
        public int BombEffect;
    }
    
    public class ED_BuildingTeamHero:EntityData
    {
        public int ParentId;
        public string ParentPath;
        public int Bullet;
        
        public ED_BuildingTeamHero(int entityId,int bullet,int parentId, string parentPath) : base(entityId)
        {
            ParentId = parentId;
            ParentPath = parentPath;
            Bullet = bullet;
        }
    }
    
    public class EL_BuildingTeamHero : Entity
    {
        private List<TeamHeroBullet> m_Bullets = new List<TeamHeroBullet>()
        {
            new TeamHeroBullet()
            {
                FireEffect = 115010101,
                BulletEffect = 115010102,
                BombEffect = 115010104,
            },
            new TeamHeroBullet()
            {
                FireEffect = 115020101,
                BulletEffect = 115020102,
                BombEffect = 115020104,
            }
        };
        
        private BattleSlotHandler m_BattleSlotHandler;
        private Dictionary<slot, Transform> m_SlotsDic;
        
        private Animator m_Animator;
        private int m_BulletIndex;

        private float m_AtkIntervalMin = 3f;
        private float m_AtkIntervalMax = 10f;
        private float m_CurAtkInterval = 0;
        
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);
        }

        protected override void OnShow(object userData)
        {
            base.OnShow(userData);
            
            m_SlotsDic = new Dictionary<slot, Transform>();
            m_BattleSlotHandler = CachedTransform.GetComponent<BattleSlotHandler>();
            if (m_BattleSlotHandler && m_BattleSlotHandler.slots!=null)
            {
                foreach (var slotData in m_BattleSlotHandler.slots)
                {
                    m_SlotsDic.TryAdd(slotData.Slot, slotData.Transform);
                }
            }
            m_Animator = CachedTransform.GetComponentInChildren<Animator>();
            
            ED_BuildingTeamHero data = userData as ED_BuildingTeamHero;
            if (data == null)
            {
                return;
            }

            m_BulletIndex = data.Bullet - 1;
            
            if (data.ParentPath != null)
            {
                GameEntry.Entity.AttachEntity(this.Id, data.ParentId,data.ParentPath);
            }
            else
            {
                GameEntry.Entity.AttachEntity(this.Id, data.ParentId);
            }
            
            Vector3 displayPos = Vector3.zero;
            Quaternion displayRotation = Quaternion.Euler(0,0,0);
            Vector3 displayScale = Vector3.one;
            if (data.Position != null)
            {
                displayPos = data.Position;
            }

            if (data.Rotation != null)
            {
                displayRotation = data.Rotation;
            }

            if (data.Scale != null)
            {
                displayScale = data.Scale;
            }

            transform.localPosition = displayPos;
            transform.localRotation = displayRotation;
            transform.localScale = displayScale;
            
        }

        private void Update()
        {
            if (m_CurAtkInterval > 0)
            {
                m_CurAtkInterval -= Time.deltaTime;
            }
        }

        protected override void OnHide(bool isShutdown, object userData)
        {
            base.OnHide(isShutdown, userData);
        }

        public bool CanAtk()
        {
            return m_CurAtkInterval <= 0;
        }
        
        public float PlayAtk(Vector3 targetPos)
        {
            Vector3 fromPos = GetSlotPosition(slot.slot_0);
            Vector3 toPos = targetPos;
            float speed = 50f;
            float flyTime = (fromPos - toPos).magnitude / speed;
            
            m_CurAtkInterval = Random.Range(m_AtkIntervalMin,m_AtkIntervalMax);
            m_Animator.SetTrigger("skill_1");
            StartCoroutine(PlayAtkE(flyTime,targetPos));

            return flyTime;
        }

        private IEnumerator PlayAtkE(float flyTime,Vector3 targetPos)
        {
            var bullet = GetBullet();
            if (bullet != null)
            {
                if (bullet.FireEffect != 0)
                {
                    GameEntry.Effect.CreateEffect(bullet.FireEffect, null, GetSlotPosition(slot.slot_0),
                        GetSlotRotation(slot.slot_0));
                }

                if (bullet.BulletEffect != 0)
                {
                    Vector3 fromPos = GetSlotPosition(slot.slot_0);
                    Vector3 toPos = targetPos;

                    var effect = GameEntry.Effect.CreateEffect(bullet.BulletEffect, null, GetSlotPosition(slot.slot_0),
                        Quaternion.LookRotation((toPos - fromPos).normalized));

                    
                    DOTween.To(() => 0, curValue =>
                    {
                        effect.SetPosition(Vector3.Lerp(fromPos, toPos, curValue));
                    }, 1f, flyTime).OnComplete(() =>
                    {
                        GameEntry.Effect.RemoveEffect(effect);
                    }).SetEase(Ease.Linear);
                }
                
                yield return new WaitForSeconds(flyTime);
            
                if (bullet.BombEffect != 0)
                {
                    GameEntry.Effect.CreateEffect(bullet.BombEffect, null, targetPos);
                }
            }
        }

        private TeamHeroBullet GetBullet()
        {
            if (m_BulletIndex < m_Bullets.Count)
            {
                return m_Bullets[m_BulletIndex];
            }

            return null;
        }
        
        public Vector3 GetSlotPosition(slot slot)
        {
            return GetSlot(slot).position;
        }

        public Quaternion GetSlotRotation(slot slot)
        {
            return GetSlot(slot).rotation;
        }

        public Transform GetSlot(slot slot)
        {
            if (m_SlotsDic.TryGetValue(slot, out var value))
            {
                return value;
            }
            return CachedTransform;
        }
        
    }
}
