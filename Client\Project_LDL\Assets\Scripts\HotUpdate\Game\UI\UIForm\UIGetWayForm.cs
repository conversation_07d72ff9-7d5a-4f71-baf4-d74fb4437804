
using System.Collections.Generic;
using Game.Hotfix.Config;
using GameFramework.Event;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public class GetWayModule{
        public obtaintype type;
        public int priority = 0;
        public itemid itemid;
        public int id;
        public GetWayModule(obtaintype _type,int _priority,itemid _itemid,int _id){
            type = _type;
            priority = _priority;
            itemid = _itemid;
            id = _id;
        }
         
    }
    public partial class UIGetWayForm : UGuiFormEx
    {
        public List<ItemModule> itemModules = new List<ItemModule>();
        public Dictionary<int,ItemModule> togDic = new Dictionary<int, ItemModule>();
        public Dictionary<int,List<get_way>> configDic = new Dictionary<int, List<get_way>>();
        public Dictionary<int,List<GetWayModule>> tableDic = new Dictionary<int, List<GetWayModule>>();
        public Dictionary<int,int> giftDic = new Dictionary<int, int>();
        //public List<UIItemModule> GiftItemModule = new List<UIItemModule>();
        public bool isClickSearch = false;
        public bool isHaveBtn = true;
        public bool isHaveTog = false;
        public bool isHaveTitle = true;
        public int clickGroup = 0;
        public List<UIToggle> tabToggList;
        public int clickTabIndex = 1;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
            tabToggList = new List<UIToggle> { m_tog1, m_tog2, m_tog3, m_tog4 };

            foreach (var item in tabToggList)
            {
                item.onValueChanged.AddListener((isOn) =>
                {
                    if (isOn)
                    {
                        clickTabIndex = item.toggleType;
                        ClickTab();
                        m_TableViewV.GetItemCount = () => { return GetTabCount(); };
                        m_TableViewV.ReloadData();
                    }
                });
            }
            // for (int i = 0; i < m_goGiftContent.transform.childCount; i++)
            // {
            //     UIItemModule uIItemModule = m_goGiftContent.transform.GetChild(i).GetComponent<UIItemModule>();
            //     if(uIItemModule != null){
            //         GiftItemModule.Add(uIItemModule);
            //     }
            // }
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            clickTabIndex = 1;
            //itemModules = userData as List<ItemModule>;
            ItemModule[] arr = userData as ItemModule[];
            foreach (var item in arr)
            {
              itemModules.Add(item);
              if(item.GetWayCount==0)
              {
                isClickSearch = true;
              }
            }

            if(isClickSearch){
                m_txtTitle.text = arr[0].GetItemName();
            }else
            {
                m_txtTitle.text = ToolScriptExtend.GetLang(1100222);
            }

            for (int i = 1; i <= itemModules.Count; i++)
            {
              togDic[i] = itemModules[i-1];  
            }
            clickGroup = int.Parse(GetGroupByItemModule(togDic[clickTabIndex]));
            //init bool
            if(itemModules.Count > 1){
                isHaveTog = true;
            }

            List <get_way> configs = GameEntry.LDLTable.GetTable<get_way>();
            foreach (var data in configs)
            {
                if(GetGroupByItemModule(itemModules[0])==data.access_group_id.ToString()){
                    isHaveBtn = data.obtain_supplement_button;
                    isHaveTitle = data.item_info_type != iteminfotype.item_info_type_none;
                    break;
                }
            }
            
            for (int i = 1; i <= itemModules.Count; i++)
            {
                if(!configDic.ContainsKey(i)){
                    configDic[i] = new List<get_way>();
                }
                if(!tableDic.ContainsKey(i)){
                    tableDic[i] = new List<GetWayModule>();
                }

                int group = int.Parse(GetGroupByItemModule(itemModules[i-1]));
                foreach (var config in configs)
                {
                    if(!giftDic.ContainsKey(i) && config.access_group_id >0 && GetGroupByItemModule(itemModules[i-1]) == config.access_group_id.ToString() ){
                        giftDic[i]= config.obtain_gift_pack;
                    }
                    if (clickGroup == config.access_group_id)
                    { // && (config.obtain_type == obtaintype.obtain_type_item_show_type || config.obtain_type == obtaintype.obtain_type_diamond_buy)
                      //if(group == config.access_group_id){
                        configDic[i].Add(config);
                        //}
                        if (config.obtain_type == obtaintype.obtain_type_item_show_type)
                        {
                            List<ItemModule> itemModules = GameEntry.LogicData.BagData.GetListByGroup((itemshowtype)config.obtain_value);
                            if (itemModules.Count > 0)
                            {
                                foreach (var module in itemModules)
                                {
                                    tableDic[i].Add(new GetWayModule(config.obtain_type, config.priority + (int)module.GetItemConfig().quality, module.ItemId, config.id));
                                }
                            }
                        }
                        else if (config.obtain_type == obtaintype.obtain_type_diamond_buy)
                        {
                            tableDic[i].Add(new GetWayModule(config.obtain_type, config.priority, (itemid)config.obtain_value, config.id));
                        }
                        else if(config.obtain_type == obtaintype.obtain_type_diamond_buy_pullrod)
                        {
                            togDic[i].SetGetWayCount(9999);
                            tableDic[i].Add(new GetWayModule(config.obtain_type, config.priority, itemModules[i-1].ItemId, config.obtain_value));
                        }
                        // else
                        // {                          
                        //     tableDic[i].Add(new GetWayModule(config.obtain_type,config.priority,itemid.itemid_nil,config.id));   
                        // }
                    }          
                }
            }
            //sort
            foreach (var dic in tableDic)
            {
              dic.Value.Sort((a, b) => a.priority.CompareTo(b.priority));   
            }

            
            m_TableViewV.GetItemCount = () => { return GetTabCount();};
            m_TableViewV.GetItemGo = () =>
            {
                return m_goCell;
            };
            m_TableViewV.UpdateItemCell = UpdateLine;
            if (m_TableViewV.itemPrototype == null)
            {
                m_TableViewV.InitTableViewByIndex(0, 12);
            }
            else{
               m_TableViewV.ReloadData(); 
            }
            
            InitPanel();
            ClickTab();
            GameEntry.Event.Subscribe(BagChangeEventArgs.EventId, OnBagChangeUpdate);
            
        }

        private void OnBagChangeUpdate(object sender, GameEventArgs e)
        {
            List<GetWayModule> newGetWay = tableDic[clickTabIndex];
            foreach (var item in newGetWay)
            {
                var data = GameEntry.LDLTable.GetTableById<item_config>(item.itemid);
                 if(!data.auto_use && GameEntry.LogicData.BagData.GetAmountById(item.itemid)<= 0){
                    newGetWay.Remove(item);
                    break;
                }
            }
            m_TableViewV.ReloadData();
            ClickTab();
            CheckClose();
        }


        public void UpdateLine(int index, GameObject itemObj){
            GetWayModule data = tableDic[clickTabIndex][index];
            UIImage icon = itemObj.transform.Find("Node/Item/icon").GetComponent<UIImage>(); 
            UIImage quality = itemObj.transform.Find("Node/Item/quality").GetComponent<UIImage>(); 
            UIText name = itemObj.transform.Find("Node/Item/name").GetComponent<UIText>(); 
            UIText des = itemObj.transform.Find("Node/Item/des").GetComponent<UIText>();
            UIText btnName = itemObj.transform.Find("Node/btn/btnName").GetComponent<UIText>();
            UIButton btn = itemObj.transform.Find("Node/btn").GetComponent<UIButton>();
            UIImage imgBtn = btn.transform.GetComponent<UIImage>();
            UIButton btnBuy = itemObj.transform.Find("Node/btnBuy").GetComponent<UIButton>();
            UIText btnBuyName = itemObj.transform.Find("Node/btnBuy/btnName").GetComponent<UIText>();
            Transform autoUse = itemObj.transform.Find("Node/autoUse");
            //imgBtn?.SetImage("Sprite/ui_public/button3.png");
            //autoUse.gameObject.SetActive(false);
            btnBuy.gameObject.SetActive(false);
            btn.gameObject.SetActive(false);
            if(data.type == obtaintype.obtain_type_item_show_type){
                btn.gameObject.SetActive(true);
               if(data.itemid != itemid.itemid_nil){
                    item_config config = GameEntry.LDLTable.GetTableById<item_config>(data.itemid);
                    icon.SetImage(ToolScriptExtend.GetItemIcon(data.itemid));
                    quality.SetImage(ToolScriptExtend.GetQualityBg(config.quality));
                    name.text = ToolScriptExtend.GetLang(config.name);
                    des.text = ToolScriptExtend.GetLang(1100061)+GameEntry.LogicData.BagData.GetAmountById(data.itemid);
                    btnName.text = ToolScriptExtend.GetLang(1100001);
    
                    if(!isClickSearch){
                        autoUse.gameObject.SetActive(true);
                        UIButton btnAutoUse = autoUse.Find("btnAutoUse").GetComponent<UIButton>();
                        UIText txtAuto = autoUse.Find("btnAutoUse/txtAuto").GetComponent<UIText>();
                        if(btnAutoUse!=null){
                            ItemModule itemModule = new ItemModule(data.itemid,GameEntry.LogicData.BagData.GetAmountById(data.itemid));
                            itemModule.SetGetWayCount(togDic[clickTabIndex].GetWayCount);
                            int autoUseNum = itemModule.GetAutoUseNum();
                            txtAuto.text = "x" + autoUseNum.ToString();

                            btnAutoUse.onClick.RemoveAllListeners();
                            btnAutoUse.onClick.AddListener(()=>
                            {
                                List<ItemModule> list = new List<ItemModule>();
                                itemModule.Count = autoUseNum;
                                list.Add(itemModule);
                                GameEntry.LogicData.BagData.OnReqItemUse(list);
                            });                                                          
                        }
                    }else{
                       autoUse.gameObject.SetActive(false);
                    }
                }             
            }else if(data.type == obtaintype.obtain_type_diamond_buy || data.type == obtaintype.obtain_type_diamond_buy_pullrod){
                item_config config = GameEntry.LDLTable.GetTableById<item_config>(data.itemid);
                btnBuy.gameObject.SetActive(true);
                autoUse.gameObject.SetActive(false);
                if (config != null)
                {
                    icon.SetImage(ToolScriptExtend.GetItemIcon(data.itemid));
                    quality.SetImage(ToolScriptExtend.GetQualityBg(config.quality));
                    name.text = ToolScriptExtend.GetLang(config.name);
                    btnBuyName.text = ToolScriptExtend.GetLang(1100057);
                    //imgBtn?.SetImage("Sprite/ui_public/button4.png"); 
                    UIText txtPrice = itemObj.transform.Find("Node/btnBuy/txtPrice").GetComponent<UIText>();
                    txtPrice.text = config.diamond.ToString();
                    if (data.type == obtaintype.obtain_type_diamond_buy_pullrod)
                    {
                        ItemModule module= new ItemModule(data.itemid,1);
                        des.text = module.GetItemDes();
                    }
                    else
                    {
                        des.text = ToolScriptExtend.GetLang(1100061)+GameEntry.LogicData.BagData.GetAmountById(data.itemid);   
                    }
                    
                }
                       
            }
            
            btn.onClick.RemoveAllListeners();
            btn.onClick.AddListener(() =>
            {
               if(data.type == obtaintype.obtain_type_item_show_type){
                    item_config config = GameEntry.LDLTable.GetTableById<item_config>(data.itemid);
                    var BagData = GameEntry.LogicData.BagData;
                    if(config.item_subtype== itemsubtype.itemsubtype_choosechest)
                    {
                        ItemModule itemModule1 = new ItemModule(data.itemid,BagData.GetAmountById(data.itemid));
                        itemModule1.SetChooseCount(1);
                        GameEntry.UI.OpenUIForm(EnumUIForm.UIChooseBoxForm,itemModule1); 
                    }
                    else{
                        List<ItemModule> list = new List<ItemModule>();
                        ItemModule itemModule = new ItemModule(data.itemid,1);
                        list.Add(itemModule);                   
                        BagData.OnReqItemUse(list,(resp)=>{
                        
                        });
                    }                
                } 
                
            });
            btnBuy.onClick.RemoveAllListeners();
            btnBuy.onClick.AddListener(() =>
            {
                if (data.type == obtaintype.obtain_type_diamond_buy)
                {
                    GameEntry.LogicData.BagData.OnReqBuyItemByDiamond(data.itemid, 1, resp =>
                    {

                    });
                }
                if (data.type == obtaintype.obtain_type_diamond_buy_pullrod)
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIGeneralBuyForm,data.id);
                }
            });
           
        }
        public void InitPanel(){
            if(isHaveTog){
                m_goTogNode.SetActive(true);
                m_goHaveTog.SetActive(true);
                m_goNoTog.SetActive(false);

                RectTransform rectTransform = m_TableViewV.GetComponent<RectTransform>();
                rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, 820);
                for (int i = 0; i <= 3; i++)
                {
                    GameObject tog = m_goTogList.transform.GetChild(i).gameObject;
                    if(i+1<= itemModules.Count){
                        UIImage icon_n = tog.transform.Find("normal/icon").transform.GetComponent<UIImage>();
                        UIImage icon_h = tog.transform.Find("select/icon").transform.GetComponent<UIImage>();
                        icon_n.SetImage(ToolScriptExtend.GetItemIcon(itemModules[i].ItemId));
                        icon_h.SetImage(ToolScriptExtend.GetItemIcon(itemModules[i].ItemId));
                        tog.SetActive(true);
                    }
                    else{
                        tog.SetActive(false);
                    }                  
                }
            }
            else{
                RectTransform rectTransform = m_TableViewV.GetComponent<RectTransform>();
                rectTransform.sizeDelta = new Vector2(rectTransform.sizeDelta.x, 920);
                m_goTogNode.SetActive(false);
                m_goHaveTog.SetActive(false);
                m_goNoTog.SetActive(true);
            }
            if (giftDic.ContainsKey(clickTabIndex))
            {
                OnRefreshGift(giftDic[clickTabIndex]);
            }
        }
        public void OnRefreshGift(int _giftId){
            GameEntry.LogicData.MallData.C2SGiftGetWay((int)_giftId, (giftId) =>
                {
                    DisplayGiftInfo(giftId);
                });
        }
        private void DisplayGiftInfo(int giftId)
        {
            var rewardCount = 0;
            if (giftId > 0)
            {
                var rewardList = GameEntry.LogicData.MallData.GetRewardList(giftId);
                rewardCount = rewardList.Count;
                
                // 设置礼包信息
                if (m_txtGiftTitle != null)
                {
                    var giftConfig = GameEntry.LDLTable.GetTableById<gift_pack>(giftId);
                    if (giftConfig != null)
                    {
                        m_txtGiftTitle.text = ToolScriptExtend.GetLang(giftConfig.gift_pack_name);
                        
                        // 设置礼包图标
                        if (m_imgGiftIcon != null)
                        {
                            m_imgGiftIcon.SetImage(giftConfig.gift_pack_icon);
                        }

                        // 设置礼包价格
                        if (m_txtGiftPrice != null && m_btnBuyGift != null)
                        {
                            var paymentId = (int)giftConfig.payment_id;
                            var paymentConfig = GameEntry.LDLTable.GetTableById<payment>(paymentId);
                            if (paymentConfig != null)
                            {
                                m_txtGiftPrice.text = GameEntry.LogicData.MallData.GetPrice(giftConfig.payment_id);
                            }
                            GameEntry.LogicData.MallData.CreateRechargeScore(giftConfig.payment_id, m_btnBuyGift.gameObject.transform);

                            BindBtnLogic(m_btnBuyGift, () =>
                            {
                                GameEntry.PaymentData.Pay(giftConfig.payment_id);
                            });
                            // 设置购买按钮点击事件
                            m_btnBuyGift.onClick.RemoveAllListeners();
                            m_btnBuyGift.onClick.AddListener(() =>
                            {
                                GameEntry.PaymentData.Pay(paymentId);
                            });
                            m_txtGift.text = $"{giftConfig.cost_effectiveness}%";
                        }
                    }
                }
                
                // 设置礼包奖励列表
                if (m_goGiftContent != null)
                {
                    // 先隐藏所有奖励物品
                    for (int i = 0; i < m_goGiftContent.transform.childCount; i++)
                    {
                        m_goGiftContent.transform.GetChild(i).gameObject.SetActive(false);
                    }
                    
                    // 显示需要的奖励物品
                    for (int i = 0; i < rewardCount && i < m_goGiftContent.transform.childCount; i++)
                    {
                        var itemObj = m_goGiftContent.transform.GetChild(i).gameObject;
                        itemObj.SetActive(true);
                        
                        // 获取或添加UIItemModule组件
                        UIItemModule itemModule = itemObj.GetComponent<UIItemModule>();
                        if (itemModule == null)
                        {
                            itemModule = itemObj.AddComponent<UIItemModule>();
                        }
                        
                        // 设置奖励物品数据
                        itemModule.SetData(rewardList[i].item_id, rewardList[i].num);
                        itemModule.DisplayInfo();
                        
                        // 添加点击事件，显示物品提示
                        itemModule.SetClick(() => {
                            itemModule.OpenTips();
                        });
                        
                        // 禁用按钮动画效果，提供更好的用户体验
                        UIButton itemButton = itemModule.GetComponent<UIButton>();
                        if (itemButton != null)
                        {
                            itemButton.useTween = false;
                        }
                    }
                }
            }
            
            // 显示或隐藏整个礼包面板
            if (m_goGift != null)
            {
                m_goGift.gameObject.SetActive(giftId > 0 && rewardCount > 0);
            }
        }
    //绑定按钮点击回调
        private void BindBtnLogic(Button btn, UnityAction action)
        {
            btn.onClick.RemoveAllListeners();
            if (action != null)
            {
                btn.onClick.AddListener(action);
            }
        }
        public string GetGroupByItemModule(ItemModule itemModule){
            if(isClickSearch && itemModule.GetItemConfig().item_lack_handle_value.Count > 1){
                return itemModule.GetItemConfig().item_lack_handle_value[1];
            }else{
                return itemModule.GetItemConfig().item_lack_handle_value[0];
            }
        }
        public void ClickTab(){
            foreach (var item in tabToggList)
            {
                if(clickTabIndex==item.toggleType)
                {
                    item.isOn = true;
                }
            }
            m_goSign.SetActive(tableDic[clickTabIndex].Count <= 0);
            clickGroup = int.Parse(GetGroupByItemModule(togDic[clickTabIndex]));
            if(configDic.ContainsKey(clickTabIndex)){
                iteminfotype topType = iteminfotype.item_info_type_general;
                // if(configDic[clickTabIndex].Count >0){
                //     topType = configDic[clickTabIndex][0].item_info_type;
                // }else{
                //     topType = configDic[clickTabIndex][0].item_info_type;
                // }
                if (configDic[clickTabIndex].Count > 0)
                {
                    topType = configDic[clickTabIndex][0].item_info_type;
                }
                
                switch (topType)
                {
                    case iteminfotype.item_info_type_none://4
                        m_goType1.SetActive(false);
                        m_goType2.SetActive(false);
                        m_goType3.SetActive(false);
                        break;
                    case iteminfotype.item_info_type_general://1
                        m_goType1.SetActive(true);
                        m_goType2.SetActive(false);
                        m_goType3.SetActive(false);
                        UIImage quality = m_goType1.transform.Find("quality").GetComponent<UIImage>();
                        quality?.SetImage(togDic[clickTabIndex].GetItemPath());
                        UIText name = m_goType1.transform.Find("Name").GetComponent<UIText>();
                        name.text = ToolScriptExtend.GetLang(togDic[clickTabIndex].GetItemConfig().name);
                        UIText have = m_goType1.transform.Find("have").GetComponent<UIText>();
                        have.text = ToolScriptExtend.GetLang(1100061) + GameEntry.LogicData.BagData.GetAmountById(togDic[clickTabIndex].ItemId).ToString();
                        m_goType1.transform.Find("icon").GetComponent<UIImage>().SetImage(ToolScriptExtend.GetItemIcon(togDic[clickTabIndex].ItemId),false);
                    break;
                    case iteminfotype.item_info_type_progress_bar_text://2
                        m_goType1.SetActive(false);
                        m_goType2.SetActive(true);
                        m_goType3.SetActive(false);

                        UIText des2 = m_goType2.transform.Find("des").GetComponent<UIText>();
                        des2.text = ToolScriptExtend.GetLang(18) + togDic[clickTabIndex].GetWayCount.ToString();
                        Slider slider2 = m_goType2.transform.Find("slider").GetComponent<Slider>();
                        slider2.value = (float)GameEntry.LogicData.BagData.GetAmountById(togDic[clickTabIndex].ItemId)/togDic[clickTabIndex].GetWayCount;
                        UIText conut2 = m_goType2.transform.Find("slider/count").GetComponent<UIText>();
                        conut2.text = GameEntry.LogicData.BagData.GetAmountById(togDic[clickTabIndex].ItemId).ToString()+"/"+togDic[clickTabIndex].GetWayCount.ToString();                                       
                        m_goType2.transform.Find("icon").GetComponent<UIImage>().SetImage(ToolScriptExtend.GetItemIcon(togDic[clickTabIndex].ItemId),false);
                    break;
                    case iteminfotype.item_info_type_progress_bar://3
                        m_goType1.SetActive(false);
                        m_goType2.SetActive(false);
                        m_goType3.SetActive(true);

                        UIImage quality3 = m_goType3.transform.Find("quality").GetComponent<UIImage>();
                        quality3?.SetImage(togDic[clickTabIndex].GetItemPath());
                        UIText name3 = m_goType3.transform.Find("Name").GetComponent<UIText>();
                        name3.text = ToolScriptExtend.GetLang(togDic[clickTabIndex].GetItemConfig().name);
                        Slider slider3 = m_goType3.transform.Find("slider").GetComponent<Slider>();
                        slider3.value = (float)GameEntry.LogicData.BagData.GetAmountById(togDic[clickTabIndex].ItemId)/togDic[clickTabIndex].GetWayCount;
                        UIText conut3 = m_goType3.transform.Find("slider/count").GetComponent<UIText>();
                        conut3.text = GameEntry.LogicData.BagData.GetAmountById(togDic[clickTabIndex].ItemId).ToString()+"/"+togDic[clickTabIndex].GetWayCount.ToString();  
                        m_goType3.transform.Find("icon").GetComponent<UIImage>().SetImage(ToolScriptExtend.GetItemIcon(togDic[clickTabIndex].ItemId),false);
                    break;
                }
            }
        }
        public void CheckClose(){
            if (togDic[clickTabIndex].GetWayCount <= GameEntry.LogicData.BagData.GetAmountById(togDic[clickTabIndex].ItemId))
            {
                Close();
            }
        }
        public int GetTabCount(){
            int count = 0;
            // if(configDic.ContainsKey(clickTabIndex)){
            //     count = configDic[clickTabIndex].Count;
            // }
            if(tableDic.ContainsKey(clickTabIndex) && tableDic[clickTabIndex].Count > 0){
                count = tableDic[clickTabIndex].Count;
            }
            return count;
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
            isClickSearch = false;
            itemModules.Clear();
            configDic.Clear();
            tableDic.Clear();
            togDic.Clear();
            giftDic.Clear();
            GameEntry.Event.Unsubscribe(BagChangeEventArgs.EventId, OnBagChangeUpdate);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
        }

        private void OnBtnCloseClick()
        {
            Close();
        }
        private void OnBtnBuyGiftClick(){

        }
        private void OnBtnAddAllClick(){

        }
        // public int GetUseNum(){
        //     long need = togDic[clickTabIndex].GetWayCount();
        //     GameEntry.LogicData.BagData.GetAmountById(togDic[clickTabIndex].ItemId)
        //    // return 1;
        // }
    }
}
