using UnityEngine;
using UnityEngine.UI;

namespace Game.Hotfix
{
    public partial class UIPioneerForm : UGuiFormEx
    {
        [SerializeField] private UIButton m_btnCheck;
        [SerializeField] private UIButton m_btnTargetHelp;
        [SerializeField] private UIButton m_btnTargetExtraReward;
        [SerializeField] private UIButton m_btnHelp;
        [SerializeField] private UIButton m_btnAddScore;
        [SerializeField] private UIButton m_btnGetAll;
        [SerializeField] private UIButton m_btnBuy;
        [SerializeField] private UIButton m_btnCheck2;
        [SerializeField] private UIButton m_btnExit;

        [SerializeField] private UIText m_txtTargetTip;
        [SerializeField] private UIText m_txtTimer1;
        [SerializeField] private UIText m_txtTargetProgressTip;
        [SerializeField] private UIText m_txtTargetCount;
        [SerializeField] private UIText m_txtTargetLockTip;
        [SerializeField] private UIText m_txtTimer2;
        [SerializeField] private UIText m_txtDiscount;
        [SerializeField] private UIText m_txtDesc;
        [SerializeField] private UIText m_txtScore;
        [SerializeField] private UIText m_txtInfiniteBoxDesc;
        [SerializeField] private UIText m_txtInfinityValue;
        [SerializeField] private UIText m_txtTimer3;

        [SerializeField] private UIImage m_imgScoreIcon1;
        [SerializeField] private UIImage m_imgScoreIcon2;
        [SerializeField] private UIImage m_imgScore;

        [SerializeField] private ScrollRect m_scrollviewPreview;
        [SerializeField] private ScrollRect m_scrollviewGuide;

        [SerializeField] private Slider m_sliderTarget;
        [SerializeField] private Slider m_sliderProgress;
        [SerializeField] private Slider m_sliderInfinity;

        [SerializeField] private GameObject m_goTargetNode;
        [SerializeField] private Spine.Unity.SkeletonGraphic m_spuiRole;
        [SerializeField] private GameObject m_goScoreNode;
        [SerializeField] private GameObject m_goScoreRewardNode;
        [SerializeField] private GameObject m_goTargetTask;
        [SerializeField] private GameObject m_goTargetTagGroup;
        [SerializeField] private GameObject m_goTargetLock;
        [SerializeField] private GameObject m_goDayNode;
        [SerializeField] private GameObject m_goZLNode;
        [SerializeField] private GameObject m_goScoreBg;
        [SerializeField] private GameObject m_goRewardNode;
        [SerializeField] private Mosframe.TableView m_TableViewVPass;
        [SerializeField] private GameObject m_goInfiniteBox;
        [SerializeField] private GameObject m_goCommanderNode;
        [SerializeField] private Spine.Unity.SkeletonGraphic m_spuiRole2;
        [SerializeField] private GameObject m_goTagGroup;
        [SerializeField] private GameObject m_goTagRoot;
        [SerializeField] private GameObject m_goPrefab;
        [SerializeField] private GameObject m_goReward;
        [SerializeField] private GameObject m_goTaskReward;
        [SerializeField] private GameObject m_goTaskItem;
        [SerializeField] private GameObject m_goSelectItem;
        [SerializeField] private GameObject m_goScoreRewardItem;
        [SerializeField] private GameObject m_goTagItem;
        [SerializeField] private GameObject m_goTargetTagItem;
        [SerializeField] private GameObject m_goGuideItem;

        void InitBind()
        {
            m_btnCheck.onClick.AddListener(OnBtnCheckClick);
            m_btnTargetHelp.onClick.AddListener(OnBtnTargetHelpClick);
            m_btnTargetExtraReward.onClick.AddListener(OnBtnTargetExtraRewardClick);
            m_btnHelp.onClick.AddListener(OnBtnHelpClick);
            m_btnAddScore.onClick.AddListener(OnBtnAddScoreClick);
            m_btnGetAll.onClick.AddListener(OnBtnGetAllClick);
            m_btnBuy.onClick.AddListener(OnBtnBuyClick);
            m_btnCheck2.onClick.AddListener(OnBtnCheck2Click);
            m_btnExit.onClick.AddListener(OnBtnExitClick);
        }
    }
}
