using System;
using System.Collections.Generic;
using System.Linq;
using Activity;
using DG.Tweening;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.Events;

//超值活动管理脚本
namespace Game.Hotfix
{
    public class ChaoZhiData
    {
        //开启活动列表缓存信息
        private List<ActivityTime> activityMsgList;
        
        //==============================后端返回  [配置]  数据缓存==============================
        private Dictionary<int, PushActivityHeroStarConfig> roleUpStarConfigDic;//角色升星模板活动缓存：（int）模板id作为键
        
        private Dictionary<int, PushActivityRechargeConfig> rechargeConfigDic;//累充模板活动缓存：（int）模板id作为键
        
        private Dictionary<int, PushActivityBattlePassConfig> ZlConfigDic;//战令模板活动缓存：（int）模板id作为键
        
        //==============================后端返回  [变更]  数据缓存==============================
        private Dictionary<int, PushActivityHeroStarData> roleUpStarMsgDic;//角色升星模板活动缓存：（int）模板id作为键
        
        private Dictionary<int, PushActivityRechargeData> rechargeMsgDic;//累充模板活动缓存：（int）模板id作为键
        
        private Dictionary<int, PushActivityBattlePassData> ZlMsgDic;//战令模板活动缓存：（int）模板id作为键
        
        //==============================战令任务数据缓存==============================
        private Dictionary<int, ActivityBattlePassTaskListResp> ZlTaskDic;//战令任务数据：（int）模板id作为键
        
        
        private List<int> ActivityEntryUnlockList;//超值活动入口解锁id
        private List<int> pioneerUnlockList;//先锋活动入口解锁id
        
        private Dictionary<int, int> RedDotDic;//红点字典
        private Dictionary<int, int> PioneerRedDotDic;//先锋红点字典  1：先锋目标进度红点 2：先锋目标任务奖励红点 3：先锋战令红点
        
        public void Init()
        {
            if(ToolScriptExtend.GetTable<activity_set>(out var setData))
            {
                ActivityEntryUnlockList = new List<int>();
                foreach (var id in setData[0].activity_entry_demand)
                {
                    ActivityEntryUnlockList.Add((int)id);
                }
                
                pioneerUnlockList = new List<int> { (int)setData[0].pioneer_entry_demand };
            }

            RedDotDic = new Dictionary<int, int>();
            PioneerRedDotDic = new Dictionary<int, int>();
            activityMsgList = new List<ActivityTime>();
            roleUpStarConfigDic = new Dictionary<int, PushActivityHeroStarConfig>();
            rechargeConfigDic = new Dictionary<int, PushActivityRechargeConfig>();
            roleUpStarMsgDic = new Dictionary<int, PushActivityHeroStarData>();
            rechargeMsgDic = new Dictionary<int, PushActivityRechargeData>();

            ZlConfigDic = new Dictionary<int, PushActivityBattlePassConfig>();
            ZlMsgDic = new Dictionary<int, PushActivityBattlePassData>();
            ZlTaskDic = new Dictionary<int, ActivityBattlePassTaskListResp>();
            
            //活动信息变更(新增或修改)推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityChange, OnActivityChange);
            //活动删除
            RegisterProtoEvent(Protocol.MessageID.PushActivityDel, OnActivityDel);
            //-------------------------------------------英雄升星-----------------------------------
            //英雄升星活动配置数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityHeroStarConfig, OnPushActivityHeroStarConfig);
            //英雄升星活动变更数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityHeroStarData, OnPushActivityHeroStarData);
            
            //-------------------------------------------累充活动-----------------------------------
            //累充活动配置数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityRechargeConfig, OnPushActivityRechargeConfig);
            //累充活动变更数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityRechargeData, OnPushActivityRechargeData);
           
            //-------------------------------------------勇士战令-----------------------------------
            //勇士战令活动配置数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityBattlePassConfig, OnPushActivityBattlePassConfig);
            //勇士战令活动变更数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityBattlePassData, OnPushActivityBattlePassData);
            //战令任务活动变更数据推送
            RegisterProtoEvent(Protocol.MessageID.PushActivityBattlePassTaskChange, OnPushActivityBattlePassTaskChange);
        }

        //注册协议推送逻辑
        private void RegisterProtoEvent(Protocol.MessageID id, UnityAction<object> callback = null)
        {
            var ProtoId = (int)id;
            NetEventDispatch.Instance.RegisterEvent(ProtoId, message =>
            {
                callback?.Invoke(message);
            });
        }
        
        //协议返回数据打印
        private void ProtoLog(bool isRequest,string protoName,object data)
        {
            bool isShow = true;
            if (isShow)
            {
                ColorLog.ProtoLog(isRequest,protoName,data);
            }
        }
        
        #region 超值活动 红点逻辑
        //超值活动入口红点
        public int CheckChaoZhiRedDot()
        {
            var sum = 0;
            if (RedDotDic != null)
            {
                foreach (var data in RedDotDic)
                {
                    sum += data.Value;
                }
            }
            return sum;
        }
        
        public int GetRedDotCountById(int templateId)
        {
            if (RedDotDic.TryGetValue(templateId, out var result))
            {
                return result;
            }
            return 0;
        }
        
        //角色升星红点逻辑
        public int CheckRoleUpStarRedDot(int templateId)
        {
            var ConfigData = GetHeroUpStarConfig(templateId);
            var MsgData = GetHeroStarCache(templateId);
            if (ConfigData == null || MsgData == null)
            {
                return 0;
            }
            
            var isAllReceived = IsUpStarAllReceived(ConfigData,MsgData);
            //奖励列表
            bool isCanReceive = IsTaskHeroStarFinish(MsgData);
            // 判断角色升星奖励是否可领取
            var isOk = !isAllReceived && isCanReceive;
            
            var count = isOk ? 1: 0;
            UpdateRedDotLogic(templateId, count);
            return count;
        }
        
        //累充活动红点检测公共逻辑
        public int CheckRechargeRedDotLogic(int templateId)
        {
            var ConfigData = GetRechargeConfig(templateId);
            var MsgData = GetRechargeMsg(templateId);
            if (ConfigData == null || MsgData == null)
            {
                return 0;
            }
            var count = 0;
            var dataList = ConfigData.ScoreRewards;
            foreach (var data in dataList)
            {
                if (MsgData.Score < data.Score)
                {
                    break;
                }
                if (!MsgData.DrawIds.Contains(data.Id))
                {
                    count++;
                }
            }
            UpdateRedDotLogic(templateId, count);
            return count;
        }
        
        //勇士战令红点逻辑
        public int CheckWarriorZLRedDot(int templateId)
        {
            var count = 0;
            //奖励页签领奖
            var rewardCount = CheckWarriorZLRewardDot(templateId);
            count += rewardCount;
            
            //每日任务+目标任务 领奖
            var taskCount = CheckWarriorZLAllTaskDot(templateId);
            count += taskCount;
            UpdateRedDotLogic(templateId, count);
            return count;
        }

        //勇士战令——奖励页签领奖
        public int CheckWarriorZLRewardDot(int templateId)
        {
            var count = CheckZLRewardDot(templateId);
            return count;
        }

        public int CheckZLRewardDot(int templateId)
        {
            var ConfigData = GetZLConfig(templateId);
            var MsgData = GetZLMsg(templateId);
            if (ConfigData == null || MsgData == null)
            {
                return 0;
            }
            var count = 0;
            var dataList = ConfigData.ScoreRewards.ToList();
            var isPay1 = IsWarriorZLVipPay(templateId,1);
            var isPay2 = IsWarriorZLVipPay(templateId,2);
            var curScore = MsgData.Score;
            foreach (var data in dataList)
            {
                if(curScore < data.Score)break;
                //判断免费奖励是否可领
                var status = CheckZLFreeRewardStatus(data.Id, data.Score, templateId);
                if (status == 1)
                {
                    count++;
                    continue;
                }

                //判断一档付费奖励是否可领
                if (isPay1)
                {
                    if (data.Reward1.Count > 0)
                    {
                        var status1 = CheckZLVipRewardStatus(data.Id,1,data.Score, templateId);
                        if (status1 == 1)
                        {
                            count++;
                            continue;
                        }
                    }
                }

                //判断二档付费奖励是否可领（如果有的话）
                if (isPay2)
                {
                    if (data.Reward2.Count > 0)
                    {
                        var status1 = CheckZLVipRewardStatus(data.Id,2,data.Score, templateId);
                        if (status1 == 1)
                        {
                            count++;
                            continue;
                        } 
                    }
                }
            }
            
            return count;
        }
        
        //勇士战令—-每日任务+目标任务红点判断
        public int CheckWarriorZLAllTaskDot(int templateId)
        {
            if (!ZlTaskDic.TryGetValue(templateId, out var taskList))
            {
                return 0;
            }
            var count = CheckWarriorZLTaskRedDotLogic(templateId,taskList.TaskList.ToList());
            return count;
        }
        
        //勇士战令—-每日任务领奖
        public int CheckWarriorZLDailyDot(int templateId)
        {
            var taskList = GetZLTaskList(templateId, refreshtype.refresh_type_daily);
            if (taskList.Count == 0)
            {
                return 0;
            }
            var count = CheckWarriorZLTaskRedDotLogic(templateId,taskList.ToList());
            return count;
        }
        
        //勇士战令——目标任务领奖
        public int CheckWarriorZLTargetDot(int templateId)
        {
            var taskList = GetZLTaskList(templateId, refreshtype.refresh_type_no_refresh);
            if (taskList.Count == 0)
            {
                return 0;
            }
            var count = CheckWarriorZLTaskRedDotLogic(templateId,taskList.ToList());
            return count;
        }
        
        public int CheckWarriorZLTaskRedDotLogic(int templateId,List<ActivityBattlePassTask> taskList)
        {
            if ( taskList.Count == 0)
            {
                return 0;
            }

            var count = 0;
            var isPay = IsWarriorZLVipPay(templateId,1);
            foreach (var task in taskList)
            {
                var status = GetZLTaskStatus(task);
                if (status == 1)
                {
                    count++;//可领取
                }
                else if(status == 3)
                {
                    if (isPay)
                    {
                        count++;//付费后继续领取
                    }
                }
            }
            return count;
        }

        private void UpdateRedDotLogic(int templateId,int count)
        {
            RedDotDic[templateId] = count;
            CheckAndRefreshForm<ValueTuple<string, int>>(EnumUIForm.UIChaoZhiActivityForm,("RedDot",templateId));
            
            GameEntry.Event.Fire(ChaoZhiDotEventArgs.EventId, ChaoZhiDotEventArgs.Create());
        }
        
        #endregion

        #region 先锋红点
        public int GetPioneerRedDotCountById(int id)
        {
            if (PioneerRedDotDic.TryGetValue(id, out var result))
            {
                return result;
            }
            return 0;
        }
        
        //先锋入口红点
        public int CheckPioneerRedDot()
        {
            var sum = 0;
            if (PioneerRedDotDic != null)
            {
                foreach (var data in PioneerRedDotDic)
                {
                    sum += data.Value;
                }
            }
            return sum;
        }

        //先锋目标红点
        public int CheckPioneerTargetRedDot()
        {
            var sum = 0;
            if (PioneerRedDotDic != null)
            {
                foreach (var data in PioneerRedDotDic)
                {
                    if (data.Key is 1 or 2)
                    {
                        sum += data.Value;
                    }
                }
            }
            return sum;
        }
        
        //先锋目标进度奖励红点
        public int CheckPioneerTargetProgressRedDot(int templateId)
        {
            var sum = 0;
            var list = GetAllPioneerVipList();
            var MsgData = GetZLMsg(templateId);
            foreach (var node in list)
            {
                // 状态：0：积分未达标 1：可领取 2: 已领取 3：上锁
                //免费奖励
                var freeStatus = CheckRewardStatus(node, MsgData, true);
                if (freeStatus == 1)
                {
                    sum++;
                }
                
                //vip奖励
                var vipStatus = CheckRewardStatus(node, MsgData, false);
                if (vipStatus == 1)
                {
                    sum++;
                }
            }
            PioneerRedDotDic[1] = sum;
            return sum;
        }
        
        //先锋所有天数目标任务奖励红点
        public int CheckAllPioneerTargetDayRedDot(int templateId)
        {
            var sum = 0;
            for (var i = 1; i <= 5; i++)
            {
                var count = CheckPioneerTargetTaskRedDot(templateId,i,0);
                sum += count;
            }
            PioneerRedDotDic[2] = sum;
            return sum;
        }
        
        //先锋指定天数目标任务奖励红点
        public int CheckPioneerTargetDayRedDot(int templateId,int day)
        {
            var sum = CheckPioneerTargetTaskRedDot(templateId,day,0);
            return sum;
        }
        
        //先锋目标任务奖励红点
        public int CheckPioneerTargetTaskRedDot(int templateId,int day,int index)
        {
            var sum = 0;
            
            if (GetPioneerActivityTime(out var activityMsg))
            {
                var today = GetToday(activityMsg.EndTime);
                if (day > today) return 0;
            }
            else
            {
                return 0;
            }
            
            var list = GetTargetTaskListByType(day, index);
            foreach (var config in list)
            {
                var msg = GetZLTaskMsg(templateId, config.task_id);
                //奖励领取状态0：进行中 1：可领取  2：已领取 3:继续领取
                var status = GetZLTaskStatus(msg);
                if (status == 1)
                {
                    sum++;
                }
            }
            return sum;
        }
        
        //先锋战令红点
        public int CheckPioneerZLRedDot(int templateId)
        {
            var sum = CheckZLRewardDot(templateId);
             PioneerRedDotDic[3] = sum;
            return sum;
        }
        
        private void UpdatePioneerRedDotLogic(int templateId)
        {
            CheckPioneerTargetProgressRedDot(templateId);
            CheckAllPioneerTargetDayRedDot(templateId);
            CheckPioneerZLRedDot(templateId);
            CheckAndRefreshForm<int>(EnumUIForm.UIPioneerForm,2);
            GameEntry.Event.Fire(PioneerChangeEventArgs.EventId, PioneerChangeEventArgs.Create());
        }
        
        #endregion
        
        #region 配表数据
        public activity_main GetActivityConfig(int templateId)
        {
            if (ToolScriptExtend.GetTable<activity_main>(out var config))
            {
                return config.FirstOrDefault(x=>(int)x.activity_template == templateId);
            }
            return null;
        }
        
        #endregion
        
        
        #region 活动基础
        public ActivityTime GetActivityMsgByTemplateId(int templateId)
        {
            var target =
                activityMsgList.FirstOrDefault(x => (int)x.Template == templateId);
            return target;
        }
        
        public void ReloadAllUnlockActivityMsg()
        {
            C2SActivityOpenInfosReq((resp) =>
            {
                if (activityMsgList.Count > 0)
                {
                    var sequence = DOTween.Sequence();
                    foreach (var node in activityMsgList)
                    {
                        sequence.AppendCallback(() => { C2SLoadActivityInfo(node); });
                        sequence.AppendInterval(0.05f);
                    }
                }
            });
        }
        
        /// <summary>
        /// 活动开启列表请求,获取已开启的超值活动信息列表
        /// </summary>
        /// <param name="callback"></param>
        public void C2SActivityOpenInfosReq(UnityAction<ActivityOpenInfosResp> callback = null)
        {
            var req = new ActivityOpenInfosReq();
            ProtoLog(true, "活动开启列表", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.ActivityOpenInfos, req, (message) =>
            {
                var resp = (ActivityOpenInfosResp)message;
                activityMsgList.Clear();
                if (resp.List != null)
                {
                    foreach (var msg in resp.List)
                    {
                        var config = GetActivityConfig((int)msg.Template);
                        if (ToolScriptExtend.CheckDemandUnlockList(config.player_demands))
                        {
                            activityMsgList.Add(msg);
                        }
                    }
                }
                
                ProtoLog(false, "活动开启列表", resp);
                callback?.Invoke(resp);
            });
        }
        
        //活动信息变更(新增或修改)推送
        private void OnActivityChange(object message)
        {
            var resp = (PushActivityChange)message;
            ProtoLog(false, "活动信息变更推送(新增或修改)协议", resp);
        }
        
        //活动删除推送
        private void OnActivityDel(object message)
        {
            var resp = (PushActivityDel)message;
            ProtoLog(false, " 活动删除推送协议", resp);
            // CheckAndRefreshForm<ValueTuple<string, int>>(EnumUIForm.UIChaoZhiActivityForm,("DailyDeal",1));
        }
        
        /// <summary>
        /// 具体活动数据请求(以推送形式返回具体活动数据)
        /// </summary>
        /// <param name="callback"></param>
        public void C2SActivityDataReq(ActivityDataReq req,UnityAction<ActivityDataResp> callback = null)
        {
            if (req == null) return;
            ProtoLog(true, "具体活动数据请求", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.ActivityData, req, (message) =>
            {
                var resp = (ActivityDataResp)message;
                ProtoLog(false, "具体活动数据请求", resp);
                callback?.Invoke(resp);

                //特殊处理
                //战令活动额外请求任务列表
                var templateId = (int)req.Template;
                var data = GetActivityConfig(templateId);
                if (data != null && data.activity_type == activity_type.activity_type_battlepass)
                {
                    if (ToolScriptExtend.GetTable<activity_battlepass_main>(out var list))
                    {
                        var target = list.FirstOrDefault(x => (int)x.activity_templateid == templateId);
                        if (target != null)
                        {
                            if (target.battlepass_page_type == battlepass_page_type.battlepass_page_type_1 ||target.battlepass_page_type == battlepass_page_type.battlepass_page_type_2 )
                            {
                                var temp = new ActivityBattlePassTaskListReq
                                {
                                    Template = req.Template,
                                    LoopTimes = req.LoopTimes
                                };
                                C2SActivityBattlePassTaskListReq(temp);
                            }
                        }
                    }
                }
            });
        }
        
        /// <summary>
        /// 活动通用领奖协议
        /// </summary>
        /// <param name="callback"></param>
        public void C2SActivityDrawReq(ActivityDrawReq req,UnityAction<ActivityDrawResp> callback = null)
        {
            if (req == null) return;
            ProtoLog(true, "活动通用领奖", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.ActivityDraw, req, (message) =>
            {
                var resp = (ActivityDrawResp)message;
                ProtoLog(false, "活动通用领奖", resp);
                ToolScriptExtend.DisplayRewardGet(resp.Rewards.ToList());
                callback?.Invoke(resp);
            });
        }
        
        /// <summary>
        /// 活动通用购买协议
        /// </summary>
        /// <param name="callback"></param>
        public void C2SActivityBuyReq(ActivityBuyReq req,UnityAction<ActivityBuyResp> callback = null)
        {
            if (req == null) return;
            ProtoLog(true, "活动通用购买", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.ActivityBuy, req, (message) =>
            {
                var resp = (ActivityBuyResp)message;
                ProtoLog(false, "活动通用购买", resp);
                callback?.Invoke(resp);
            });
        }
        
        /// <summary>
        /// 活动配置数据请求(以推送形式返回具体活动数据)
        /// </summary>
        /// <param name="callback"></param>
        public void C2SActivityConfigReq(ActivityConfigReq req,UnityAction<ActivityConfigResp> callback = null)
        {
            if (req == null) return;
            ProtoLog(true, "活动配置数据请求", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.ActivityConfig, req, (message) =>
            {
                var resp = (ActivityConfigResp)message;
                ProtoLog(false, "活动配置数据请求", resp.ToString());
                callback?.Invoke(resp);
            });
        }

        //请求+缓存（配置+变更数据）流程
        public void C2SLoadActivityInfo(ActivityTime activityMsg)
        {
            //先请求配置数据，成功后，再请求可变更数据，再初始化界面信息
            var configData = new ActivityConfigReq
            {
                Template = activityMsg.Template,
                Type = activityMsg.Type
            };
            C2SActivityConfigReq(configData, (resp) =>
            {
                var reqData = new ActivityDataReq
                {
                    Template = activityMsg.Template,
                    LoopTimes = activityMsg.LoopTimes,
                    Type = activityMsg.Type
                };
                C2SActivityDataReq(reqData);
            });
        }
        
        #endregion
        
        #region 角色升星

        //英雄升星配置数据推送
        private void OnPushActivityHeroStarConfig(object message)
        {
            var resp = (PushActivityHeroStarConfig)message;
            ProtoLog(false, " 英雄升星活动【配置】数据推送", resp);
            roleUpStarConfigDic[(int)resp.Template] = resp;
        }
        
        //英雄升星活动变更数据推送
        private void OnPushActivityHeroStarData(object message)
        {
            var resp = (PushActivityHeroStarData)message;
            ProtoLog(false, " 英雄升星活动【变更】数据推送", resp);
            var templateId = (int)resp.Template;
            roleUpStarMsgDic[templateId] = resp;
            CheckRoleUpStarRedDot(templateId);
            CheckAndRefreshForm<ValueTuple<string, int>>(EnumUIForm.UIChaoZhiActivityForm,("ChaoZhi_RoleUpStar",2));

        }
        
        //活动任务中所有英雄的升星要求是否达标
        public bool IsTaskHeroStarFinish(long taskId)
        {
            var tables = GameEntry.LDLTable.GetTable<activity_herostar_condition>();
            var config = tables.FirstOrDefault(x => (long)x.id == taskId);
            if (config == null) return false;
            var condition = config.task_condition;
            if (ToolScriptExtend.GetTable<activity_herostar>(out var list))
            {
                var target = list.FirstOrDefault(x => x.activity_templateid == config.activity_templateid);
                if (target != null)
                {
                    if (condition.Count != target.hero.Count)
                    {
                        return false;
                    }

                    for (var i = 0; i < condition.Count; i++)
                    {
                        var heroId = target.hero[i];
                        var starCount = condition[i]/5;
                        var heroVo = GameEntry.LogicData.HeroData.GetHeroModule(heroId);
                        if (heroVo != null)
                        {
                            if (heroVo.StarNum < starCount)
                            {
                                return false;
                            }
                        }
                        else
                        {
                            return false;
                        }
                    }
                    return true;
                }
            }
            return false;
        }
        
        //活动任务中所有英雄的升星要求是否达标
        public bool IsTaskHeroStarFinish(PushActivityHeroStarData msgData)
        {
            if (msgData == null) return false;
            var configData = GetHeroUpStarConfig((int)msgData.Template);
            if (configData == null) return false;
            var data = configData.Conditions.FirstOrDefault(x => !msgData.DrawIds.Contains(x.Id));
            if (data != null)
            {
                for (var i = 0; i < configData.HeroIds.Count; i++)
                {
                    var heroId = (int)configData.HeroIds[i];
                    var starCount = (int)data.StarConditions[i]/5;
                    
                    //判断是否完成领奖要求
                    var heroVo = GameEntry.LogicData.HeroData.GetHeroModule((itemid)heroId);
                    if (heroVo != null)
                    {
                        if (heroVo.StarNum < starCount)
                        {
                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                }
                return true;
            }
            return false;
        }
        
        //获取角色升星模板活动的缓存数据
        public PushActivityHeroStarData GetHeroStarCache(int templateId)
        {
            if (roleUpStarMsgDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        //获取角色升星模板活动的配置缓存数据
        public PushActivityHeroStarConfig GetHeroUpStarConfig(int templateId)
        {
            if (roleUpStarConfigDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        //获取角色升星模板活动的变更缓存数据
        public PushActivityHeroStarData GetHeroUpStarMsg(int templateId)
        {
            if (roleUpStarMsgDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        //角色升星所有奖励是否都已经领取
        public bool IsUpStarAllReceived(PushActivityHeroStarConfig ConfigData,PushActivityHeroStarData MsgData)
        {
            var drawCont = MsgData.DrawIds.Count;
            if (drawCont == 0)
            {
                return false;
            }
            var configCont = ConfigData.Conditions.Count;
            if(drawCont == configCont)
            {
                foreach (var data in ConfigData.Conditions)
                {
                    if (!MsgData.DrawIds.Contains(data.Id))
                    {
                        return false;
                    }
                }
                return true;
            }
            return false;
        }
        
        #endregion
        
        #region 累充活动
        
        //累充活动配置数据推送
        private void OnPushActivityRechargeConfig(object message)
        {
            var resp = (PushActivityRechargeConfig)message;
            ProtoLog(false, " 英雄升星活动配置数据推送", resp);
            rechargeConfigDic[(int)resp.Template] = resp;
        }
        
        //累充活动变更数据推送
        private void OnPushActivityRechargeData(object message)
        {
            var resp = (PushActivityRechargeData)message;
            ProtoLog(false, " 累充活动变更数据推送", resp);
            var templateId = (int)resp.Template;
            rechargeMsgDic[templateId] = resp;
            CheckRechargeRedDotLogic(templateId);
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIChaoZhiActivityForm);
            if (form != null)
            {
                if (ToolScriptExtend.GetTable<activity_recharge>(out var list))
                {
                    var target = list.FirstOrDefault(x =>
                        (int)x.activity_templateid == (int)resp.Template);
                    if (target != null)
                    {
                        var str = "ChaoZhi_DailyRecharge";
                        if (target.activity_recharge_day == 0)
                        {
                            str = "ChaoZhi_RoleRecharge";
                        }
                        else if (target.activity_recharge_day == 1)
                        {
                            str = "ChaoZhi_DailyRecharge";
                        }
                        else if (target.activity_recharge_day == 7)
                        {
                            str = "ChaoZhi_WeeklyRecharge";
                        }
                        CheckAndRefreshForm<ValueTuple<string, int>>(EnumUIForm.UIChaoZhiActivityForm,(str,1));
                    }
                }
            }
            UpdateRechargeIconList();
        }
        
        public string GetRechargeIcon(int template)
        {            
            if (ToolScriptExtend.GetTable<activity_recharge>(out var table))
            {
                var data = table.FirstOrDefault(x => (int)x.activity_templateid == template);
                if (data != null)
                {
                    return data.icon;
                }
            }
            return null;
        }
        
        //获取累充模板活动的配置缓存数据
        public PushActivityRechargeConfig GetRechargeConfig(int templateId)
        {
            if (rechargeConfigDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        //获取累充模板活动的变更缓存数据
        public PushActivityRechargeData GetRechargeMsg(int templateId)
        {
            if (rechargeMsgDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        #endregion
        
        #region 战令
        //战令活动配置数据推送
        private void OnPushActivityBattlePassConfig(object message)
        {
            var resp = (PushActivityBattlePassConfig)message;
            ProtoLog(false, " 战令活动配置数据推送", resp);
            ZlConfigDic[(int)resp.Template] = resp;
        }
        
        //战令活动变更数据推送
        private void OnPushActivityBattlePassData(object message)
        {
            var resp = (PushActivityBattlePassData)message;
            ProtoLog(false, " 战令活动变更数据推送", resp);
            var templateId = (int)resp.Template;
            ZlMsgDic[templateId] = resp;
            
            if (ToolScriptExtend.GetTable<activity_battlepass_main>(out var list))
            {
                var target = list.FirstOrDefault(x =>
                    (int)x.activity_templateid == (int)resp.Template);
                if (target != null)
                {
                    if (target.battlepass_page_type == battlepass_page_type.battlepass_page_type_1)
                    {
                        CheckAndRefreshForm<ValueTuple<string, int>>(EnumUIForm.UIChaoZhiActivityForm,("ChaoZhi_WarriorZL",1));
                    }
                    else if (target.battlepass_page_type == battlepass_page_type.battlepass_page_type_2)
                    {
                        CheckAndRefreshForm<int>(EnumUIForm.UIPioneerForm,1);
                        CheckAndRefreshForm<int>(EnumUIForm.UIPioneerRewardForm,1);
                        UpdatePioneerRedDotLogic(templateId);
                    }
                }
            }
            
            CheckWarriorZLRedDot(templateId);
        }
        
        //战令活动模板活动的配置缓存数据
        public PushActivityBattlePassConfig GetZLConfig(int templateId)
        {
            if (ZlConfigDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        //获取战令模板活动的变更缓存数据
        public PushActivityBattlePassData GetZLMsg(int templateId)
        {
            if (ZlMsgDic.TryGetValue(templateId, out var data))
            {
                return data;
            }
            return null;
        }
        
        /// <summary>
        /// 战令全量任务
        /// </summary>
        /// <param name="callback"></param>
        public void C2SActivityBattlePassTaskListReq(ActivityBattlePassTaskListReq req,UnityAction<ActivityBattlePassTaskListResp> callback = null)
        {
            ProtoLog(true, "战令全量任务", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.ActivityBattlePassTaskList, req, (message) =>
            {
                var resp = (ActivityBattlePassTaskListResp)message;
                var templateId = (int)resp.Template;
                ZlTaskDic[templateId] = resp;
                ProtoLog(false, "战令全量任务", resp);
                callback?.Invoke(resp);
            });
        }

        /// <summary>
        /// 战令任务领奖请求
        /// </summary>
        /// <param name="callback"></param>
        public void C2SActivityBattlePassDrawTaskReq(ActivityBattlePassDrawTaskReq req,UnityAction<ActivityBattlePassDrawTaskResp> callback = null)
        {
            ProtoLog(true, "战令任务领奖请求", req);
            GameEntry.LDLNet.Send(Protocol.MessageID.ActivityBattlePassDrawTask, req, (message) =>
            {
                var resp = (ActivityBattlePassDrawTaskResp)message;
                ProtoLog(false, "战令任务领奖请求", resp);
                callback?.Invoke(resp);
                ToolScriptExtend.DisplayRewardGet(resp.Rewards.ToList());                
            });
        }
        
        //战令活动任务变更数据推送
        private void OnPushActivityBattlePassTaskChange(object message)
        {
            var resp = (PushActivityBattlePassTaskChange)message;
            ProtoLog(false, " 战令活动变更数据推送", resp);
            var templateId = (int)resp.Template;
            if (!ZlTaskDic.ContainsKey(templateId))
            {
                return;
            }
            
            var taskData = ZlTaskDic[templateId];
            var changeDic = new Dictionary<int,ActivityBattlePassTask>();
            foreach (var task in resp.TaskList)
            {
                changeDic.Add(task.Id,task);
            }
            
            var result = new List<ActivityBattlePassTask>();
            var taskList = taskData.TaskList.ToList();
            foreach (var task in taskList)
            {
                var key = task.Id;
                if (changeDic.ContainsKey(key))
                {
                    result.Add(changeDic[key]);
                    changeDic.Remove(key);
                }
                else
                {
                    result.Add(task);
                }
            }

            if (changeDic.Count > 0)
            {
                foreach (var task in changeDic.Values)
                {
                    result.Add(task);
                }
            }
            
            var changeResp = new ActivityBattlePassTaskListResp();
            changeResp.Template = resp.Template;
            changeResp.LoopTimes = resp.LoopTimes;
            changeResp.TaskList.Clear();
            changeResp.TaskList.AddRange(result);
            ZlTaskDic[templateId] = changeResp;
            
            if (ToolScriptExtend.GetTable<activity_battlepass_main>(out var list))
            {
                var target = list.FirstOrDefault(x =>
                    (int)x.activity_templateid == (int)resp.Template);
                if (target != null)
                {
                    if (target.battlepass_page_type == battlepass_page_type.battlepass_page_type_1)
                    {
                        CheckWarriorZLRedDot(templateId);
                        CheckAndRefreshForm(EnumUIForm.UIChaoZhiActivityForm,("ChaoZhi_WarriorZL",1));
                    }
                    else if (target.battlepass_page_type == battlepass_page_type.battlepass_page_type_2)
                    {
                        CheckAndRefreshForm<int>(EnumUIForm.UIPioneerForm,1);
                        CheckAndRefreshForm<int>(EnumUIForm.UIPioneerRewardForm,1);
                        UpdatePioneerRedDotLogic(templateId);
                    }
                }
            }
        }
        
        //获取战令模板活动的变更缓存数据
        public List<ActivityBattlePassTask> GetZLTaskList(int templateId,refreshtype type)
        {
            var result = new List<ActivityBattlePassTask>();
            if (ZlTaskDic.TryGetValue(templateId, out var data))
            {
                var list = data.TaskList.ToList();
                var sortDic = new Dictionary<int,int>();
                if(!ToolScriptExtend.GetTable<activity_battlepass_task>(out var table))return result;
                foreach (var node in list)
                {
                    var config = table.FirstOrDefault(x => x.task_id == node.Id);
                    if (config != null)
                    {
                        if (config.activity_task_refresh_type == type)
                        {
                            result.Add(node);
                            sortDic.Add(node.Id,config.priority);
                        }
                    }
                }
                
                if (result.Count > 0)
                {
                    result.Sort((a, b) =>
                    {
                        // 奖励领取状态0：进行中 1：可领取  2：已领取 3:继续领取
                        var statusA =  GetZLTaskStatus(a);
                        var statusB =  GetZLTaskStatus(b);
                        // 可领(1)>>继续领取(2)>>未达标(3)>>已领取(4)
                        var flagA = GetCompareFlag(statusA);
                        var flagB = GetCompareFlag(statusB);
                        if (flagA != flagB)
                        {
                            return flagA - flagB;
                        }
                        else
                        {
                            var sortA = sortDic[a.Id];
                            var sortB = sortDic[b.Id];
                            return sortA - sortB;
                        }
                    });
                }
            }
            return result;
        }

        public ActivityBattlePassTask GetZLTaskMsg(int templateId,int taskId)
        {
            if (ZlTaskDic.TryGetValue(templateId, out var data))
            {
                var list = data.TaskList.ToList();
                var result = list.FirstOrDefault(x => x.Id == taskId);
                if (result != null)
                {
                    return result;
                }
            }
            return null;
        }
        
        // 可领(1)>>继续领取(2)>>未达标(3)>>已领取(4)
        private int GetCompareFlag(int status)
        {
            var flag = 3;
            // 奖励领取状态0：进行中 1：可领取  2：已领取 3:继续领取
            if (status == 0)
            {
                flag = 3;
            }
            else if (status == 1)
            {
                flag = 1;
            }
            else if (status == 2)
            {
                flag = 4;
            }
            else if (status == 3)
            {
                flag = 2;
            }
            return flag;
        }
        
        //获取战令积分icon
        public itemid GetZLScoreIcon(int templateId)
        {
            if (ToolScriptExtend.GetTable<activity_battlepass_main>(out var table))
            {
                var config = table.FirstOrDefault(x => (int)x.activity_templateid == templateId);
                if (config != null)
                {
                    return config.battlepass_itemid;
                }
            }
            return itemid.itemid_1220002;
        }

        //获取战令任务的状态
        //奖励领取状态0：进行中 1：可领取  2：已领取 3:继续领取
        public int GetZLTaskStatus(ActivityBattlePassTask data)
        {
            if (data == null) return 0;
            
            var status =  0;
            if (data.IsFinish)
            {
                if (data.DrawTimes == 0)
                {
                    status = 1;
                }
                else if(data.DrawTimes == 1)
                {
                    status = 3;
                    
                }
                else if(data.DrawTimes == 2)
                {
                    status = 2;
                }
            }
            else
            {
                status = 0;
            }
            return status;
        }
        
        //勇士战令是否已购买
        public bool IsWarriorZLVipPay(int templateId,int vipType)
        {
            var MsgData = GetZLMsg(templateId);
            var isUnlock = MsgData.UnlockGrade.ToList().Contains(vipType);
            return isUnlock;
        }
        
        //判断免费奖励状态
        public int CheckZLFreeRewardStatus(int id, int score,int templateId)
        {
            var MsgData = GetZLMsg(templateId);
            //0:未解锁 1：可领取 2：已领取
            if (MsgData.Score >= score)
            {
                var isReceived = IsRewardReceived(id, 0,templateId);
                return isReceived ? 2 : 1;
            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 判断充值奖励状态
        /// </summary>
        /// <param name="vipType">充值档位</param>
        /// <param name="score">达标分数</param>
        /// <returns>0:未解锁 1：可领取 2：已领取 3:未购买</returns>
        public int CheckZLVipRewardStatus(int id, int vipType, int score,int templateId)
        {
            var MsgData = GetZLMsg(templateId);
            var isUnlock = IsWarriorZLVipPay(templateId,vipType);
            if (isUnlock)
            {
                if (MsgData.Score >= score)
                {
                    var isReceived = IsRewardReceived(id, 1,templateId);
                    return isReceived ? 2 : 1;
                }
                else
                {
                    return 0;
                }
            }
            else
            {
                return 3;
            }
        }
        
        /// <summary>
        /// 奖励是否已经领取过
        /// </summary>
        /// <param name="id">activity_battlepass_reward中的id</param>
        /// <param name="vipType">档位id</param>
        /// <returns></returns>
        private bool IsRewardReceived(int id, int vipType,int templateId)
        {
            var MsgData = GetZLMsg(templateId);
            var value = id;
            var grade = vipType;
            return MsgData.Draws.Any(x => x.Id == value && x.DrawGrades.Contains(grade));
        }
        
        // 积分里程碑奖励领取状态  0：未解锁 1:可领取 2：已领取 3：继续领取 
        public int CheckZLItemStatus(int id, int score,int curScore,int TemplateId)
        {
            var targetId = id;
            if (curScore >= score)
            {
                //免费奖励  0:未解锁 1：可领取 2：已领取
                var freeStatus = CheckZLFreeRewardStatus(targetId, score, TemplateId);
                if (freeStatus == 1)
                {
                    return 1;
                }

                //充值奖励  0:未解锁 1：可领取 2：已领取 3：未购买
                var vipStatus = CheckZLVipRewardStatus(targetId, 1, score, TemplateId);
                if (vipStatus == 1)
                {
                    return 1;
                }

                //Result:继续领取
                if (freeStatus == 2 && vipStatus == 3)
                {
                    return 3;
                }

                //Result:已完成
                if (freeStatus == 2 && vipStatus == 2)
                {
                    return 2;
                }
            }

            return 0;
        }
        
        //展示战令奖励信息
        public void ShowZLRewardMsg(int id, bool isFree, Transform root, PbGameconfig.reward data, int score,GameObject childObj,int TemplateId)
        {
            var rewardData = new reward() { item_id = (itemid)data.ItemId, num = data.Num };
            if (root.childCount == 0)
            {
                var obj = GameObject.Instantiate(childObj, root);
                var freeNode = obj.transform.Find("node");

                freeNode.localScale = Vector3.one * 0.6f;
                BagManager.CreatItem(freeNode, rewardData.item_id, rewardData.num, (module) =>
                {
                    ToolScriptExtend.SetItemObjTxtScale(module.gameObject, 1.3f);
                    module.SetClick(module.OpenTips);
                });
            }
            else
            {
                var child = root.GetComponentInChildren<UIItemModule>();
                if (child != null)
                {
                    ToolScriptExtend.SetItemObjInfo(child.transform.parent, child.gameObject, rewardData.item_id,
                        (int)rewardData.num, 1.3f);
                }
            }

            if (root.childCount == 0) return;
            var target = root.GetChild(0);
            var effect = target.transform.Find("effect");
            var lockObj = target.transform.Find("btnLock");
            var received = target.transform.Find("received");

            if (isFree)
            {
                lockObj.gameObject.SetActive(false);
                effect.gameObject.SetActive(false);
                //0:未解锁 1：可领取 2：已领取
                var status = CheckZLFreeRewardStatus(id, score, TemplateId);
                received.gameObject.SetActive(status == 2);
            }
            else
            {
                //0:未解锁 1：可领取 2：已领取 3：未购买
                var status = CheckZLVipRewardStatus(id, 1, score, TemplateId);
                lockObj.gameObject.SetActive(status == 3);
                effect.gameObject.SetActive(status != 2);
                received.gameObject.SetActive(status == 2);
            }
        }
        #endregion

        #region 先锋战令
        //超值活动入口是否解锁
        public bool IsPioneerUnlock()
        {
            if (pioneerUnlockList == null)
            {
                return false;
            }
            return ToolScriptExtend.CheckDemandUnlockList(pioneerUnlockList);
        }
        
        /// <summary>
        /// 今天是第几天
        /// </summary>
        /// <param name="endTime"></param>
        /// <param name="sumDay"></param>
        /// <returns></returns>
        public int GetToday(long endTime,int sumDay = 7)
        {
            long startTimestamp = endTime - (86400*sumDay);
            DateTime startDate = DateTimeOffset.FromUnixTimeSeconds(startTimestamp).DateTime;
            DateTime curData = DateTimeOffset.FromUnixTimeSeconds((long)TimeComponent.Now).DateTime;
            TimeSpan duration = curData.Date - startDate.Date;
            return duration.Days + 1;
        }
        
        public bool GetPioneerActivityTime(out ActivityTime data)
        {
            var target =
                activityMsgList.FirstOrDefault(x => (int)x.Template == (int)activity_template.activity_template_100115);
            data = target;
            return target != null;
        }

        //请求更新先锋战令配置和变更数据
        public void C2SPioneerMsg()
        {
            if (GetPioneerActivityTime(out var data))
            {
                C2SLoadActivityInfo(data);
            }
        }
        
        //获取先锋战令任务列表
        public List<activity_battlepass_task> GetAllPioneerTaskList()
        {
            if (ToolScriptExtend.GetTable<activity_battlepass_task>(out var data))
            {
                var list = data.Where(x => x.activity_templateid == activity_template.activity_template_100115)
                    .ToList();

                return list;
            }
            return null;
        }

        // 获取先锋战令vip任务奖励列表
        public List<activity_battlepass_vip> GetAllPioneerVipList()
        {
            if (ToolScriptExtend.GetTable<activity_battlepass_vip>(out var data))
            {
                var list = data.ToList();
                list.Sort((a, b) => a.score - b.score);
                return list;
            }
            return null;
        }
        
        // 状态：0：积分未达标 1：可领取 2: 已领取 3：上锁
        public int CheckPioneerVipStatus(activity_battlepass_vip data,PushActivityBattlePassData MsgData)
        {
            var curScore = MsgData.Score;
            var needScore = data.score;
            var needVipLevel = data.vip_level;
            if (curScore < needScore)
            {
                return 0;
            }
            else
            {
                var isFreeReceived = IsZLVipRewardReceived(data.id, 0, MsgData);
                if (!isFreeReceived)
                {
                    return 1;
                }
                
                var vipLevel = GameEntry.LogicData.VipData.GetVipLevel();
                if (vipLevel < needVipLevel)
                {
                    return 3;
                }
                else
                {
                    var isVipReceived = IsZLVipRewardReceived(data.id, 1, MsgData);
                    if (!isVipReceived)
                    {
                        return 1;
                    }
                }
                return 2;
            }
        }
        
        /// <summary>
        /// vip奖励领取情况
        /// </summary>
        /// <param name="id">activity_battlepass_vip中的id</param>
        /// <param name="vipType">档位id</param>
        /// <returns></returns>
        public bool IsZLVipRewardReceived(int id, int vipType,PushActivityBattlePassData MsgData)
        {
            var value = id;
            var grade = vipType;
            return MsgData.VipDraws.Any(x => x.Id == value && x.DrawGrades.Contains(grade));
        }
        
        //获取先锋目标的进度
        public float GetPioneerTargetProgress(PushActivityBattlePassData MsgData,float startRatio)
        {
            var value1 = MsgData.Score;
            if (value1 < 0)
            {
                value1 = 0;
            }

            var list = GetAllPioneerVipList();
            var maxValue = list.Max(x => x.score);
            var value2 = maxValue;

            foreach (var data in list)
            {
                if (value1 < data.score)
                {
                    value2 = data.score;
                    break;
                }
            }
            
            //进度条显示
            if (value1 >= maxValue)
            {
                return 1;
            }
            else
            {
                var valueList = list.Select(x => x.score).ToList();
                var list1 = new List<int>();
                foreach (var score in valueList)
                {
                    list1.Add(score);
                }

                var ratioList = new List<float>();
                var nodeList = new List<int>();
                var lastValue = 0;

                var checkCount = valueList.Count;
                var unit = (1 - startRatio) / (checkCount - 1);
                for (var i = 0; i < checkCount; i++)
                {
                    ratioList.Add(i == 0 ? startRatio : unit);
                }

                for (var i = 0; i < checkCount; i++)
                {
                    if (i == 0)
                    {
                        ratioList.Add(unit * 1.0f / 2);
                    }
                    else
                    {
                        ratioList.Add(unit);
                    }
                }

                for (var i = 0; i < list1.Count; i++)
                {
                    var curScore = list1[i];
                    var offset = curScore - lastValue;
                    nodeList.Add(offset);
                    lastValue = curScore;
                }

                var value = MsgData.Score;
                float ratioSum = 0;
                int offsetSum = 0;
                for (var i = 0; i < list1.Count; i++)
                {
                    var curScore = list1[i];
                    if (value > curScore)
                    {
                        ratioSum += ratioList[i];
                        offsetSum = curScore;
                    }
                    else
                    {
                        var finalOffset = value - offsetSum;
                        ratioSum += (finalOffset * 1.0f / nodeList[i]) * ratioList[i];
                        break;
                    }
                }
                return ratioSum;
            }
        }
        
        /// <summary>
        /// 获取任务列表 activity_battlepass_task
        /// </summary>
        /// <param name="day">接取任务天数</param>
        /// <param name="index">页签顺序，0：所有页签</param>
        /// <returns></returns>
        public List<activity_battlepass_task> GetTargetTaskListByType(int day, int index)
        {
            var result = new List<activity_battlepass_task>();
            var configTasks = GetAllPioneerTaskList();
            if (configTasks == null) return result;
            var data = configTasks.Where(x =>
            {
                if (index == 0)
                {
                    return x.accept_day == day;
                }
                else
                {
                    return x.accept_day == day && x.page_order == index;
                }
            });
            result.AddRange(data);
            result.Sort((a, b) => a.activity_templateid - b.activity_templateid);
            return result;
        }
        
        // 状态：0：积分未达标 1：可领取 2: 已领取 3：上锁
        public int CheckRewardStatus(activity_battlepass_vip data,PushActivityBattlePassData MsgData,bool isFree)
        {
            var curScore = MsgData.Score;
            var needScore = data.score;
            var needVipLevel = data.vip_level;
            if (curScore < needScore)
            {
                return 0;
            }
            else
            {
                if (isFree)
                {
                    var isFreeReceived = IsZLVipRewardReceived(data.id, 0, MsgData);
                    return isFreeReceived ? 2 : 1;
                }
                else
                {
                    var vipLevel = GameEntry.LogicData.VipData.GetVipLevel();
                    if (vipLevel < needVipLevel)
                    {
                        return 3;
                    }
                    else
                    {
                        var isVipReceived = IsZLVipRewardReceived(data.id, 1, MsgData);
                        return isVipReceived ? 2 : 1;
                    }
                }
            }
        }
        #endregion
        
        /// <summary>
        /// 展示奖励列表
        /// </summary>
        /// <param name="template">奖励模板</param>
        /// <param name="rewardRoot">奖励item生成的父节点</param>
        /// <param name="rewardList">奖励列表数据</param>
        /// <param name="Scaling">奖励item缩放</param>
        /// <param name="bigFontScale">放大itemObj上的文本组件,解决缩放itemObj后文本显示太小的问题</param>
        public void ShowRewardList(GameObject template,Transform rewardRoot,List<PbGameconfig.reward> rewardList,float Scaling = 0.5f,float bigFontScale = 1.3f)
        {
            ToolScriptExtend.RecycleOrCreate(template,rewardRoot,rewardList.Count);
            for (var i = 0; i < rewardList.Count; i++)
            {
                var child = rewardRoot.GetChild(i);
                SetRewardInfo(child, rewardList[i], Scaling, bigFontScale);
            }
        }
        
        //显示奖励信息
        public void SetRewardInfo(Transform root, PbGameconfig.reward _info,float Scaling,float bigFontScale)
        {
            var itemId = (itemid)(int)_info.ItemId;
            var itemCount = _info.Num;
            var info = new reward() { item_id = itemId, num = itemCount };
            var data = ToolScriptExtend.GetItemConfig(itemId);
            if (data == null) return;
            //道具item
            var node = root.Find("node");
            LoadReward(node,info,null,Scaling,bigFontScale);
        }

        //获取开启的活动列表
        public List<ActivityTime> GetActivityList()
        {
            var result = new List<ActivityTime>();
            if (activityMsgList != null)
            {
                foreach (var msg in activityMsgList)
                {
                    var config = GetActivityConfig((int)msg.Template);
                    if (ToolScriptExtend.CheckDemandUnlockList(config.player_demands))
                    {
                        result.Add(msg);
                    }
                }
                
                result.Sort((a, b) =>
                {
                    var config_A = GetActivityConfig((int)a.Template);
                    var config_B = GetActivityConfig((int)b.Template);
                    return config_A.activity_order - config_B.activity_order;
                });
            }
            return result;
        }

        //获取模板的预制体
        public string GetTemplatePrefab(int template)
        {
            var mainData = GetActivityConfig(template);
            if (mainData == null) return null;
            var type = mainData.activity_type;
            switch (type)
            {
                case activity_type.activity_type_battlepass:
                    if (ToolScriptExtend.GetTable<activity_battlepass_main>(out var list1))
                    {
                        var target = list1.FirstOrDefault(x => (int)x.activity_templateid == template);
                        if (target != null)
                        {
                            if (target.battlepass_page_type == battlepass_page_type.battlepass_page_type_1)
                            {
                                return "ChaoZhi/ChaoZhi_WarriorZL";
                            }
                        }
                    }
                    break;
                case activity_type.activity_type_recharge:
                    if (ToolScriptExtend.GetTable<activity_recharge>(out var list))
                    {
                        var target = list.FirstOrDefault(x => (int)x.activity_templateid == template);
                        if (target != null)
                        {
                            if (target.activity_recharge_day == 0)
                            {
                                return "ChaoZhi/ChaoZhi_RoleRecharge";
                            }
                            else if (target.activity_recharge_day == 1)
                            {
                                return "ChaoZhi/ChaoZhi_DailyRecharge";
                            }
                            else if (target.activity_recharge_day == 7)
                            {
                                return "ChaoZhi/ChaoZhi_WeeklyRecharge";
                            }
                        }
                    }
                    break;
                case activity_type.activity_type_herostar:
                    return "ChaoZhi/ChaoZhi_RoleUpStar";
            }

            return null;
        }
        
        //获取商城购买开放时间
        public int GetRemainTime(ulong endTime)
        {
            var time = endTime - TimeComponent.Now;
            return time <= 0 ? 0 : (int)time;
        }

        //超值活动入口是否解锁
        public bool IsChaoZhiActivityUnlock()
        {
            if (ActivityEntryUnlockList == null)
            {
                return false;
            }
            return ToolScriptExtend.CheckDemandUnlockList(ActivityEntryUnlockList);
        }
        
        //获取（已解锁累充活动）累充积分图标列表
        public List<string> GetRechargeIconList()
        {
            var result = new List<string>();
            //如果累充活动未开启，则不显示累充积分标签
            if (!GameEntry.LogicData.ChaoZhiData.IsRechargeActivityUnlock())
            {
                return result;
            }
            
            if (ToolScriptExtend.GetTable<activity_recharge>(out var table))
            {
                foreach (var key in rechargeMsgDic.Keys)
                {
                    var data = table.FirstOrDefault(x => (int)x.activity_templateid == key);
                    if (data != null)
                    {
                        result.Add(data.icon);
                    }
                }   
            }
            return result;
        }

        public void UpdateRechargeIconList()
        {
            var form = GameEntry.UI.GetUIForm(EnumUIForm.UIMainFaceForm);
            var target = form as UIMainFaceForm;
            if (target != null)
            {
                target.RechargeScoreCtrl.UpdateIconList();
            }
        }
        
        //累充活动是否开启
        public bool IsRechargeActivityUnlock()
        {
            return rechargeMsgDic.Count > 0;
        }

        public string GetTaskFormatStr(int langId,affairtype task_type,List<string> task_value)
        {
            var resultStr = "";
            if (ToolScriptExtend.GetTable<task_dictionary>(out var dictionaries))
            {
                var temp = dictionaries.FirstOrDefault(x => x.task_type == task_type);
                if (temp != null)
                {
                    var tempList = temp.value_key;
                    var valueList = task_value;
                    var langDic = new Dictionary<string, object>();
                    for (var i = 0; i < tempList.Count; i++)
                    {
                        langDic.Add(tempList[i],valueList[i]);
                    }

                    resultStr = ToolScriptExtend.GetLangFormat(langId, langDic);
                }
            }
            return resultStr;
        }
        
        //通用奖励列表排序比较规则
        public int CompareRewardLogic(reward a, reward b)
        {
            var configA = ToolScriptExtend.GetItemConfig(a.item_id);
            var configB = ToolScriptExtend.GetItemConfig(b.item_id);
            if (configB.quality != configA.quality)
            {
                return configB.quality - configA.quality;
            }
            else
            {
                if (configB.index != configA.index)
                {
                    return configB.quality - configA.quality;
                }
                else
                {
                    return a.item_id - b.item_id;
                }
            }
        }

        //先判断界面是否打开，然后在推刷新逻辑
        public void CheckAndRefreshForm<T>(EnumUIForm flag,T data)
        {
            var form = GameEntry.UI.GetUIForm(flag);
            if (form != null)
            {
                GameEntry.UI.RefreshUIForm(flag,data);
            }
        }
        
        //奖励itemObj
        public void LoadReward(Transform node,reward info,Action<GameObject> callback = null,float size = 1,float txtScale =1)
        {
            node.localScale = Vector3.one*size;
            if (node.childCount == 0)
            {
                BagManager.CreatItem(node, info.item_id, (int)info.num, (module) =>
                {
                    module.SetClick(module.OpenTips);
                    ToolScriptExtend.SetItemObjTxtScale(module.gameObject,txtScale);
                    callback?.Invoke(module.gameObject);
                });
            }
            else
            {
                var obj = node.GetChild(0).gameObject;
                ToolScriptExtend.SetItemObjInfo(node, obj, info.item_id, (int)info.num);
                callback?.Invoke(obj);
            }
        }
        
    }
}





