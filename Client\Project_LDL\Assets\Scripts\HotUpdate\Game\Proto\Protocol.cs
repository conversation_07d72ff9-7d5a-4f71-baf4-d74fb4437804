// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: protocol.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Protocol {

  /// <summary>Holder for reflection information generated from protocol.proto</summary>
  public static partial class ProtocolReflection {

    #region Descriptor
    /// <summary>File descriptor for protocol.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static ProtocolReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cg5wcm90b2NvbC5wcm90bxIIcHJvdG9jb2wqijQKCU1lc3NhZ2VJRBIICgRO",
            "b25lEAASCQoFSGVhcnQQARIVChFTZWNyZXRTaGFyZVB1YktleRALEhMKD1Nl",
            "Y3JldFNoYXJlVGVzdBAMEgkKBUxvZ2luEA0SDwoLQWNjb3VudExpc3QQZRIR",
            "CgxCYXR0bGVDcmVhdGUQkQMSFQoQQmF0dGxlUmVjb3JkUmVhZBDhAxIaChVC",
            "YXR0bGVSZWNvcmRSZWFkR3JvdXAQ4gMSEgoNTGFuZ1RyYW5zbGF0ZRD1AxIS",
            "Cg1MYW5nU2Vuc2l0aXZlEPYDEhAKC1VuaW9uQ3JlYXRlENkEEg4KCVVuaW9u",
            "UXVpdBDaBBIPCgpVbmlvbkJyaWVmENsEEg4KCVVuaW9uTGlzdBDcBBITCg5V",
            "bmlvbkpvaW5BcHBseRDdBBIXChJVbmlvbkpvaW5BcHBseUxpc3QQ3gQSGAoT",
            "VW5pb25Kb2luQXBwbHlBZ3JlZRDfBBIZChRVbmlvbkpvaW5BcHBseVJlZnVz",
            "ZRDgBBIQCgtVbmlvblNlYXJjaBDhBBIUCg9Vbmlvbk9uZUtleUpvaW4Q4gQS",
            "EQoMVW5pb25EaXNiYW5kEOMEEhEKDFVuaW9uS2lja091dBDkBBIUCg9Vbmlv",
            "bkVkaXROb3RpZnkQ5QQSFAoPVW5pb25DaGFuZ2VGbGFnEOYEEhQKD1VuaW9u",
            "Q2hhbmdlTmFtZRDnBBIZChRVbmlvbkNoYW5nZVNob3J0TmFtZRDoBBIYChNV",
            "bmlvblRyYW5zZmVyTGVhZGVyEOkEEhAKC1VuaW9uSW52aXRlEOoEEhQKD1Vu",
            "aW9uUmFsbHlQb2ludBDrBBISCg1VbmlvblNlbmRNYWlsEOwEEhoKFVVuaW9u",
            "Q2hhbmdlUGVybWlzc2lvbhDtBBIdChhVbmlvbkNoYW5nZUpvaW5Db25kaXRp",
            "b24Q7gQSGgoVVW5pb25TZXRSZWNvbW1lbmRUZWNoEPAEEhQKD1VuaW9uRGVj",
            "bGFyZVdhchDxBBIYChNVbmlvblJlbW92ZUdhcnJpc29uEPIEEhQKD1VuaW9u",
            "R2l2ZVVwQ2l0eRDzBBIVChBVbmlvbk9wZW5SZWNydWl0EPQEEhAKC1VuaW9u",
            "UmVjb3JkEPUEEiAKG1VuaW9uR2V0Q2xlYW5JbmFjdGl2ZVN3aXRjaBD2BBId",
            "ChhVbmlvbkFwcGx5TGlzdENoYW5nZVB1c2gQ9wQSHAoXVW5pb25Sb2xlSW5m",
            "b0NoYW5nZVB1c2gQ+AQSEgoNVW5pb25GcmVlSm9pbhD5BBISCg1Vbmlvbkdp",
            "ZnRMaXN0EPoEEhcKElVuaW9uQ2xlYW5JbmFjdGl2ZRD7BBIaChVVbmlvbkNs",
            "ZWFuQWxsSW5hY3RpdmUQ/AQSFQoQVW5pb25HaWZ0UmVjZWl2ZRD9BBIYChNV",
            "bmlvbkdpZnRSZWNlaXZlQWxsEP4EEhQKD1VuaW9uTWVtYmVyTGlzdBD/BBIY",
            "ChNVbmlvbkNoYW5nZVBvc2l0aW9uEIAFEhMKDlVuaW9uTWVtYmVyT3V0EIEF",
            "EhIKDVVuaW9uUmFua0xpc3QQggUSGQoUVW5pb25SYW5kb21TaG9ydE5hbWUQ",
            "gwUSEwoOVW5pb25DaGVja05hbWUQhAUSHQoYVW5pb25Bbm9ueW1vdXNHaWZ0",
            "U3dpdGNoEIUFEhYKEVVuaW9uSGF2ZUJlZW5Kb2luEIYFEhIKDVVuaW9uVGVj",
            "aExpc3QQhwUSEwoOVW5pb25UZWNoU3R1ZHkQiAUSHwoaVW5pb25UZWNoQXV0",
            "b1VwZ3JhZGVTd2l0Y2gQiQUSHwoaVW5pb25UZWNoQXV0b1VwZ3JhZGVOb3Rp",
            "ZnkQigUSFAoPVW5pb25UZWNoRG9uYXRlEIsFEhQKD1VuaW9uQ2hhbmdlTGFu",
            "ZxCMBRIXChJVbmlvbk1pbGVTdG9uZUxpc3QQjQUSGAoTUHVzaE1pbGVzdG9u",
            "ZUNoYW5nZRCOBRIZChRVbmlvbk1pbGVTdG9uZVJld2FyZBCPBRIYChNQdXNo",
            "VW5pb25UZWNoQ2hhbmdlEJAFEhIKDVVuaW9uSGVscExpc3QQkQUSEgoNUHVz",
            "aFVuaW9uSGVscBCSBRIRCgxVbmlvbkhlbHBBbGwQkwUSFgoRUHVzaFVuaW9u",
            "SGVscFRpcHMQlAUSFAoPUHVzaFVuaW9uQ2hhbmdlEJUFEiAKG0Nyb3NzVW5p",
            "b25Sb2xlQXJ0aWNsZUNoYW5nZRC8BRIZChRDcm9zc1VuaW9uUm9sZUNhbk91",
            "dBC9BRIYChNDcm9zc1VuaW9uTWVtYmVyT3V0EL4FEhsKFkNyb3NzVW5pb25I",
            "YXZlQmVlbkpvaW4QvwUSIgodQ3Jvc3NVbmlvbkFwcGx5TGlzdENoYW5nZVB1",
            "c2gQwAUSIgodQ3Jvc3NVbmlvblRlY2hBdXRvVXBncmFkZVB1c2gQwQUSHQoY",
            "Q3Jvc3NVbmlvblRlY2hDaGFuZ2VQdXNoEMIFEhkKFENyb3NzUHVzaFVuaW9u",
            "Q2hhbmdlEMMFEhsKFkNyb3NzVW5pb25Jbml0aWF0ZUhlbHAQxAUSIgodQ3Jv",
            "c3NVbmlvbk5vdGlmeU1lbWJlckdldEhlbHAQxQUSIQocQ3Jvc3NVbmlvbkJ1",
            "aWxkUXVldWVGaW5pc2hlZBDGBRIpCiRDcm9zc1VuaW9uVHJhZGVUcmFpbkFw",
            "cG9pbnRDb25kdWN0b3IQxwUSHwoaQ3Jvc3NVbmlvblRyYWRlVHJhaW5TdW1t",
            "b24QyAUSHwoaQ3Jvc3NVbmlvblRyYWRlVHJhaW5JbnZpdGUQyQUSFwoSQ3Jv",
            "c3NQdXNoVW5pb25IZWxwEMoFEhwKF0Nyb3NzVW5pb25TdW1tb25TdWNjZXNz",
            "EMsFEhkKFENyb3NzVW5pb25NZW1iZXJKb2luEMwFEh4KGUNyb3NzVW5pb25Q",
            "dXNoVHJhZGVDaGFuZ2UQzQUSFQoQTm92aWNlUGVyc29uSW5mbxChBhITCg5O",
            "b3ZpY2VDb21wSW5mbxCiBhIPCgpOb3ZpY2VSYW5rEKMGEhQKD05vdmljZUNo",
            "YWxsZW5nZRCkBhIWChFOb3ZpY2VSZWZyZXNoQ29tcBClBhIcChdOb3ZpY2VC",
            "dXlDaGFsbGVuZ2VUaW1lcxCmBhIWChFOb3ZpY2VEcmF3QWNoaWV2ZRCnBhIS",
            "Cg1Ob3ZpY2VSZXBvcnRzEKgGEhYKEU5vdmljZVNydkNvbXBJbmZvELUGEhIK",
            "DU5vdmljZVNydlJhbmsQtgYSGQoUTm92aWNlU3J2UmVmcmVzaENvbXAQtwYS",
            "FwoSTm92aWNlU3J2Q2hhbGxlbmdlELgGEhMKDlBlYWtQZXJzb25JbmZvEL8G",
            "Eg0KCFBlYWtSYW5rEMAGEhUKEFBlYWtSYW5rSW50ZXJ2YWwQwQYSEgoNUGVh",
            "a0NoYWxsZW5nZRDCBhIQCgtQZWFrUmVwb3J0cxDDBhIQCgtQZWFrU3J2UmFu",
            "axDKBhIYChNQZWFrU3J2UmFua0ludGVydmFsEMsGEhUKEFBlYWtTcnZDaGFs",
            "bGVuZ2UQzAYSEwoOQXJlbmFPcGVuSW5mb3MQ+wYSEAoLQXJlbmFHTU9QZW4Q",
            "/AYSEAoLQXJlbmFHTVNodXQQ/QYSDgoJUm9sZUVudGVyEOkHEg8KClJvbGVD",
            "cmVhdGUQ6gcSFQoQUHVzaFJvbGVBcnRpY2xlcxDrBxILCgZSb2xlR00Q7AcS",
            "EwoOUm9sZVF1ZXJ5TG9jYWwQ7QcSEwoOUm9sZVF1ZXJ5TXVsdGkQ7gcSFAoP",
            "UHVzaFBvd2VyQ2hhbmdlEO8HEhgKE1JvbGVDaGVja0NvbmRpdGlvbnMQ8AcS",
            "EAoLUHVzaEFydGljbGUQ8QcSFQoQSGVyb1VwZ3JhZGVMZXZlbBDMCBIUCg9I",
            "ZXJvVXBncmFkZVN0YXIQzQgSFQoQSGVyb1VwZ3JhZGVTa2lsbBDOCBIQCgtI",
            "ZXJvTWFwU2hvdxDPCBIaChVIZXJvVXBncmFkZUhvbm9yTGV2ZWwQ0AgSEgoN",
            "SGVyb1N5bnRoZXRpYxDRCBIXChJIZXJvUXVlcnlBdHRyQnVpbGQQ/wgSEAoL",
            "RXF1aXBUYWtlT24QsAkSEQoMRXF1aXBUYWtlT2ZmELEJEhEKDEVxdWlwTGV2",
            "ZWxVcBCyCRITCg5FcXVpcFByb21vdGlvbhCzCRIRCgxFcXVpcFByb2R1Y2UQ",
            "tQkSEQoMRXF1aXBSZXNvbHZlELcJEhsKFkVxdWlwTWF0ZXJpYWxTeW50aGVz",
            "aXMQuAkSGQoURXF1aXBNYXRlcmlhbFJlc29sdmUQuQkSDAoHSXRlbVVzZRCU",
            "ChIMCgdJdGVtQnV5EJUKEhUKEFJlY3J1aXRQb29sc0luZm8Q+AoSFgoRUmVj",
            "cnVpdFBvb2xDaGFuZ2UQ+QoSEwoOUmVjcnVpdFBvb2xEZWwQ+goSDAoHUmVj",
            "cnVpdBD7ChITCg5SZWNydWl0U2V0V2lzaBD8ChIUCg9SZWNydWl0RHJhd1dp",
            "c2gQ/QoSGQoUUmVjcnVpdFVwZGF0ZVByZXZpZXcQ/goSEAoLQnVpbGRDcmVh",
            "dGUQwAwSEQoMQnVpbGRVcGdyYWRlEMEMEg4KCUJ1aWxkTW92ZRDCDBIVChBC",
            "dWlsZFF1ZXVlRmluaXNoEMMMEhkKFEJ1aWxkUXVldWVBY2NlbGVyYXRlEMQM",
            "EhMKDkJ1aWxkUXVldWVIZWxwEMYMEhwKF1B1c2hCdWlsZEhlbHBBY2NlbGVy",
            "YXRlEMcMEg4KCVB1c2hCdWlsZBDIDBIOCglTb2xkaWVyT3AQpA0SFQoQU29s",
            "ZGllclRyZWF0bWVudBClDRIWChFQdXNoU29sZGllckNoYW5nZRCmDRIYChNQ",
            "dXNoSG9zcGl0YWxzQ2hhbmdlEKcNEhMKDlN1cnZpdm9yU3VtbW9uENYNEhMK",
            "DlN1cnZpdm9yU3RhclVwENcNEhUKEFN1cnZpdm9yRGlzcGF0Y2gQ2A0SGQoU",
            "U3Vydml2b3JGYXN0RGlzcGF0Y2gQ2Q0SEgoNV29ya2VyUmVudGluZxCIDhIT",
            "Cg5QdXNoV29ya2VyTG9jaxCJDhIOCglUZWNoU3R1ZHkQ7A4SEwoOUHVzaFRl",
            "Y2hDaGFuZ2UQ7Q4SDwoKVWF2VXBncmFkZRDQDxIPCgpVYXZVc2VTa2luENEP",
            "EhYKEVVhdkVxdWlwQ29tcG9uZW50ENIPEh4KGVVhdk9uZUNsaWNrRXF1aXBD",
            "b21wb25lbnQQ0w8SGQoUVWF2T25lQ2xpY2tTeW50aGVzaXMQ1A8SEAoLVWF2",
            "UmVzZWFyY2gQ1Q8SDgoJU3RvcmVMb2FkELgXEg0KCFN0b3JlQnV5ELkXEg0K",
            "CFNob3BMb2FkEJwYEhYKEVNob3BEYWlseURlYWxMb2FkEJ0YEhYKEVB1c2hT",
            "aG9wRGFpbHlEZWFsEJ4YEhgKE1Nob3BEYWlseURlYWxTd2l0Y2gQnxgSGwoW",
            "U2hvcERhaWx5RGVhbENsYWltRnJlZRCgGBIaChVTaG9wRGFpbHlNdXN0SGF2",
            "ZUxvYWQQohgSGwoWU2hvcERhaWx5TXVzdEhhdmVDbGFpbRCjGBIVChBTaG9w",
            "V2Vla0RlYWxMb2FkEKcYEhYKEVNob3BNb250aENhcmRMb2FkEKwYEhcKElNo",
            "b3BNb250aENhcmRDbGFpbRCtGBIVChBTaG9wV2Vla0NhcmRMb2FkELEYEhoK",
            "FVNob3BXZWVrQ2FyZENsYWltRnJlZRCyGBIbChZTaG9wV2Vla0NhcmRDbGFp",
            "bVRvZGF5ELMYEhkKFFNob3BHaWZ0UGFja01hbGxMb2FkELYYEhUKEFNob3BE",
            "YXduRnVuZExvYWQQuxgSFgoRU2hvcERhd25GdW5kQ2xhaW0QvBgSFAoPU2hv",
            "cERpYW1vbmRMb2FkEMAYEg8KClNob3BHZXRXYXkQ/xgSEgoNUGF5bWVudFJl",
            "d2FyZBCBGRIQCgtQdXNoUGF5bWVudBCCGRIeChlQYXltZW50T3JkZXJCdXNp",
            "bmVzc1J1bGVzEIMZEhIKDVB1c2hQcml2aWxlZ2UQhBkSDwoKVGVhbU1vZGlm",
            "eRDlGRIOCglUZWFtUXVlcnkQ5hkSEAoLQmF0dGxlRGVidWcQ5xkSFQoQVGVh",
            "bURlZmVuZE1vZGlmeRDoGRIcChdCYXR0bGVDcmVhdGVCeUZvcm1hdGlvbhDp",
            "GRIUCg9CYXR0bGVUZWFtUXVlcnkQ6hkSEwoOUHVzaFRlYW1TdGF0dXMQ6xkS",
            "FgoRUHVzaEZvcm1hdGlvblRlYW0Q7BkSEAoLRHVuZ2VvbkxvYWQQyRoSEQoM",
            "RHVuZ2VvbkZpZ2h0EMoaEhIKDUR1bmdlb25TZXR0bGUQyxoSFAoPRHVuZ2Vv",
            "bkNsYWltQm94EMwaEh4KGUR1bmdlb25BY2N1bXVsYXRlZFJld2FyZHMQzRoS",
            "DQoIVGFza0xpc3QQ+hoSEwoOVGFza0NoYW5nZVB1c2gQ+xoSEAoLVGFza1Jl",
            "Y2VpdmUQ/BoSHAoXVGFza1JlY2VpdmVTY29yZVJld2FyZHMQ/RoSGAoTSW5u",
            "ZXJDaXR5T2NjdXB5R3JpZBCtGxIaChVJbm5lckNpdHlVbmxvY2tSZWdpb24Q",
            "rhsSGgoVVmlwUmVjZWl2ZURhaWx5UG9pbnRzEN4bEhgKE1ZpcFJlY2VpdmVE",
            "YWlseUdpZnQQ3xsSEgoNUHVzaFZpcENoYW5nZRDgGxINCghSYW5rTGlzdBCR",
            "HBIUCg9QdXNoVHJhZGVDaGFuZ2UQ9BwSDwoKVHJhZGVTaGFyZRD1HBIQCgtU",
            "cmFkZUxvb2tVcBD2HBIcChdUcmFkZUNhcmdvVHJhbnNwb3J0TGlzdBD3HBIX",
            "ChJUcmFkZVZhblJlY29yZExpc3QQ+RwSEwoOVHJhZGVWYW5EZXRhaWwQ+hwS",
            "FAoPVHJhZGVWYW5SZWZyZXNoEPscEhMKDlRyYWRlVmFuRGVwYXJ0EPwcEhMK",
            "DlRyYWRlVmFuU2V0T3V0EP0cEhoKFVRyYWRlVmFuUmVjZWl2ZVJld2FyZBD+",
            "HBIQCgtUcmFkZVZhblJvYhD/HBITCg5UcmFkZVZhbldhbnRlZBCAHRIUCg9U",
            "cmFkZVZhbkNvbGxlY3QQgR0SHwoaVHJhZGVUcmFpbkFwcG9pbnRDb25kdWN0",
            "b3IQgh0SGwoWVHJhZGVUcmFpblJlZnJlc2hHb29kcxCEHRIVChBUcmFkZVRy",
            "YWluTGluZVVwEIUdEhUKEFRyYWRlVHJhaW5EZXRhaWwQhh0SFQoQVHJhZGVU",
            "cmFpblRoYW5rcxCHHRIZChRUcmFkZVRyYWluVGhhbmtzTGlzdBCIHRIVChBU",
            "cmFkZVRyYWluSW52aXRlEIkdEhgKE1RyYWRlVHJhaW5Gb3JtYXRpb24Qih0S",
            "HQoYVHJhZGVUcmFpbkZvcm1hdGlvbk9yZGVyEIsdEicKIlRyYWRlVHJhaW5T",
            "ZWxlY3RWaXBQYXNzZW5nZXJSZXdhcmQQjB0SHQoYVHJhZGVUcmFpbkd1YXJk",
            "QW5nZWxMaWtlEI0dEhoKFVRyYWRlVHJhaW5BZ3JlZUludml0ZRCOHRIdChhU",
            "cmFkZVJlY2VpdmVUaGFua3NSZXdhcmQQjx0SGQoUVHJhZGVUcmFpblN0YXJ0",
            "RmlnaHQQkB0SHgoZVHJhZGVUcmFpbkZpZ2h0UmVjb3JkTGlzdBCRHRIcChdU",
            "cmFkZUcyR1RyYWluU3RhcnRGaWdodBDEHRIVChBUcmFkZUcyR1ZhbkZpZ2h0",
            "EMUdEhMKDlRyYWRlRzJHUm9iTG9nEMYdEhYKEUFjdGl2aXR5T3BlbkluZm9z",
            "EKEfEhcKElB1c2hBY3Rpdml0eUNoYW5nZRCiHxIUCg9QdXNoQWN0aXZpdHlE",
            "ZWwQox8SEwoOQWN0aXZpdHlDb25maWcQpB8SEQoMQWN0aXZpdHlEYXRhEKUf",
            "EhEKDEFjdGl2aXR5RHJhdxCmHxIQCgtBY3Rpdml0eUJ1eRCnHxIfChpQdXNo",
            "QWN0aXZpdHlIZXJvU3RhckNvbmZpZxDTHxIdChhQdXNoQWN0aXZpdHlIZXJv",
            "U3RhckRhdGEQ1B8SHwoaUHVzaEFjdGl2aXR5UmVjaGFyZ2VDb25maWcQ3R8S",
            "HQoYUHVzaEFjdGl2aXR5UmVjaGFyZ2VEYXRhEN4fEiEKHFB1c2hBY3Rpdml0",
            "eUJhdHRsZVBhc3NDb25maWcQ5x8SHwoaUHVzaEFjdGl2aXR5QmF0dGxlUGFz",
            "c0RhdGEQ6B8SHwoaQWN0aXZpdHlCYXR0bGVQYXNzVGFza0xpc3QQ6R8SHwoa",
            "QWN0aXZpdHlCYXR0bGVQYXNzRHJhd1Rhc2sQ6h8SJQogUHVzaEFjdGl2aXR5",
            "QmF0dGxlUGFzc1Rhc2tDaGFuZ2UQ6x8SHAoXUHVzaEFjdGl2aXR5UG93ZXJD",
            "b25maWcQ8R8SGgoVUHVzaEFjdGl2aXR5UG93ZXJEYXRhEPIfEg4KCVRvd2Vy",
            "TG9hZBCJJxIQCgtUb3dlckNob29zZRCKJxIPCgpUb3dlckZpZ2h0EIsnEhIK",
            "DFBheW1lbnRPcmRlchDBuAISDwoJVG93blNydkdtEJDIAhITCg1Ub3duU3J2",
            "Q3JlYXRlEJHIAhIQCgpDYW1lcmFJbml0ENjJAhISCgxDYW1lcmFSZW1vdmUQ",
            "2ckCEhAKCkNhbWVyYU1vdmUQ2skCEhEKC01vbnN0ZXJUaXBzELzKAhIOCghN",
            "YWlsTG9hZBCZ7wISDgoITWFpbFNlbmQQmu8CEhAKCk1haWxEZXRhaWwQm+8C",
            "Eg4KCE1haWxSZWFkEJzvAhIPCglNYWlsQ2xhaW0Qne8CEhAKCk1haWxEZWxl",
            "dGUQnu8CEhIKDE1haWxGYXZvcml0ZRCf7wISFAoOTWFpbFVuZmF2b3JpdGUQ",
            "oO8CEg4KCE1haWxMaWtlEKHvAhIOCghQdXNoTWFpbBDL7wIqpzsKCUVycm9y",
            "Q29kZRILCgdTdWNjZXNzEAASFgoSU3VjY2Vzc1dpdGhvdXRTYXZlEAESGAoT",
            "UHJvdG9VbmFyc2hhbEZhaWxlZBCRThIXChJQcm90b01hcnNoYWxGYWlsZWQQ",
            "kk4SFQoQUHJvdG9jb2xOb3RGb3VuZBCTThIVChBSZW1vdGVDYWxsRmFpbGVk",
            "EJROEhMKDkNvbmZpZ05vdEZvdW5kEJVOEgsKBklEWmVybxCWThIQCgtEZXZl",
            "bG9wT25seRCXThIPCgpQdXNoRmFpbGVkEJhOEg8KClBhcmFtRXJyb3IQm04S",
            "EAoLUGFyYW1FcnJvcjIQnE4SEAoLUGFyYW1FcnJvcjMQnU4SEAoLUGFyYW1F",
            "cnJvcjQQnk4SEAoLUGFyYW1FcnJvcjUQn04SEAoLUGFyYW1FcnJvcjYQoE4S",
            "EAoLUGFyYW1FcnJvcjcQoU4SEAoLUGFyYW1FcnJvcjgQok4SEAoLUGFyYW1F",
            "cnJvcjkQo04SEQoMUGFyYW1FcnJvcjEwEKROEhYKEUNvbmZpZ1Jld2FyZEVy",
            "cm9yEKVOEhcKEkNvbmZpZ0NvbnN1bWVFcnJvchCmThIVChBDaGFuZ2VTaG93",
            "RmFpbGVkEKdOEhYKEUNoYW5nZVNob3dGYWlsZWQyEKhOEhcKElVubG9ja0hl",
            "cm9Ob3RGb3VuZBCvThIYChNVbmxvY2tIZXJvTGV2ZWxMZXNzELBOEhcKElVu",
            "bG9ja0hlcm9TdGFyTGVzcxCxThITCg5MZXZlbE5vdEVub3VnaBCyThISCg1T",
            "dGFyTm90RW5vdWdoELNOEhMKDkJ1aWxkTGV2ZWxMZXNzELROEhMKDkRCQ3Jl",
            "YXRlRmFpbGVkELlOEhIKDURCUXVlcnlGYWlsZWQQuk4SEwoOREJVcGRhdGVG",
            "YWlsZWQQu04SEQoMRW5jb2RlRmFpbGVkELxOEhEKDERlY29kZUZhaWxlZBC9",
            "ThISCg1FbmNyeXB0RmFpbGVkEL5OEhIKDURlY3J5cHRGYWlsZWQQv04SEgoN",
            "UmV3YXJkQ2xhaW1lZBDAThIUCg9SZXdhcmROb3RBY3RpdmUQwU4SHAoXUmV3",
            "YXJkTm90TWF0Y2hDb25kaXRpb24Qwk4SFgoRUmV3YXJkc0NsYWltRW1wdHkQ",
            "w04SEAoLSW52YWxpZFdvcmQQxE4SDQoIVGltZXNPdXQQxU4SEAoLQnV5VGlt",
            "ZXNPdXQQxk4SFwoSUHJpdmlsZWdlTm90QWN0aXZlEMdOEhUKEFByaXZpbGVn",
            "ZUV4cGlyZWQQyE4SEgoNVW5TdXBwb3J0VHlwZRDJThIPCgpTeXN0ZW1CdXN5",
            "EMpOEhQKD1RlYW1Ob3RVbmxvY2tlZBDLThIQCgtDb25maWdFcnJvchDNThIQ",
            "CgtDb25maWdFcnJvMhDOThIQCgtDb25maWdFcnJvMxDPThIQCgtDb25maWdF",
            "cnJvNBDQThIQCgtDb25maWdFcnJvNRDRThISCg1JdGVtTm90RW5vdWdoEPRO",
            "EhcKElJlc291cmNlMU5vdEVub3VnaBD1ThIXChJSZXNvdXJjZTJOb3RFbm91",
            "Z2gQ9k4SFwoSUmVzb3VyY2UzTm90RW5vdWdoEPdOEhcKElJlc291cmNlNE5v",
            "dEVub3VnaBD4ThIXChJSZXNvdXJjZTVOb3RFbm91Z2gQ+U4SFwoSUmVzb3Vy",
            "Y2U2Tm90RW5vdWdoEPpOEhcKElJlc291cmNlN05vdEVub3VnaBD7ThIXChJS",
            "ZXNvdXJjZThOb3RFbm91Z2gQ/E4SFwoSUmVzb3VyY2U5Tm90RW5vdWdoEP1O",
            "EhgKE1Jlc291cmNlMTBOb3RFbm91Z2gQ/k4SGAoTUmVzb3VyY2UxMU5vdEVu",
            "b3VnaBD/ThIYChNSZXNvdXJjZTEyTm90RW5vdWdoEIBPEhgKE1Jlc291cmNl",
            "MTNOb3RFbm91Z2gQgU8SGAoTUmVzb3VyY2UxNE5vdEVub3VnaBCCTxIYChNS",
            "ZXNvdXJjZTE1Tm90RW5vdWdoEINPEhQKD1Nlc3Npb25Ob3RSZWFkeRD5VRIW",
            "ChFTaGFyZUtleUdlbkZhaWxlZBD6VRIXChJTaGFyZUtleVRlc3RGYWlsZWQQ",
            "+1USGwoWTG9naW5Ub2tlblZlcmlmeUZhaWxlZBD8VRIcChdTZXJ2aWNlVmVy",
            "c2lvbnNOb3RGb3VuZBD9VRISCg1TZXJ2ZXJJREVtcHR5EP5VEhcKEkFjY291",
            "bnRMaXN0SXNFbXB0eRD/VRIXChJSb2xlUG9sbGVyTm90Rm91bmQQ4V0SEwoO",
            "Um9sZUdldFRpbWVvdXQQ4l0SFAoPUm9sZUxvZ2luRmFpbGVkEONdEhMKDlJv",
            "bGVOZWVkQ3JlYXRlEORdEhUKEFJvbGVDcmVhdGVGYWlsZWQQ5V0SFgoRUm9s",
            "ZUNyZWF0ZWRCZWZvcmUQ5l0SGwoWUm9sZUdNQmFnRXhlY3V0ZUZhaWxlZBDn",
            "XRIaChVSb2xlR01TR0V4ZWN1dGVGYWlsZWQQ6F0SEQoMSGVyb05vdEV4aXN0",
            "EI1gEhUKEEhlcm9BbHJlYWR5RXhpc3QQjmASEQoMSGVyb1N0YXJGdWxsEI9g",
            "EhIKDUhlcm9MZXZlbEZ1bGwQkGASFwoSSGVyb1NraWxsTGV2ZWxGdWxsEJFg",
            "EhsKFkhlcm9UeXBlTm9uQ29uZm9ybWFuY2UQkmASFAoPSGVyb1NraWxsVW5s",
            "b2NrEJNgEhUKEEhlcm9DYW5Ob3RJblRlYW0QlGASFwoSSGVyb0hvbm9yTGV2",
            "ZWxGdWxsEJVgEhUKEEhlcm9DcmVhdGVGYWlsZWQQtGASFwoSQnVpbGRBbHJl",
            "YWR5RXhpc3RzEPFgEhwKF0J1aWxkRGVtYW5kTm90U2F0aXNmaWVkEPJgEh0K",
            "GEJ1aWxkQ3JlYXRlQ29zdE5vdEVub3VnaBDzYBIXChJCdWlsZENhbk5vdHVw",
            "Z3JhZGUQ9GASEwoOQnVpbGROb3RFeGlzdHMQ9WASHwoaQnVpbGRJc0NyZWF0",
            "aW5nT3JVcGdyYWRpbmcQ9mASHgoZQnVpbGRVcGdyYWRlQ29zdE5vdEVub3Vn",
            "aBD3YBIUCg9CdWlsZENhbk5vdE1vdmUQ+GASGAoTQnVpbGRRdWV1ZU5vdEV4",
            "aXN0cxCFYRIYChNCdWlsZFF1ZXVlTm90RmluaXNoEIZhEhwKF0J1aWxkUXVl",
            "dWVIYXNCZWVuSGVscGVkEIdhEhcKEkJ1aWxkUXVldWVJc0ZpbmlzaBCIYRIZ",
            "ChRCdWlsZFF1ZXVlQ2FuTm90SGVscBCJYRIfChpCdWlsZFF1ZXVlQ2FuTm90",
            "QWNjZWxlcmF0ZRCKYRIjCh5CdWlsZFF1ZXVlSXRlbUNhbk5vdEFjY2VsZXJh",
            "dGUQi2ESJwoiQnVpbGRRdWV1ZUl0ZW1BY2NlbGVyYXRlTm90ZUVub3VnaBCM",
            "YRIqCiVCdWlsZFF1ZXVlRGlhbW9uZEFjY2VsZXJhdGVOb3RlRW5vdWdoEI1h",
            "EhkKFEJ1aWxkV29ya2VyTm90RXhpc3RzEI5hEhoKFUJ1aWxkV29ya2VydUlz",
            "Tm90SWRsZRCPYRISCg1CdWlsZE1heExldmVsEJBhEhkKFElubmVyQ2l0eUdy",
            "aWROb3ROZXh0ELliEhwKF0lubmVyQ2l0eUR1bmdlb25Ob3RQYXNzELpiEhsK",
            "FklubmVyQ2l0eVJlZ2lvbk5vdE5leHQQu2ISGwoWSW5uZXJDaXR5R3JpZE5v",
            "dFVubG9jaxC8YhIWChBFcXVpcEFscmVhZHlXb3JuEN2IARISCg1FcXVpcE5v",
            "dEZvdW5kEJ1jEhMKDkVxdWlwTGV2ZWxGdWxsEJ5jEhwKF0VxdWlwUHJvbW90",
            "aW9uTGV2ZWxGdWxsEJ9jEhcKEkVxdWlwTm90UHJvZHVjaWJsZRCgYxIUCg9F",
            "cXVpcFRhcmdldFNhbWUQoWMSGQoURXF1aXBOb3RUYXJnZXRPYmplY3QQomMS",
            "FgoRRXF1aXBDcmVhdGVGYWlsZWQQo2MSGgoVRXF1aXBDYW50VXBncmFkZUxl",
            "dmVsEKRjEh4KGUVxdWlwQ2FudFVwZ3JhZGVQcm9tb3Rpb24QpWMSFwoSRXF1",
            "aXBUYWtlT25Ob01hdGNoEKZjEh4KGUVxdWlwVGFrZU9uQ3VycmVudEFscmVh",
            "ZHkQp2MSFgoRRXF1aXBUYWtlT2ZmRW1wdHkQqGMSGAoTRXF1aXBSZXNvbHZl",
            "SURFbXB0eRCpYxIYChNFcXVpcFJlc29sdmVEcmVzc2VkEKpjEh8KGkVxdWlw",
            "TWF0ZXJpYWxDYW50U3ludGhlc2lzEKtjEh0KGEVxdWlwTWF0ZXJpYWxDYW50",
            "UmVzb2x2ZRCsYxIRCgxJdGVtTm90Rm91bmQQzmMSEgoNSXRlbVVzZUZhaWxl",
            "ZBDPYxISCg1JdGVtQWRkRmFpbGVkENBjEhYKEUl0ZW1BdXRvVXNlRmFpbGVk",
            "ENFjEhcKEkl0ZW1DYW50UGF5RGlhbW9uZBDSYxISCg1SZWNydWl0RmFpbGVk",
            "EIFkEhgKE1JlY3J1aXRXaXNoU2V0Rmlyc3QQgmQSHgoZUmVjcnVpdFdpc2hU",
            "aW1lc05vdEVub3VnaBCDZBIYChNTdG9yZUdvb2RzTm90RW5vdWdoELJkEhwK",
            "F1Nob3BEYWlseURlYWxHaWZ0TG9ja2VkEOVkEhwKF1Nob3BDYW50UHVyY2hh",
            "c2VNZXJnZWQxEOZkEhsKFlNob3BNb250aENhcmROb3RBY3RpdmUQ52QSGQoU",
            "U2hvcE1vbnRoQ2FyZEV4cGlyZWQQ6GQSGgoVU2hvcFdlZWtDYXJkTm90QWN0",
            "aXZlEOlkEhgKE1Nob3BXZWVrQ2FyZEV4cGlyZWQQ6mQSGAoTRHVuZ2VvbkNh",
            "bnRGaWdodE1heBCsZhIdChhEdW5nZW9uTGFzdEZpZ2h0Tm90TWF0Y2gQrWYS",
            "HQoYRHVuZ2VvbkZpZ2h0Rmlyc3RNaXNzaW9uEK5mEh8KGkR1bmdlb25Cb3hJ",
            "REdyZWF0ZXJUaGVuTWF4EK9mEhYKEUR1bmdlb25Cb3hDbGFpbWVkELBmEhkK",
            "FER1bmdlb25GaWdodFJlcGVhdGVkELFmEhoKFUR1bmdlb25GaWdodE5vdE5l",
            "eHRJRBCyZhIVChBTdXJ2aXZvck5vdEZvdW5kEN5mEhAKC1Vhdk1heExldmVs",
            "EJBnEhIKDVNvbGRpZXJNYXhOdW0Q9GcSDgoJVmlwRXhwaXJlENhoEhcKElRy",
            "YWRlVmFuU2V0T3V0RnVsbBC8aRIYChNUcmFkZVRpbWVzTm90RW5vdWdoEL1p",
            "EhIKDVRyYWRlTm90Rm91bmQQvmkSGAoTVHJhZGVIYXZlQmVlbkRlcGFydBC/",
            "aRIUCg9UcmFkZU5vdEFycml2ZWQQwGkSGAoTVHJhZGVSZWNvcmROb3RGb3Vu",
            "ZBDBaRIQCgtUcmFkZVdhbnRlZBDCaRIXChJUcmFkZVJvYlRpbWVzTGltaXQQ",
            "w2kSJAofVHJhZGVDcmVhdGVDYXJnb1RyYW5zcG9ydEZhaWxlZBDEaRIXChJU",
            "cmFkZVRyYWluTm90Rm91bmQQxWkSGwoWVHJhZGVDYW5ub3RTdW1tb25UcmFp",
            "bhDGaRIaChVUcmFkZU5vdEluUHJlcGFyZVRpbWUQx2kSHAoXVHJhZGVUcmFp",
            "blRoYW5rc0FscmVhZHkQyGkSHwoaVHJhZGVOb0Zvcm1hdGlvblBlcm1pc3Np",
            "b24QyWkSFgoRVHJhZGVOb1Blcm1pc3Npb24QymkSFAoPVHJhZGVSZXBlYXRM",
            "aWtlEMtpEhYKEVRyYWRlUmVwZWF0SW52aXRlEMxpEhIKDVRyYWRlSW52aXRl",
            "Q0QQzWkSFwoSVHJhZGVJbnZpdGVFeHBpcmVkEM5pEhwKF1RyYWRlVHJhaW5J",
            "blByb3RlY3RUaW1lEM9pEhQKD1RyYWRlVmFuQXJyaXZlZBDQaRIaChVUcmFk",
            "ZVZhbkluUHJvdGVjdFRpbWUQ0WkSHAoXVHJhZGVUcmFpblJvYlRpbWVzTGlt",
            "aXQQ0mkSGQoUVGVjaFByZVRlY2hOb3RBY3RpdmUQoGoSGAoTVGVjaFByZVRl",
            "Y2hOb3RTdHVkeRChahIRCgxUZWNoTWF4TGV2ZWwQomoSGwoVQWNjb3VudFBv",
            "bGxlck5vdEZvdW5kEMG4AhIXChFBY2NvdW50R2V0VGltZW91dBDCuAISGQoT",
            "QWNjb3VudENyZWF0ZUZhaWxlZBDDuAISFgoQVW5pb25JbnZhbGlkV29yZBCp",
            "wAISFwoRVW5pb25DcmVhdGVGYWlsZWQQqsACEhUKD1VuaW9uTmFtZUxlbmd0",
            "aBCrwAISFQoPVW5pb25OYW1lUmVwZWF0EKzAAhITCg1Vbmlvbk5vdEZvdW5k",
            "EK3AAhIVCg9VbmlvbkFwcGx5QWdyZWUQrsACEhYKEFVuaW9uQXBwbHlSZWZ1",
            "c2UQr8ACEhYKEFVuaW9uQXBwbHlSZXBlYXQQsMACEiIKHFVuaW9uTm90RW5v",
            "dWdoQXBwbHlDb25kaXRpb24QscACEiMKHVVuaW9uTm90RW5vdWdoQ3JlYXRl",
            "Q29uZGl0aW9uELLAAhIZChNVbmlvbk1lbWJlck51bUxpbWl0ELPAAhIfChlV",
            "bmlvbk5vRm91bmRNZWV0Q29uZGl0aW9uELTAAhIXChFVbmlvbk5vRW50ZXJV",
            "bmlvbhC1wAISFwoRVW5pb25Ob1Blcm1pc3Npb24QtsACEhoKFFVuaW9uTm90",
            "aWNlTGVuZ3RoRXJyELfAAhIdChdVbmlvbkNvbmRpdGlvblBhcmFtc0VychC4",
            "wAISGgoUVW5pb25HaWZ0SGFzUmVjZWl2ZWQQucACEhsKFVVuaW9uQXBwbHlJ",
            "bmZvVGltZW91dBC6wAISFAoOVW5pb25Ob3RNZW1iZXIQu8ACEhcKEVVuaW9u",
            "TWVtYmVyTnVtR1QxELzAAhIaChRVbmlvbk1lbWJlckNhbm5vdE91dBC9wAIS",
            "GwoVVW5pb24yNEhvdXJDYW5ub3RKb2luEL7AAhIcChZVbmlvbk5vdE9wZW5V",
            "bmlvbkJ1aWxkEL/AAhIXChFVbmlvbkhhc0pvaW5VbmlvbhDAwAISHQoXVW5p",
            "b25QZXJtaXNzaW9uTnVtTGltaXQQwcACEhkKE1VuaW9uUG93ZXJOb3RFbm91",
            "Z2gQwsACEh0KF1VuaW9uQmFzZUxldmVsTm90RW5vdWdoEMPAAhIdChdVbmlv",
            "bkdpZnRMZXZlbE5vdEVub3VnaBDEwAISFwoRVW5pb25HaWZ0Tm90RXhpc3QQ",
            "xcACEisKJVVuaW9uTWVtYmVySGVhZHF1YXJ0ZXJzTGV2ZWxOb3RFbm91Z2gQ",
            "xsACEhgKElVuaW9uVGVjaFVwZ3JhZGluZxDHwAISFwoRVW5pb25UZWNoTGV2",
            "ZWxNYXgQyMACEh0KF1VuaW9uVGVjaERvbmF0ZVRpbWVzTWF4EMnAAhIYChJV",
            "bmlvblRlY2hOb3RFbm91Z2gQysACEh8KGVVuaW9uT25seU9uZVJlY29tbWVu",
            "ZFRlY2gQy8ACEhcKEVVuaW9uTWlsZVN0b25lRW5kEMzAAhIkCh5Vbmlvbk1p",
            "bGVTdG9uZVJld2FyZE5vUmVjZWl2ZTEQzcACEiQKHlVuaW9uTWlsZVN0b25l",
            "UmV3YXJkTm9SZWNlaXZlMhDOwAISGAoSVW5pb25QcmVUZWNoTG9ja2VkEM/A",
            "AhIYChJVbmlvblRlY2hUYWdMb2NrZWQQ0MACEhoKFFVuaW9uVGVjaFdhaXRV",
            "cGdyYWRlENHAAhIdChdVbmlvbk1pbGVzdG9uZU5vdE1lbWJlchDSwAISFgoQ",
            "VW5pb25HaWZ0VGltZW91dBDTwAISGQoTVW5pb25UcmFkZUhhdmVUcmFpbhDU",
            "wAISHwoZVW5pb25UcmFkZVRyYWluU3VtbW9uRmFpbBDVwAISIQobVW5pb25U",
            "cmFkZUpvaW5UaW1lTm90RW5vdWdoENbAAhIVCg9BcmVuYVNydlRpbWVvdXQQ",
            "kMgCEhIKDEFyZW5hUnBjRmFpbBCRyAISGAoSQXJlbmFHcm91cE5vdEZvdW5k",
            "EJPIAhIZChNBcmVuYVBvbGxlck5vdEZvdW5kEJTIAhIXChFBcmVuYVR5cGVO",
            "b3RGb3VuZBCVyAISGQoTTm92aWNlSW5mb05vdENyZWF0ZRCbyAISIQobTm92",
            "aWNlQ2hhbGxlbmdlUmFua05vdFZhbGlkEJzIAhIdChdOb3ZpY2VDaGFsbGVu",
            "Z2VOb3RNYXRjaBCdyAISHAoWTm92aWNlQnV5Q2hhbGxlbmdlRmFpbBCeyAIS",
            "HgoYTm92aWNlQWNoaWV2ZUFscmVhZHlEcmF3EJ/IAhIgChpOb3ZpY2VBY2hp",
            "ZXZlUmFua05vdEVub3VnaBCgyAISIwodTm92aWNlQ2hhbGxlbmdlVGltZXNO",
            "b3RFbm91Z2gQocgCEhsKFVBlYWtSYW5rR3JvdXBOb3RGb3VuZBD0yAISIQob",
            "UGVha0NoYWxsZW5nZVRpbWVzTm90RW5vdWdoEPXIAhIXChFQZWFrSW5mb05v",
            "dENyZWF0ZRD3yAISGwoVUGVha0NoYWxsZW5nZU5vdE1hdGNoEPjIAhIfChlQ",
            "ZWFrQ2hhbGxlbmdlUmFua05vdFJhbmdlEPnIAhIfChlQYXltZW50Tm90U3Vw",
            "cG9ydFRva2VuUGF5EPnPAhIaChRQYXltZW50UHJpY2VOb3RNYXRjaBD6zwIS",
            "GgoUUGF5bWVudE9yZGVyTm90Rm91bmQQ+88CEhkKE1BheW1lbnRPcmRlcklu",
            "dmFsaWQQ/M8CEhwKFlBheW1lbnRDaGFubmVsTm90Rm91bmQQ/c8CEh4KGFBh",
            "eW1lbnRPcmRlclVwZGF0ZUZhaWxlZBD+zwISIAoaUGF5bWVudE9yZGVyRGVs",
            "aXZlcnlGYWlsZWQQ/88CEicKIVBheW1lbnRPcmRlckNoZWNrRmFpbGVkUmVt",
            "b3RlQ2FsbBCA0AISGgoUUGF5bWVudEdpZnRQdXJjaGFzZWQQgdACEh8KGUZv",
            "cm1hdGlvbkhlcm9SZXBlYXRJblRlYW0Q4dcCEiMKHUZvcm1hdGlvblBvc2l0",
            "aW9uUmVwZWF0SW5UZWFtEOLXAhIjCh1Gb3JtYXRpb25IZXJvUmVwZWF0SW5B",
            "bGxUZWFtcxDj1wISJwohRm9ybWF0aW9uUG9zaXRpb25SZXBlYXRJbkFsbFRl",
            "YW1zEOTXAhIZChNGb3JtYXRpb25JbnZhbGlkUG9zEOXXAhIdChdCYXR0bGVG",
            "b3JtYXRpb25Ob3RGb3VuZBDF2AISGgoUQmF0dGxlSGVyb1NraWxsRW1wdHkQ",
            "xtgCEhkKE0JhdHRsZUhlcm9BdHRyRW1wdHkQx9gCEhsKFUJhdHRsZVBhcnNl",
            "VGVhbUZhaWxlZBCp2QISGAoSQmF0dGxlQ3JlYXRlRmFpbGVkEKrZAhIMCgZG",
            "YWlsZWQQ0YYDEg0KB1RpbWVvdXQQ0oYDEhMKDVRva2VuTm90Rm91bmQQ04YD",
            "EhcKEVRva2VuVmVyaWZ5RmFpbGVkENSGAxIUCg5GZWF0dXJlTm90T3BlbhDV",
            "hgMSFwoRV2ViUm91dGVyTm90Rm91bmQQuY4DEhQKDldlYkludGVybmFsRXJy",
            "ELqOAxIWChBXZWJQYXJhbU5vdEZvdW5kELuOAxIbChVXZWJQYXJhbUludmFs",
            "aWRGb3JtYXQQvI4DEhgKEldlYlBhcmFtUmVhZEZhaWxlZBC9jgMSGwoVV2Vi",
            "UGFyYW1JbnZhbGlkTGVuZ3RoEL6OAxIaChRXZWJQYXJhbVZlcmlmeUZhaWxl",
            "ZBC/jgMSGgoUV2ViUGFyYW1NaXNzUmVxdWlyZWQQwI4DEhYKEFdlYlBhcmFt",
            "TWlzc1NpZ24QwY4DEhsKFVdlYlJhdGVMaW1pdEdldEZhaWxlZBDNjgMSGgoU",
            "V2ViUmF0ZUxpbWl0Tm90QWxsb3cQzo4DEhkKE1dlYkludmFsaWRUaW1lc3Rh",
            "bXAQz44DEhkKE1dlYlNpZ25WZXJpZnlGYWlsZWQQ0I4DEhUKD1dlYlNpZ25J",
            "bkZhaWxlZBCtkgMSGgoUV2ViQ3JlYXRlVG9rZW5GYWlsZWQQrpIDEhwKFldl",
            "YkFjY291bnRTdGF0dXNGb3JiaWQQr5IDQhtaGXNlcnZlci9hcGkvcGIvcGJf",
            "cHJvdG9jb2xiBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Protocol.MessageID), typeof(global::Protocol.ErrorCode), }, null, null));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 协议号范围: 0 ~ 65536
  /// </summary>
  public enum MessageID {
    [pbr::OriginalName("None")] None = 0,
    /// <summary>
    /// ----------------------- gate 1 ~ 100 -----------------------
    /// </summary>
    [pbr::OriginalName("Heart")] Heart = 1,
    /// <summary>
    /// 公钥互换
    /// </summary>
    [pbr::OriginalName("SecretSharePubKey")] SecretSharePubKey = 11,
    /// <summary>
    /// 测试密钥
    /// </summary>
    [pbr::OriginalName("SecretShareTest")] SecretShareTest = 12,
    /// <summary>
    /// 登录服务器
    /// </summary>
    [pbr::OriginalName("Login")] Login = 13,
    /// <summary>
    /// ----------------------- account 101 ~ 199 -----------------------
    /// </summary>
    [pbr::OriginalName("AccountList")] AccountList = 101,
    /// <summary>
    /// ----------------------- chat 200 ~ 299 -----------------------
    /// ----------------------- mail 300 ~ 399 -----------------------
    /// ----------------------- battle 400 ~ 479 -----------------------
    /// </summary>
    [pbr::OriginalName("BattleCreate")] BattleCreate = 401,
    /// <summary>
    /// ----------------------- battlerecord 480 ~ 499 -----------------------
    /// </summary>
    [pbr::OriginalName("BattleRecordRead")] BattleRecordRead = 481,
    /// <summary>
    /// 读取战报组
    /// </summary>
    [pbr::OriginalName("BattleRecordReadGroup")] BattleRecordReadGroup = 482,
    /// <summary>
    /// ----------------------- lang 500 ~ 549 -----------------------
    /// </summary>
    [pbr::OriginalName("LangTranslate")] LangTranslate = 501,
    /// <summary>
    /// 检查非法字符
    /// </summary>
    [pbr::OriginalName("LangSensitive")] LangSensitive = 502,
    /// <summary>
    /// ----------------------- union 600 ~ 799 -----------------------
    /// </summary>
    [pbr::OriginalName("UnionCreate")] UnionCreate = 601,
    /// <summary>
    /// 退出联盟（主动）
    /// </summary>
    [pbr::OriginalName("UnionQuit")] UnionQuit = 602,
    /// <summary>
    /// 获取联盟简略信息
    /// </summary>
    [pbr::OriginalName("UnionBrief")] UnionBrief = 603,
    /// <summary>
    /// 获取联盟列表
    /// </summary>
    [pbr::OriginalName("UnionList")] UnionList = 604,
    /// <summary>
    /// 联盟申请
    /// </summary>
    [pbr::OriginalName("UnionJoinApply")] UnionJoinApply = 605,
    /// <summary>
    /// 联盟申请列表
    /// </summary>
    [pbr::OriginalName("UnionJoinApplyList")] UnionJoinApplyList = 606,
    /// <summary>
    /// 联盟申请同意
    /// </summary>
    [pbr::OriginalName("UnionJoinApplyAgree")] UnionJoinApplyAgree = 607,
    /// <summary>
    /// 联盟申请拒绝
    /// </summary>
    [pbr::OriginalName("UnionJoinApplyRefuse")] UnionJoinApplyRefuse = 608,
    /// <summary>
    /// 联盟搜索
    /// </summary>
    [pbr::OriginalName("UnionSearch")] UnionSearch = 609,
    /// <summary>
    /// 联盟一键入盟
    /// </summary>
    [pbr::OriginalName("UnionOneKeyJoin")] UnionOneKeyJoin = 610,
    /// <summary>
    /// 联盟解散
    /// </summary>
    [pbr::OriginalName("UnionDisband")] UnionDisband = 611,
    /// <summary>
    /// 联盟踢出（踢人）
    /// </summary>
    [pbr::OriginalName("UnionKickOut")] UnionKickOut = 612,
    /// <summary>
    /// 联盟编辑宣言
    /// </summary>
    [pbr::OriginalName("UnionEditNotify")] UnionEditNotify = 613,
    /// <summary>
    /// 联盟旗帜修改
    /// </summary>
    [pbr::OriginalName("UnionChangeFlag")] UnionChangeFlag = 614,
    /// <summary>
    /// 联盟改名
    /// </summary>
    [pbr::OriginalName("UnionChangeName")] UnionChangeName = 615,
    /// <summary>
    /// 联盟简称改名
    /// </summary>
    [pbr::OriginalName("UnionChangeShortName")] UnionChangeShortName = 616,
    /// <summary>
    /// 转让盟主
    /// </summary>
    [pbr::OriginalName("UnionTransferLeader")] UnionTransferLeader = 617,
    /// <summary>
    /// 联盟邀请
    /// </summary>
    [pbr::OriginalName("UnionInvite")] UnionInvite = 618,
    /// <summary>
    /// 联盟集结点
    /// </summary>
    [pbr::OriginalName("UnionRallyPoint")] UnionRallyPoint = 619,
    /// <summary>
    /// 联盟邮件发送
    /// </summary>
    [pbr::OriginalName("UnionSendMail")] UnionSendMail = 620,
    /// <summary>
    /// 联盟权限调整
    /// </summary>
    [pbr::OriginalName("UnionChangePermission")] UnionChangePermission = 621,
    /// <summary>
    /// 联盟加入条件修改
    /// </summary>
    [pbr::OriginalName("UnionChangeJoinCondition")] UnionChangeJoinCondition = 622,
    /// <summary>
    /// 联盟科技推荐
    /// </summary>
    [pbr::OriginalName("UnionSetRecommendTech")] UnionSetRecommendTech = 624,
    /// <summary>
    /// 联盟宣战
    /// </summary>
    [pbr::OriginalName("UnionDeclareWar")] UnionDeclareWar = 625,
    /// <summary>
    /// 联盟移除驻军
    /// </summary>
    [pbr::OriginalName("UnionRemoveGarrison")] UnionRemoveGarrison = 626,
    /// <summary>
    /// 联盟放弃城市
    /// </summary>
    [pbr::OriginalName("UnionGiveUpCity")] UnionGiveUpCity = 627,
    /// <summary>
    /// 联盟开放招募
    /// </summary>
    [pbr::OriginalName("UnionOpenRecruit")] UnionOpenRecruit = 628,
    /// <summary>
    /// 联盟日志
    /// </summary>
    [pbr::OriginalName("UnionRecord")] UnionRecord = 629,
    /// <summary>
    /// 联盟获取清除不活跃成员开关状态
    /// </summary>
    [pbr::OriginalName("UnionGetCleanInactiveSwitch")] UnionGetCleanInactiveSwitch = 630,
    /// <summary>
    /// 联盟申请列表变更推送
    /// </summary>
    [pbr::OriginalName("UnionApplyListChangePush")] UnionApplyListChangePush = 631,
    /// <summary>
    /// 联盟成员信息变更推送
    /// </summary>
    [pbr::OriginalName("UnionRoleInfoChangePush")] UnionRoleInfoChangePush = 632,
    /// <summary>
    /// 联盟自由加入
    /// </summary>
    [pbr::OriginalName("UnionFreeJoin")] UnionFreeJoin = 633,
    /// <summary>
    /// 联盟礼物列表
    /// </summary>
    [pbr::OriginalName("UnionGiftList")] UnionGiftList = 634,
    /// <summary>
    /// 联盟清除不活跃成员
    /// </summary>
    [pbr::OriginalName("UnionCleanInactive")] UnionCleanInactive = 635,
    /// <summary>
    /// 联盟清除所有不活跃成员
    /// </summary>
    [pbr::OriginalName("UnionCleanAllInactive")] UnionCleanAllInactive = 636,
    /// <summary>
    /// 联盟礼物领取
    /// </summary>
    [pbr::OriginalName("UnionGiftReceive")] UnionGiftReceive = 637,
    /// <summary>
    /// 联盟礼物一键领取
    /// </summary>
    [pbr::OriginalName("UnionGiftReceiveAll")] UnionGiftReceiveAll = 638,
    /// <summary>
    /// 联盟成员列表
    /// </summary>
    [pbr::OriginalName("UnionMemberList")] UnionMemberList = 639,
    /// <summary>
    /// 联盟职位调整
    /// </summary>
    [pbr::OriginalName("UnionChangePosition")] UnionChangePosition = 640,
    /// <summary>
    /// 联盟成员退出通知（关闭所有联盟界面）
    /// </summary>
    [pbr::OriginalName("UnionMemberOut")] UnionMemberOut = 641,
    /// <summary>
    /// 联盟排行榜
    /// </summary>
    [pbr::OriginalName("UnionRankList")] UnionRankList = 642,
    /// <summary>
    /// 联盟随机简称
    /// </summary>
    [pbr::OriginalName("UnionRandomShortName")] UnionRandomShortName = 643,
    /// <summary>
    /// 联盟校验名称
    /// </summary>
    [pbr::OriginalName("UnionCheckName")] UnionCheckName = 644,
    /// <summary>
    /// 联盟匿名礼物开关
    /// </summary>
    [pbr::OriginalName("UnionAnonymousGiftSwitch")] UnionAnonymousGiftSwitch = 645,
    /// <summary>
    /// 联盟玩家已经加入联盟，关闭创建界面
    /// </summary>
    [pbr::OriginalName("UnionHaveBeenJoin")] UnionHaveBeenJoin = 646,
    /// <summary>
    /// 联盟科技列表
    /// </summary>
    [pbr::OriginalName("UnionTechList")] UnionTechList = 647,
    /// <summary>
    /// 联盟科技研究
    /// </summary>
    [pbr::OriginalName("UnionTechStudy")] UnionTechStudy = 648,
    /// <summary>
    /// 联盟科技自动升级开关
    /// </summary>
    [pbr::OriginalName("UnionTechAutoUpgradeSwitch")] UnionTechAutoUpgradeSwitch = 649,
    /// <summary>
    /// 联盟科技自动升级通知
    /// </summary>
    [pbr::OriginalName("UnionTechAutoUpgradeNotify")] UnionTechAutoUpgradeNotify = 650,
    /// <summary>
    /// 联盟科技捐献
    /// </summary>
    [pbr::OriginalName("UnionTechDonate")] UnionTechDonate = 651,
    /// <summary>
    /// 联盟语言修改
    /// </summary>
    [pbr::OriginalName("UnionChangeLang")] UnionChangeLang = 652,
    /// <summary>
    /// 联盟里程碑列表
    /// </summary>
    [pbr::OriginalName("UnionMileStoneList")] UnionMileStoneList = 653,
    /// <summary>
    /// 联盟里程碑变化推送
    /// </summary>
    [pbr::OriginalName("PushMilestoneChange")] PushMilestoneChange = 654,
    /// <summary>
    /// 联盟里程碑奖励领取
    /// </summary>
    [pbr::OriginalName("UnionMileStoneReward")] UnionMileStoneReward = 655,
    /// <summary>
    /// 联盟科技变化推送
    /// </summary>
    [pbr::OriginalName("PushUnionTechChange")] PushUnionTechChange = 656,
    /// <summary>
    /// 联盟帮助列表
    /// </summary>
    [pbr::OriginalName("UnionHelpList")] UnionHelpList = 657,
    /// <summary>
    /// 联盟请求成员帮助通知
    /// </summary>
    [pbr::OriginalName("PushUnionHelp")] PushUnionHelp = 658,
    /// <summary>
    /// 联盟帮助全部
    /// </summary>
    [pbr::OriginalName("UnionHelpAll")] UnionHelpAll = 659,
    /// <summary>
    /// 联盟帮助提示
    /// </summary>
    [pbr::OriginalName("PushUnionHelpTips")] PushUnionHelpTips = 660,
    /// <summary>
    /// 推送联盟变化信息
    /// </summary>
    [pbr::OriginalName("PushUnionChange")] PushUnionChange = 661,
    /// <summary>
    /// ----------------------- union跨服请求 700 ~ 799 -----------------------
    /// </summary>
    [pbr::OriginalName("CrossUnionRoleArticleChange")] CrossUnionRoleArticleChange = 700,
    /// <summary>
    /// 角色能否离开联盟
    /// </summary>
    [pbr::OriginalName("CrossUnionRoleCanOut")] CrossUnionRoleCanOut = 701,
    /// <summary>
    /// 联盟成员退出
    /// </summary>
    [pbr::OriginalName("CrossUnionMemberOut")] CrossUnionMemberOut = 702,
    /// <summary>
    /// 联盟玩家已经加入联盟，关闭界面
    /// </summary>
    [pbr::OriginalName("CrossUnionHaveBeenJoin")] CrossUnionHaveBeenJoin = 703,
    /// <summary>
    /// 联盟申请列表变更推送
    /// </summary>
    [pbr::OriginalName("CrossUnionApplyListChangePush")] CrossUnionApplyListChangePush = 704,
    /// <summary>
    /// 联盟科技自动升级开关推送
    /// </summary>
    [pbr::OriginalName("CrossUnionTechAutoUpgradePush")] CrossUnionTechAutoUpgradePush = 705,
    /// <summary>
    /// 联盟科技变化推送
    /// </summary>
    [pbr::OriginalName("CrossUnionTechChangePush")] CrossUnionTechChangePush = 706,
    /// <summary>
    /// 联盟变化推送
    /// </summary>
    [pbr::OriginalName("CrossPushUnionChange")] CrossPushUnionChange = 707,
    /// <summary>
    /// 联盟发起帮助
    /// </summary>
    [pbr::OriginalName("CrossUnionInitiateHelp")] CrossUnionInitiateHelp = 708,
    /// <summary>
    /// 通知联盟成员获得帮助
    /// </summary>
    [pbr::OriginalName("CrossUnionNotifyMemberGetHelp")] CrossUnionNotifyMemberGetHelp = 709,
    /// <summary>
    /// 联盟建筑队列完成
    /// </summary>
    [pbr::OriginalName("CrossUnionBuildQueueFinished")] CrossUnionBuildQueueFinished = 710,
    /// <summary>
    /// 联盟指定列车长
    /// </summary>
    [pbr::OriginalName("CrossUnionTradeTrainAppointConductor")] CrossUnionTradeTrainAppointConductor = 711,
    /// <summary>
    /// 联盟召唤列车
    /// </summary>
    [pbr::OriginalName("CrossUnionTradeTrainSummon")] CrossUnionTradeTrainSummon = 712,
    /// <summary>
    /// 联盟邀请vip乘客
    /// </summary>
    [pbr::OriginalName("CrossUnionTradeTrainInvite")] CrossUnionTradeTrainInvite = 713,
    /// <summary>
    /// 联盟帮助变化推送
    /// </summary>
    [pbr::OriginalName("CrossPushUnionHelp")] CrossPushUnionHelp = 714,
    /// <summary>
    /// 联盟召唤列车成功
    /// </summary>
    [pbr::OriginalName("CrossUnionSummonSuccess")] CrossUnionSummonSuccess = 715,
    /// <summary>
    /// 联盟成员加入
    /// </summary>
    [pbr::OriginalName("CrossUnionMemberJoin")] CrossUnionMemberJoin = 716,
    /// <summary>
    /// 联盟免费列车推送
    /// </summary>
    [pbr::OriginalName("CrossUnionPushTradeChange")] CrossUnionPushTradeChange = 717,
    /// <summary>
    /// ----------------------- arena 800 ~ 899 -----------------------
    /// </summary>
    [pbr::OriginalName("NovicePersonInfo")] NovicePersonInfo = 801,
    /// <summary>
    /// 新兵训练营挑战数据
    /// </summary>
    [pbr::OriginalName("NoviceCompInfo")] NoviceCompInfo = 802,
    /// <summary>
    /// 新兵训练营排行榜
    /// </summary>
    [pbr::OriginalName("NoviceRank")] NoviceRank = 803,
    /// <summary>
    /// 新兵训练营发起挑战
    /// </summary>
    [pbr::OriginalName("NoviceChallenge")] NoviceChallenge = 804,
    /// <summary>
    /// 新兵训练营刷新对手
    /// </summary>
    [pbr::OriginalName("NoviceRefreshComp")] NoviceRefreshComp = 805,
    /// <summary>
    /// 新兵训练营购买挑战次数
    /// </summary>
    [pbr::OriginalName("NoviceBuyChallengeTimes")] NoviceBuyChallengeTimes = 806,
    /// <summary>
    /// 新兵训练营领取排名成就
    /// </summary>
    [pbr::OriginalName("NoviceDrawAchieve")] NoviceDrawAchieve = 807,
    /// <summary>
    /// 新兵训练营战报数据
    /// </summary>
    [pbr::OriginalName("NoviceReports")] NoviceReports = 808,
    /// <summary>
    /// 新兵训练营挑战对手数据
    /// </summary>
    [pbr::OriginalName("NoviceSrvCompInfo")] NoviceSrvCompInfo = 821,
    /// <summary>
    /// 新兵训练营排行榜数据
    /// </summary>
    [pbr::OriginalName("NoviceSrvRank")] NoviceSrvRank = 822,
    /// <summary>
    /// 刷新挑战对手
    /// </summary>
    [pbr::OriginalName("NoviceSrvRefreshComp")] NoviceSrvRefreshComp = 823,
    /// <summary>
    /// 发起挑战
    /// </summary>
    [pbr::OriginalName("NoviceSrvChallenge")] NoviceSrvChallenge = 824,
    /// <summary>
    /// 巅峰竞技场个人数据
    /// </summary>
    [pbr::OriginalName("PeakPersonInfo")] PeakPersonInfo = 831,
    /// <summary>
    /// 巅峰竞技场排名数据
    /// </summary>
    [pbr::OriginalName("PeakRank")] PeakRank = 832,
    /// <summary>
    /// 巅峰竞技场指定排名区间数据
    /// </summary>
    [pbr::OriginalName("PeakRankInterval")] PeakRankInterval = 833,
    /// <summary>
    /// 巅峰竞技场发起挑战
    /// </summary>
    [pbr::OriginalName("PeakChallenge")] PeakChallenge = 834,
    /// <summary>
    /// 巅峰竞技场战报数据
    /// </summary>
    [pbr::OriginalName("PeakReports")] PeakReports = 835,
    /// <summary>
    /// 巅峰竞技场排名数据
    /// </summary>
    [pbr::OriginalName("PeakSrvRank")] PeakSrvRank = 842,
    /// <summary>
    /// 巅峰竞技场指定排名区间数据
    /// </summary>
    [pbr::OriginalName("PeakSrvRankInterval")] PeakSrvRankInterval = 843,
    /// <summary>
    /// 巅峰竞技场发起挑战
    /// </summary>
    [pbr::OriginalName("PeakSrvChallenge")] PeakSrvChallenge = 844,
    /// <summary>
    /// 竞技场开启信息
    /// </summary>
    [pbr::OriginalName("ArenaOpenInfos")] ArenaOpenInfos = 891,
    /// <summary>
    /// 竞技场GM开启
    /// </summary>
    [pbr::OriginalName("ArenaGMOPen")] ArenaGmopen = 892,
    /// <summary>
    /// 竞技场GM关闭
    /// </summary>
    [pbr::OriginalName("ArenaGMShut")] ArenaGmshut = 893,
    /// <summary>
    /// 角色 1001 ~ 1099
    /// </summary>
    [pbr::OriginalName("RoleEnter")] RoleEnter = 1001,
    /// <summary>
    /// 角色创建
    /// </summary>
    [pbr::OriginalName("RoleCreate")] RoleCreate = 1002,
    /// <summary>
    /// 推送物品
    /// </summary>
    [pbr::OriginalName("PushRoleArticles")] PushRoleArticles = 1003,
    /// <summary>
    /// 开挂
    /// </summary>
    [pbr::OriginalName("RoleGM")] RoleGm = 1004,
    /// <summary>
    /// 查询本服角色信息
    /// </summary>
    [pbr::OriginalName("RoleQueryLocal")] RoleQueryLocal = 1005,
    /// <summary>
    /// 查询混服角色信息
    /// </summary>
    [pbr::OriginalName("RoleQueryMulti")] RoleQueryMulti = 1006,
    /// <summary>
    /// 推送战斗力变化
    /// </summary>
    [pbr::OriginalName("PushPowerChange")] PushPowerChange = 1007,
    /// <summary>
    /// 角色条件检测
    /// </summary>
    [pbr::OriginalName("RoleCheckConditions")] RoleCheckConditions = 1008,
    /// <summary>
    /// 推送物品（恭喜获得）
    /// </summary>
    [pbr::OriginalName("PushArticle")] PushArticle = 1009,
    /// <summary>
    /// 英雄 1100 ~ 1199
    /// </summary>
    [pbr::OriginalName("HeroUpgradeLevel")] HeroUpgradeLevel = 1100,
    /// <summary>
    /// 英雄升星请求
    /// </summary>
    [pbr::OriginalName("HeroUpgradeStar")] HeroUpgradeStar = 1101,
    /// <summary>
    /// 英雄技能升级
    /// </summary>
    [pbr::OriginalName("HeroUpgradeSkill")] HeroUpgradeSkill = 1102,
    /// <summary>
    /// 英雄空投，回收显示请求
    /// </summary>
    [pbr::OriginalName("HeroMapShow")] HeroMapShow = 1103,
    /// <summary>
    /// 英雄荣誉升级请求
    /// </summary>
    [pbr::OriginalName("HeroUpgradeHonorLevel")] HeroUpgradeHonorLevel = 1104,
    /// <summary>
    /// 合成英雄请求
    /// </summary>
    [pbr::OriginalName("HeroSynthetic")] HeroSynthetic = 1105,
    /// <summary>
    /// 查看英雄属性组成
    /// </summary>
    [pbr::OriginalName("HeroQueryAttrBuild")] HeroQueryAttrBuild = 1151,
    /// <summary>
    /// 装备 1200 ~ 1299 
    /// </summary>
    [pbr::OriginalName("EquipTakeOn")] EquipTakeOn = 1200,
    /// <summary>
    /// 装备脱下
    /// </summary>
    [pbr::OriginalName("EquipTakeOff")] EquipTakeOff = 1201,
    /// <summary>
    /// 装备升级
    /// </summary>
    [pbr::OriginalName("EquipLevelUp")] EquipLevelUp = 1202,
    /// <summary>
    /// 装备晋升
    /// </summary>
    [pbr::OriginalName("EquipPromotion")] EquipPromotion = 1203,
    /// <summary>
    /// 装备生产
    /// </summary>
    [pbr::OriginalName("EquipProduce")] EquipProduce = 1205,
    /// <summary>
    /// 装备分解
    /// </summary>
    [pbr::OriginalName("EquipResolve")] EquipResolve = 1207,
    /// <summary>
    /// 装备材料合成
    /// </summary>
    [pbr::OriginalName("EquipMaterialSynthesis")] EquipMaterialSynthesis = 1208,
    /// <summary>
    /// 装备材料分解
    /// </summary>
    [pbr::OriginalName("EquipMaterialResolve")] EquipMaterialResolve = 1209,
    /// <summary>
    /// 道具 1300 ~ 1399
    /// </summary>
    [pbr::OriginalName("ItemUse")] ItemUse = 1300,
    /// <summary>
    /// 购买道具
    /// </summary>
    [pbr::OriginalName("ItemBuy")] ItemBuy = 1301,
    /// <summary>
    /// 招募 1400 ~ 1499
    /// </summary>
    [pbr::OriginalName("RecruitPoolsInfo")] RecruitPoolsInfo = 1400,
    /// <summary>
    /// 招募池变动(新增或修改)
    /// </summary>
    [pbr::OriginalName("RecruitPoolChange")] RecruitPoolChange = 1401,
    /// <summary>
    /// 招募池删除
    /// </summary>
    [pbr::OriginalName("RecruitPoolDel")] RecruitPoolDel = 1402,
    /// <summary>
    /// 招募
    /// </summary>
    [pbr::OriginalName("Recruit")] Recruit = 1403,
    /// <summary>
    /// 设心愿英雄
    /// </summary>
    [pbr::OriginalName("RecruitSetWish")] RecruitSetWish = 1404,
    /// <summary>
    /// 领取心愿英雄
    /// </summary>
    [pbr::OriginalName("RecruitDrawWish")] RecruitDrawWish = 1405,
    /// <summary>
    /// 更新已读预告时间戳
    /// </summary>
    [pbr::OriginalName("RecruitUpdatePreview")] RecruitUpdatePreview = 1406,
    /// <summary>
    /// 建筑 1600  ~ 1699
    /// </summary>
    [pbr::OriginalName("BuildCreate")] BuildCreate = 1600,
    /// <summary>
    /// 建筑升级
    /// </summary>
    [pbr::OriginalName("BuildUpgrade")] BuildUpgrade = 1601,
    /// <summary>
    /// 建筑移动
    /// </summary>
    [pbr::OriginalName("BuildMove")] BuildMove = 1602,
    /// <summary>
    /// 队列完成
    /// </summary>
    [pbr::OriginalName("BuildQueueFinish")] BuildQueueFinish = 1603,
    /// <summary>
    /// 队列加速
    /// </summary>
    [pbr::OriginalName("BuildQueueAccelerate")] BuildQueueAccelerate = 1604,
    /// <summary>
    /// 队列帮助
    /// </summary>
    [pbr::OriginalName("BuildQueueHelp")] BuildQueueHelp = 1606,
    /// <summary>
    /// 推送建筑帮助加速
    /// </summary>
    [pbr::OriginalName("PushBuildHelpAccelerate")] PushBuildHelpAccelerate = 1607,
    /// <summary>
    /// 推送建筑
    /// </summary>
    [pbr::OriginalName("PushBuild")] PushBuild = 1608,
    /// <summary>
    /// 士兵协议 1700  ~ 1749
    /// </summary>
    [pbr::OriginalName("SoldierOp")] SoldierOp = 1700,
    /// <summary>
    /// 操作士兵(治疗)
    /// </summary>
    [pbr::OriginalName("SoldierTreatment")] SoldierTreatment = 1701,
    /// <summary>
    /// 士兵变更
    /// </summary>
    [pbr::OriginalName("PushSoldierChange")] PushSoldierChange = 1702,
    /// <summary>
    /// 医院变更
    /// </summary>
    [pbr::OriginalName("PushHospitalsChange")] PushHospitalsChange = 1703,
    /// <summary>
    /// 幸存者协议 1750 ~ 1799
    /// </summary>
    [pbr::OriginalName("SurvivorSummon")] SurvivorSummon = 1750,
    /// <summary>
    /// 幸存者升星
    /// </summary>
    [pbr::OriginalName("SurvivorStarUp")] SurvivorStarUp = 1751,
    /// <summary>
    /// 幸存者派遣
    /// </summary>
    [pbr::OriginalName("SurvivorDispatch")] SurvivorDispatch = 1752,
    /// <summary>
    /// 幸存者快速派遣
    /// </summary>
    [pbr::OriginalName("SurvivorFastDispatch")] SurvivorFastDispatch = 1753,
    /// <summary>
    /// 工人协议 1800 ~ 1899
    /// </summary>
    [pbr::OriginalName("WorkerRenting")] WorkerRenting = 1800,
    /// <summary>
    /// 工人解锁
    /// </summary>
    [pbr::OriginalName("PushWorkerLock")] PushWorkerLock = 1801,
    /// <summary>
    /// 个人科技协议 1900 ~ 1999
    /// </summary>
    [pbr::OriginalName("TechStudy")] TechStudy = 1900,
    /// <summary>
    /// 推送科技变化
    /// </summary>
    [pbr::OriginalName("PushTechChange")] PushTechChange = 1901,
    /// <summary>
    /// 无人机 2000 ~ 2099
    /// </summary>
    [pbr::OriginalName("UavUpgrade")] UavUpgrade = 2000,
    /// <summary>
    ///无人机换装
    /// </summary>
    [pbr::OriginalName("UavUseSkin")] UavUseSkin = 2001,
    /// <summary>
    ///无人机装备组件
    /// </summary>
    [pbr::OriginalName("UavEquipComponent")] UavEquipComponent = 2002,
    /// <summary>
    ///无人机一键装备
    /// </summary>
    [pbr::OriginalName("UavOneClickEquipComponent")] UavOneClickEquipComponent = 2003,
    /// <summary>
    ///无人机一键合成
    /// </summary>
    [pbr::OriginalName("UavOneClickSynthesis")] UavOneClickSynthesis = 2004,
    /// <summary>
    ///无人机研究
    /// </summary>
    [pbr::OriginalName("UavResearch")] UavResearch = 2005,
    /// <summary>
    /// 商店 3000 ~ 3049
    /// </summary>
    [pbr::OriginalName("StoreLoad")] StoreLoad = 3000,
    /// <summary>
    /// 商店购买物品
    /// </summary>
    [pbr::OriginalName("StoreBuy")] StoreBuy = 3001,
    /// <summary>
    /// 商城 3100 ~ 3199
    /// </summary>
    [pbr::OriginalName("ShopLoad")] ShopLoad = 3100,
    /// <summary>
    /// 商城每日特惠加载
    /// </summary>
    [pbr::OriginalName("ShopDailyDealLoad")] ShopDailyDealLoad = 3101,
    /// <summary>
    /// 推送商城每日特惠信息
    /// </summary>
    [pbr::OriginalName("PushShopDailyDeal")] PushShopDailyDeal = 3102,
    /// <summary>
    /// 商城每日特惠切换
    /// </summary>
    [pbr::OriginalName("ShopDailyDealSwitch")] ShopDailyDealSwitch = 3103,
    /// <summary>
    /// 商城每日特惠领取免费奖励
    /// </summary>
    [pbr::OriginalName("ShopDailyDealClaimFree")] ShopDailyDealClaimFree = 3104,
    /// <summary>
    /// 商城每日必买加载
    /// </summary>
    [pbr::OriginalName("ShopDailyMustHaveLoad")] ShopDailyMustHaveLoad = 3106,
    /// <summary>
    /// 商城每日必买领取积分奖励
    /// </summary>
    [pbr::OriginalName("ShopDailyMustHaveClaim")] ShopDailyMustHaveClaim = 3107,
    /// <summary>
    /// 商城每周特惠加载
    /// </summary>
    [pbr::OriginalName("ShopWeekDealLoad")] ShopWeekDealLoad = 3111,
    /// <summary>
    /// 商城超级月卡加载
    /// </summary>
    [pbr::OriginalName("ShopMonthCardLoad")] ShopMonthCardLoad = 3116,
    /// <summary>
    /// 商城超级月卡领取今日奖励
    /// </summary>
    [pbr::OriginalName("ShopMonthCardClaim")] ShopMonthCardClaim = 3117,
    /// <summary>
    /// 商城周卡加载
    /// </summary>
    [pbr::OriginalName("ShopWeekCardLoad")] ShopWeekCardLoad = 3121,
    /// <summary>
    /// 商城周卡领取免费奖励
    /// </summary>
    [pbr::OriginalName("ShopWeekCardClaimFree")] ShopWeekCardClaimFree = 3122,
    /// <summary>
    /// 商城周卡领取今日奖励
    /// </summary>
    [pbr::OriginalName("ShopWeekCardClaimToday")] ShopWeekCardClaimToday = 3123,
    /// <summary>
    /// 商城礼包商城加载
    /// </summary>
    [pbr::OriginalName("ShopGiftPackMallLoad")] ShopGiftPackMallLoad = 3126,
    /// <summary>
    /// 商城黎明基金加载
    /// </summary>
    [pbr::OriginalName("ShopDawnFundLoad")] ShopDawnFundLoad = 3131,
    /// <summary>
    /// 商城黎明基金领取奖励
    /// </summary>
    [pbr::OriginalName("ShopDawnFundClaim")] ShopDawnFundClaim = 3132,
    /// <summary>
    /// 商城钻石直购加载
    /// </summary>
    [pbr::OriginalName("ShopDiamondLoad")] ShopDiamondLoad = 3136,
    /// <summary>
    /// 礼包链最新礼包
    /// </summary>
    [pbr::OriginalName("ShopGetWay")] ShopGetWay = 3199,
    /// <summary>
    /// 充值 3200 ~ 3299
    /// </summary>
    [pbr::OriginalName("PaymentReward")] PaymentReward = 3201,
    /// <summary>
    /// 充值订单完成
    /// </summary>
    [pbr::OriginalName("PushPayment")] PushPayment = 3202,
    /// <summary>
    /// 充值下单前业务检查
    /// </summary>
    [pbr::OriginalName("PaymentOrderBusinessRules")] PaymentOrderBusinessRules = 3203,
    /// <summary>
    /// 特权变更
    /// </summary>
    [pbr::OriginalName("PushPrivilege")] PushPrivilege = 3204,
    /// <summary>
    /// 战斗 3300 ~ 3399
    /// </summary>
    [pbr::OriginalName("TeamModify")] TeamModify = 3301,
    /// <summary>
    /// 阵容查询
    /// </summary>
    [pbr::OriginalName("TeamQuery")] TeamQuery = 3302,
    /// <summary>
    /// 战斗调试
    /// </summary>
    [pbr::OriginalName("BattleDebug")] BattleDebug = 3303,
    /// <summary>
    /// 防守队伍修改
    /// </summary>
    [pbr::OriginalName("TeamDefendModify")] TeamDefendModify = 3304,
    /// <summary>
    /// 通过阵容创建战斗
    /// </summary>
    [pbr::OriginalName("BattleCreateByFormation")] BattleCreateByFormation = 3305,
    /// <summary>
    /// 战斗队伍查询
    /// </summary>
    [pbr::OriginalName("BattleTeamQuery")] BattleTeamQuery = 3306,
    /// <summary>
    /// 队伍状态变更
    /// </summary>
    [pbr::OriginalName("PushTeamStatus")] PushTeamStatus = 3307,
    /// <summary>
    /// 推送布阵队伍变更
    /// </summary>
    [pbr::OriginalName("PushFormationTeam")] PushFormationTeam = 3308,
    /// <summary>
    /// 关卡 3400 ~ 3449
    /// </summary>
    [pbr::OriginalName("DungeonLoad")] DungeonLoad = 3401,
    /// <summary>
    /// 关卡战斗
    /// </summary>
    [pbr::OriginalName("DungeonFight")] DungeonFight = 3402,
    /// <summary>
    /// 关卡结算
    /// </summary>
    [pbr::OriginalName("DungeonSettle")] DungeonSettle = 3403,
    /// <summary>
    /// 关卡领取宝箱奖励
    /// </summary>
    [pbr::OriginalName("DungeonClaimBox")] DungeonClaimBox = 3404,
    /// <summary>
    /// 关卡挂机奖励
    /// </summary>
    [pbr::OriginalName("DungeonAccumulatedRewards")] DungeonAccumulatedRewards = 3405,
    /// <summary>
    /// 任务 3450 ~ 3499
    /// </summary>
    [pbr::OriginalName("TaskList")] TaskList = 3450,
    /// <summary>
    /// 任务变更推送
    /// </summary>
    [pbr::OriginalName("TaskChangePush")] TaskChangePush = 3451,
    /// <summary>
    /// 任务领取
    /// </summary>
    [pbr::OriginalName("TaskReceive")] TaskReceive = 3452,
    /// <summary>
    /// 任务领取积分奖励
    /// </summary>
    [pbr::OriginalName("TaskReceiveScoreRewards")] TaskReceiveScoreRewards = 3453,
    /// <summary>
    /// 内城地图 3500 ~ 3549
    /// </summary>
    [pbr::OriginalName("InnerCityOccupyGrid")] InnerCityOccupyGrid = 3501,
    /// <summary>
    /// 城内地图解锁区域
    /// </summary>
    [pbr::OriginalName("InnerCityUnlockRegion")] InnerCityUnlockRegion = 3502,
    /// <summary>
    /// vip 3550 ~ 3599
    /// </summary>
    [pbr::OriginalName("VipReceiveDailyPoints")] VipReceiveDailyPoints = 3550,
    /// <summary>
    /// VIP等级每日免费礼包
    /// </summary>
    [pbr::OriginalName("VipReceiveDailyGift")] VipReceiveDailyGift = 3551,
    /// <summary>
    /// 推送vip信息变化
    /// </summary>
    [pbr::OriginalName("PushVipChange")] PushVipChange = 3552,
    /// <summary>
    /// rank 3600 ~ 3650
    /// </summary>
    [pbr::OriginalName("RankList")] RankList = 3601,
    /// <summary>
    /// 城际贸易 trade 3700  ~ 3799
    /// </summary>
    [pbr::OriginalName("PushTradeChange")] PushTradeChange = 3700,
    /// <summary>
    /// 贸易货车分享
    /// </summary>
    [pbr::OriginalName("TradeShare")] TradeShare = 3701,
    /// <summary>
    /// 贸易查看货车、火车、月台
    /// </summary>
    [pbr::OriginalName("TradeLookUp")] TradeLookUp = 3702,
    /// <summary>
    /// 贸易请求货车信息
    /// </summary>
    [pbr::OriginalName("TradeCargoTransportList")] TradeCargoTransportList = 3703,
    /// <summary>
    /// 贸易货车历史记录
    /// </summary>
    [pbr::OriginalName("TradeVanRecordList")] TradeVanRecordList = 3705,
    /// <summary>
    /// 贸易货车详情
    /// </summary>
    [pbr::OriginalName("TradeVanDetail")] TradeVanDetail = 3706,
    /// <summary>
    /// 贸易刷新货车
    /// </summary>
    [pbr::OriginalName("TradeVanRefresh")] TradeVanRefresh = 3707,
    /// <summary>
    /// 贸易货车出发
    /// </summary>
    [pbr::OriginalName("TradeVanDepart")] TradeVanDepart = 3708,
    /// <summary>
    /// 贸易货车出发（那个加号）
    /// </summary>
    [pbr::OriginalName("TradeVanSetOut")] TradeVanSetOut = 3709,
    /// <summary>
    /// 贸易到达后领取奖励
    /// </summary>
    [pbr::OriginalName("TradeVanReceiveReward")] TradeVanReceiveReward = 3710,
    /// <summary>
    /// 贸易掠夺货物
    /// </summary>
    [pbr::OriginalName("TradeVanRob")] TradeVanRob = 3711,
    /// <summary>
    /// 贸易通缉玩家
    /// </summary>
    [pbr::OriginalName("TradeVanWanted")] TradeVanWanted = 3712,
    /// <summary>
    /// 贸易收藏货车
    /// </summary>
    [pbr::OriginalName("TradeVanCollect")] TradeVanCollect = 3713,
    /// <summary>
    /// 贸易指定列车长
    /// </summary>
    [pbr::OriginalName("TradeTrainAppointConductor")] TradeTrainAppointConductor = 3714,
    /// <summary>
    /// 贸易刷新火车货物
    /// </summary>
    [pbr::OriginalName("TradeTrainRefreshGoods")] TradeTrainRefreshGoods = 3716,
    /// <summary>
    /// 贸易排队上车
    /// </summary>
    [pbr::OriginalName("TradeTrainLineUp")] TradeTrainLineUp = 3717,
    /// <summary>
    /// 贸易获取火车详细信息
    /// </summary>
    [pbr::OriginalName("TradeTrainDetail")] TradeTrainDetail = 3718,
    /// <summary>
    /// 贸易乘车感谢
    /// </summary>
    [pbr::OriginalName("TradeTrainThanks")] TradeTrainThanks = 3719,
    /// <summary>
    /// 贸易感谢列表
    /// </summary>
    [pbr::OriginalName("TradeTrainThanksList")] TradeTrainThanksList = 3720,
    /// <summary>
    /// 贸易邀请vip乘客
    /// </summary>
    [pbr::OriginalName("TradeTrainInvite")] TradeTrainInvite = 3721,
    /// <summary>
    /// 贸易调整货车布阵
    /// </summary>
    [pbr::OriginalName("TradeTrainFormation")] TradeTrainFormation = 3722,
    /// <summary>
    /// 贸易列调整货车布阵顺序
    /// </summary>
    [pbr::OriginalName("TradeTrainFormationOrder")] TradeTrainFormationOrder = 3723,
    /// <summary>
    /// 贸易选择vip乘客奖励
    /// </summary>
    [pbr::OriginalName("TradeTrainSelectVipPassengerReward")] TradeTrainSelectVipPassengerReward = 3724,
    /// <summary>
    /// 贸易守护天使点赞
    /// </summary>
    [pbr::OriginalName("TradeTrainGuardAngelLike")] TradeTrainGuardAngelLike = 3725,
    /// <summary>
    /// 贸易同意邀请
    /// </summary>
    [pbr::OriginalName("TradeTrainAgreeInvite")] TradeTrainAgreeInvite = 3726,
    /// <summary>
    /// 贸易领取感谢奖励
    /// </summary>
    [pbr::OriginalName("TradeReceiveThanksReward")] TradeReceiveThanksReward = 3727,
    /// <summary>
    /// 贸易开始战斗
    /// </summary>
    [pbr::OriginalName("TradeTrainStartFight")] TradeTrainStartFight = 3728,
    /// <summary>
    /// 贸易列车获取战斗记录
    /// </summary>
    [pbr::OriginalName("TradeTrainFightRecordList")] TradeTrainFightRecordList = 3729,
    /// <summary>
    /// 城际贸易 trade 3780  ~ 3799
    /// </summary>
    [pbr::OriginalName("TradeG2GTrainStartFight")] TradeG2GtrainStartFight = 3780,
    /// <summary>
    /// 贸易货车战斗
    /// </summary>
    [pbr::OriginalName("TradeG2GVanFight")] TradeG2GvanFight = 3781,
    /// <summary>
    /// 贸易货车掠夺日志
    /// </summary>
    [pbr::OriginalName("TradeG2GRobLog")] TradeG2GrobLog = 3782,
    /// <summary>
    /// 活动 4000 ~ 4999
    /// 活动基础
    /// </summary>
    [pbr::OriginalName("ActivityOpenInfos")] ActivityOpenInfos = 4001,
    /// <summary>
    /// 活动开启信息变更推送
    /// </summary>
    [pbr::OriginalName("PushActivityChange")] PushActivityChange = 4002,
    /// <summary>
    /// 活动删除推送
    /// </summary>
    [pbr::OriginalName("PushActivityDel")] PushActivityDel = 4003,
    /// <summary>
    /// 活动配置
    /// </summary>
    [pbr::OriginalName("ActivityConfig")] ActivityConfig = 4004,
    /// <summary>
    /// 活动数据
    /// </summary>
    [pbr::OriginalName("ActivityData")] ActivityData = 4005,
    /// <summary>
    /// 活动通用领奖
    /// </summary>
    [pbr::OriginalName("ActivityDraw")] ActivityDraw = 4006,
    /// <summary>
    /// 活动通用购买
    /// </summary>
    [pbr::OriginalName("ActivityBuy")] ActivityBuy = 4007,
    /// <summary>
    /// 英雄升星
    /// </summary>
    [pbr::OriginalName("PushActivityHeroStarConfig")] PushActivityHeroStarConfig = 4051,
    /// <summary>
    /// 活动英雄升星数据推送
    /// </summary>
    [pbr::OriginalName("PushActivityHeroStarData")] PushActivityHeroStarData = 4052,
    /// <summary>
    /// 累充
    /// </summary>
    [pbr::OriginalName("PushActivityRechargeConfig")] PushActivityRechargeConfig = 4061,
    /// <summary>
    /// 活动累充数据据推送
    /// </summary>
    [pbr::OriginalName("PushActivityRechargeData")] PushActivityRechargeData = 4062,
    /// <summary>
    /// 战令
    /// </summary>
    [pbr::OriginalName("PushActivityBattlePassConfig")] PushActivityBattlePassConfig = 4071,
    /// <summary>
    /// 活动战令数据推送
    /// </summary>
    [pbr::OriginalName("PushActivityBattlePassData")] PushActivityBattlePassData = 4072,
    /// <summary>
    /// 活动战令全量任务
    /// </summary>
    [pbr::OriginalName("ActivityBattlePassTaskList")] ActivityBattlePassTaskList = 4073,
    /// <summary>
    /// 活动战令领取任务
    /// </summary>
    [pbr::OriginalName("ActivityBattlePassDrawTask")] ActivityBattlePassDrawTask = 4074,
    /// <summary>
    /// 活动战令变动推送
    /// </summary>
    [pbr::OriginalName("PushActivityBattlePassTaskChange")] PushActivityBattlePassTaskChange = 4075,
    /// <summary>
    /// 希望灯火(战力)
    /// </summary>
    [pbr::OriginalName("PushActivityPowerConfig")] PushActivityPowerConfig = 4081,
    /// <summary>
    /// 活动希望灯火数据推送
    /// </summary>
    [pbr::OriginalName("PushActivityPowerData")] PushActivityPowerData = 4082,
    /// <summary>
    /// 荣耀远征 5000 ~ 5049
    /// </summary>
    [pbr::OriginalName("TowerLoad")] TowerLoad = 5001,
    /// <summary>
    /// 荣耀远征选择章节
    /// </summary>
    [pbr::OriginalName("TowerChoose")] TowerChoose = 5002,
    /// <summary>
    /// 荣耀远征战斗
    /// </summary>
    [pbr::OriginalName("TowerFight")] TowerFight = 5003,
    /// <summary>
    /// ----------------------- SDK 40000 ~ 41999 -----------------------
    /// 充值 40000 ~ 40049
    /// </summary>
    [pbr::OriginalName("PaymentOrder")] PaymentOrder = 40001,
    /// <summary>
    /// ----------------------- map 42000 ~ 46999 -----------------------
    /// 主城 42000 ~ 42199
    /// </summary>
    [pbr::OriginalName("TownSrvGm")] TownSrvGm = 42000,
    /// <summary>
    /// 城市服务器创建
    /// </summary>
    [pbr::OriginalName("TownSrvCreate")] TownSrvCreate = 42001,
    /// <summary>
    /// 镜头 42200 ~ 42299
    /// </summary>
    [pbr::OriginalName("CameraInit")] CameraInit = 42200,
    /// <summary>
    /// 镜头移除
    /// </summary>
    [pbr::OriginalName("CameraRemove")] CameraRemove = 42201,
    /// <summary>
    /// 镜头移动
    /// </summary>
    [pbr::OriginalName("CameraMove")] CameraMove = 42202,
    /// <summary>
    /// monster 42300 ~ 42399
    /// </summary>
    [pbr::OriginalName("MonsterTips")] MonsterTips = 42300,
    /// <summary>
    /// 邮件 47000 ~ 47099
    /// </summary>
    [pbr::OriginalName("MailLoad")] MailLoad = 47001,
    /// <summary>
    /// 邮件发送
    /// </summary>
    [pbr::OriginalName("MailSend")] MailSend = 47002,
    /// <summary>
    /// 邮件详情
    /// </summary>
    [pbr::OriginalName("MailDetail")] MailDetail = 47003,
    /// <summary>
    /// 邮件阅读
    /// </summary>
    [pbr::OriginalName("MailRead")] MailRead = 47004,
    /// <summary>
    /// 邮件领取附件
    /// </summary>
    [pbr::OriginalName("MailClaim")] MailClaim = 47005,
    /// <summary>
    /// 邮件删除
    /// </summary>
    [pbr::OriginalName("MailDelete")] MailDelete = 47006,
    /// <summary>
    /// 邮件收藏
    /// </summary>
    [pbr::OriginalName("MailFavorite")] MailFavorite = 47007,
    /// <summary>
    /// 邮件取消收藏
    /// </summary>
    [pbr::OriginalName("MailUnfavorite")] MailUnfavorite = 47008,
    /// <summary>
    /// 邮件点赞
    /// </summary>
    [pbr::OriginalName("MailLike")] MailLike = 47009,
    /// <summary>
    /// 邮件推送
    /// </summary>
    [pbr::OriginalName("PushMail")] PushMail = 47051,
  }

  /// <summary>
  /// 错误码范围规划：
  /// 1000 ~ 9999: 客户端
  /// 10000 ~ 59999: 服务端
  /// 60000 ~ 99999: 客户端
  /// 10w ~ : 策划
  /// </summary>
  public enum ErrorCode {
    /// <summary>
    /// 成功
    /// </summary>
    [pbr::OriginalName("Success")] Success = 0,
    /// <summary>
    /// 成功且不保存数据
    /// </summary>
    [pbr::OriginalName("SuccessWithoutSave")] SuccessWithoutSave = 1,
    /// <summary>
    /// ----------------------- common 10000 ~ 10999
    /// </summary>
    [pbr::OriginalName("ProtoUnarshalFailed")] ProtoUnarshalFailed = 10001,
    /// <summary>
    /// 协议编码失败
    /// </summary>
    [pbr::OriginalName("ProtoMarshalFailed")] ProtoMarshalFailed = 10002,
    /// <summary>
    /// 协议未找到
    /// </summary>
    [pbr::OriginalName("ProtocolNotFound")] ProtocolNotFound = 10003,
    /// <summary>
    /// 远程调用失败
    /// </summary>
    [pbr::OriginalName("RemoteCallFailed")] RemoteCallFailed = 10004,
    /// <summary>
    /// 配置未找到
    /// </summary>
    [pbr::OriginalName("ConfigNotFound")] ConfigNotFound = 10005,
    /// <summary>
    /// id为0
    /// </summary>
    [pbr::OriginalName("IDZero")] Idzero = 10006,
    /// <summary>
    /// 仅在开发环境下可用
    /// </summary>
    [pbr::OriginalName("DevelopOnly")] DevelopOnly = 10007,
    /// <summary>
    /// 推送失败
    /// </summary>
    [pbr::OriginalName("PushFailed")] PushFailed = 10008,
    /// <summary>
    /// 参数错误
    /// </summary>
    [pbr::OriginalName("ParamError")] ParamError = 10011,
    /// <summary>
    /// 参数错误2
    /// </summary>
    [pbr::OriginalName("ParamError2")] ParamError2 = 10012,
    /// <summary>
    /// 参数错误3
    /// </summary>
    [pbr::OriginalName("ParamError3")] ParamError3 = 10013,
    /// <summary>
    /// 参数错误4
    /// </summary>
    [pbr::OriginalName("ParamError4")] ParamError4 = 10014,
    /// <summary>
    /// 参数错误5
    /// </summary>
    [pbr::OriginalName("ParamError5")] ParamError5 = 10015,
    /// <summary>
    /// 参数错误6
    /// </summary>
    [pbr::OriginalName("ParamError6")] ParamError6 = 10016,
    /// <summary>
    /// 参数错误7
    /// </summary>
    [pbr::OriginalName("ParamError7")] ParamError7 = 10017,
    /// <summary>
    /// 参数错误8
    /// </summary>
    [pbr::OriginalName("ParamError8")] ParamError8 = 10018,
    /// <summary>
    /// 参数错误9
    /// </summary>
    [pbr::OriginalName("ParamError9")] ParamError9 = 10019,
    /// <summary>
    /// 参数错误10
    /// </summary>
    [pbr::OriginalName("ParamError10")] ParamError10 = 10020,
    /// <summary>
    /// 配置奖励错误
    /// </summary>
    [pbr::OriginalName("ConfigRewardError")] ConfigRewardError = 10021,
    /// <summary>
    /// 配置消耗错误
    /// </summary>
    [pbr::OriginalName("ConfigConsumeError")] ConfigConsumeError = 10022,
    /// <summary>
    /// 物品变更失败
    /// </summary>
    [pbr::OriginalName("ChangeShowFailed")] ChangeShowFailed = 10023,
    /// <summary>
    /// 物品变更失败2
    /// </summary>
    [pbr::OriginalName("ChangeShowFailed2")] ChangeShowFailed2 = 10024,
    /// <summary>
    /// 英雄不存在
    /// </summary>
    [pbr::OriginalName("UnlockHeroNotFound")] UnlockHeroNotFound = 10031,
    /// <summary>
    /// 英雄等级不足
    /// </summary>
    [pbr::OriginalName("UnlockHeroLevelLess")] UnlockHeroLevelLess = 10032,
    /// <summary>
    /// 英雄星级不足
    /// </summary>
    [pbr::OriginalName("UnlockHeroStarLess")] UnlockHeroStarLess = 10033,
    /// <summary>
    /// 等级不足
    /// </summary>
    [pbr::OriginalName("LevelNotEnough")] LevelNotEnough = 10034,
    /// <summary>
    /// 星级不足
    /// </summary>
    [pbr::OriginalName("StarNotEnough")] StarNotEnough = 10035,
    /// <summary>
    /// 建筑等级不足
    /// </summary>
    [pbr::OriginalName("BuildLevelLess")] BuildLevelLess = 10036,
    /// <summary>
    /// 数据库创建失败
    /// </summary>
    [pbr::OriginalName("DBCreateFailed")] DbcreateFailed = 10041,
    /// <summary>
    /// 数据库查询失败
    /// </summary>
    [pbr::OriginalName("DBQueryFailed")] DbqueryFailed = 10042,
    /// <summary>
    /// 数据库更新失败
    /// </summary>
    [pbr::OriginalName("DBUpdateFailed")] DbupdateFailed = 10043,
    /// <summary>
    /// 编码失败
    /// </summary>
    [pbr::OriginalName("EncodeFailed")] EncodeFailed = 10044,
    /// <summary>
    /// 解码失败
    /// </summary>
    [pbr::OriginalName("DecodeFailed")] DecodeFailed = 10045,
    /// <summary>
    /// 加密失败
    /// </summary>
    [pbr::OriginalName("EncryptFailed")] EncryptFailed = 10046,
    /// <summary>
    /// 解密失败
    /// </summary>
    [pbr::OriginalName("DecryptFailed")] DecryptFailed = 10047,
    /// <summary>
    /// 奖励已领取
    /// </summary>
    [pbr::OriginalName("RewardClaimed")] RewardClaimed = 10048,
    /// <summary>
    /// 奖励未激活
    /// </summary>
    [pbr::OriginalName("RewardNotActive")] RewardNotActive = 10049,
    /// <summary>
    /// 不满足获取条件,无法领取奖励
    /// </summary>
    [pbr::OriginalName("RewardNotMatchCondition")] RewardNotMatchCondition = 10050,
    /// <summary>
    /// 没有奖励可以领取
    /// </summary>
    [pbr::OriginalName("RewardsClaimEmpty")] RewardsClaimEmpty = 10051,
    /// <summary>
    /// 含有非法字符
    /// </summary>
    [pbr::OriginalName("InvalidWord")] InvalidWord = 10052,
    /// <summary>
    /// 次数不足
    /// </summary>
    [pbr::OriginalName("TimesOut")] TimesOut = 10053,
    /// <summary>
    /// 购买次数不足
    /// </summary>
    [pbr::OriginalName("BuyTimesOut")] BuyTimesOut = 10054,
    /// <summary>
    /// 权限未激活
    /// </summary>
    [pbr::OriginalName("PrivilegeNotActive")] PrivilegeNotActive = 10055,
    /// <summary>
    /// 权限已过期
    /// </summary>
    [pbr::OriginalName("PrivilegeExpired")] PrivilegeExpired = 10056,
    /// <summary>
    /// 不支持的类型
    /// </summary>
    [pbr::OriginalName("UnSupportType")] UnSupportType = 10057,
    /// <summary>
    /// 系统繁忙
    /// </summary>
    [pbr::OriginalName("SystemBusy")] SystemBusy = 10058,
    /// <summary>
    /// 队伍未解锁
    /// </summary>
    [pbr::OriginalName("TeamNotUnlocked")] TeamNotUnlocked = 10059,
    /// <summary>
    /// 配置错误
    /// </summary>
    [pbr::OriginalName("ConfigError")] ConfigError = 10061,
    /// <summary>
    /// 配置错误2
    /// </summary>
    [pbr::OriginalName("ConfigErro2")] ConfigErro2 = 10062,
    /// <summary>
    /// 配置错误3
    /// </summary>
    [pbr::OriginalName("ConfigErro3")] ConfigErro3 = 10063,
    /// <summary>
    /// 配置错误4
    /// </summary>
    [pbr::OriginalName("ConfigErro4")] ConfigErro4 = 10064,
    /// <summary>
    /// 配置错误5
    /// </summary>
    [pbr::OriginalName("ConfigErro5")] ConfigErro5 = 10065,
    /// <summary>
    /// 道具不足
    /// </summary>
    [pbr::OriginalName("ItemNotEnough")] ItemNotEnough = 10100,
    /// <summary>
    /// 体力不足
    /// </summary>
    [pbr::OriginalName("Resource1NotEnough")] Resource1NotEnough = 10101,
    /// <summary>
    /// 粮食不足
    /// </summary>
    [pbr::OriginalName("Resource2NotEnough")] Resource2NotEnough = 10102,
    /// <summary>
    /// 铁矿不足
    /// </summary>
    [pbr::OriginalName("Resource3NotEnough")] Resource3NotEnough = 10103,
    /// <summary>
    /// 金币不足
    /// </summary>
    [pbr::OriginalName("Resource4NotEnough")] Resource4NotEnough = 10104,
    /// <summary>
    /// 英雄经验不足
    /// </summary>
    [pbr::OriginalName("Resource5NotEnough")] Resource5NotEnough = 10105,
    /// <summary>
    /// 钻石不足
    /// </summary>
    [pbr::OriginalName("Resource6NotEnough")] Resource6NotEnough = 10106,
    /// <summary>
    /// 荣誉积分不足
    /// </summary>
    [pbr::OriginalName("Resource7NotEnough")] Resource7NotEnough = 10107,
    /// <summary>
    /// 远征奖章不足
    /// </summary>
    [pbr::OriginalName("Resource8NotEnough")] Resource8NotEnough = 10108,
    /// <summary>
    /// 光荣勋章不足
    /// </summary>
    [pbr::OriginalName("Resource9NotEnough")] Resource9NotEnough = 10109,
    /// <summary>
    /// 技能勋章不足
    /// </summary>
    [pbr::OriginalName("Resource10NotEnough")] Resource10NotEnough = 10110,
    /// <summary>
    /// 闪亮金币不足
    /// </summary>
    [pbr::OriginalName("Resource11NotEnough")] Resource11NotEnough = 10111,
    /// <summary>
    /// VIP点数不足
    /// </summary>
    [pbr::OriginalName("Resource12NotEnough")] Resource12NotEnough = 10112,
    /// <summary>
    /// 金砖不足
    /// </summary>
    [pbr::OriginalName("Resource13NotEnough")] Resource13NotEnough = 10113,
    /// <summary>
    /// 原油不足
    /// </summary>
    [pbr::OriginalName("Resource14NotEnough")] Resource14NotEnough = 10114,
    /// <summary>
    /// 同盟贡献点不足
    /// </summary>
    [pbr::OriginalName("Resource15NotEnough")] Resource15NotEnough = 10115,
    /// <summary>
    /// ----------------------- gate 11000 ~ 11999
    /// </summary>
    [pbr::OriginalName("SessionNotReady")] SessionNotReady = 11001,
    /// <summary>
    /// 密钥生成失败
    /// </summary>
    [pbr::OriginalName("ShareKeyGenFailed")] ShareKeyGenFailed = 11002,
    /// <summary>
    /// 秘钥测试失败
    /// </summary>
    [pbr::OriginalName("ShareKeyTestFailed")] ShareKeyTestFailed = 11003,
    /// <summary>
    /// 登录秘钥验证失败
    /// </summary>
    [pbr::OriginalName("LoginTokenVerifyFailed")] LoginTokenVerifyFailed = 11004,
    /// <summary>
    /// 服务版本号未找到
    /// </summary>
    [pbr::OriginalName("ServiceVersionsNotFound")] ServiceVersionsNotFound = 11005,
    /// <summary>
    /// 未设置区服Id
    /// </summary>
    [pbr::OriginalName("ServerIDEmpty")] ServerIdempty = 11006,
    /// <summary>
    /// 账号角色列表为空
    /// </summary>
    [pbr::OriginalName("AccountListIsEmpty")] AccountListIsEmpty = 11007,
    /// <summary>
    /// 12000 ~ 12299 角色
    /// </summary>
    [pbr::OriginalName("RolePollerNotFound")] RolePollerNotFound = 12001,
    /// <summary>
    /// 获取角色数据超时
    /// </summary>
    [pbr::OriginalName("RoleGetTimeout")] RoleGetTimeout = 12002,
    /// <summary>
    /// 登录失败
    /// </summary>
    [pbr::OriginalName("RoleLoginFailed")] RoleLoginFailed = 12003,
    /// <summary>
    /// 需要创建角色
    /// </summary>
    [pbr::OriginalName("RoleNeedCreate")] RoleNeedCreate = 12004,
    /// <summary>
    /// 角色创建失败
    /// </summary>
    [pbr::OriginalName("RoleCreateFailed")] RoleCreateFailed = 12005,
    /// <summary>
    /// 角色已创建
    /// </summary>
    [pbr::OriginalName("RoleCreatedBefore")] RoleCreatedBefore = 12006,
    /// <summary>
    /// 角色GM背包执行失败
    /// </summary>
    [pbr::OriginalName("RoleGMBagExecuteFailed")] RoleGmbagExecuteFailed = 12007,
    /// <summary>
    /// 角色GM SG 执行失败    
    /// </summary>
    [pbr::OriginalName("RoleGMSGExecuteFailed")] RoleGmsgexecuteFailed = 12008,
    /// <summary>
    /// 12300 ~ 12399 英雄
    /// </summary>
    [pbr::OriginalName("HeroNotExist")] HeroNotExist = 12301,
    /// <summary>
    /// 英雄已存在
    /// </summary>
    [pbr::OriginalName("HeroAlreadyExist")] HeroAlreadyExist = 12302,
    /// <summary>
    /// 英雄已满星
    /// </summary>
    [pbr::OriginalName("HeroStarFull")] HeroStarFull = 12303,
    /// <summary>
    /// 英雄已满级
    /// </summary>
    [pbr::OriginalName("HeroLevelFull")] HeroLevelFull = 12304,
    /// <summary>
    /// 英雄技能已满级
    /// </summary>
    [pbr::OriginalName("HeroSkillLevelFull")] HeroSkillLevelFull = 12305,
    /// <summary>
    /// 英雄类型不符合
    /// </summary>
    [pbr::OriginalName("HeroTypeNonConformance")] HeroTypeNonConformance = 12306,
    /// <summary>
    /// 技能未解锁
    /// </summary>
    [pbr::OriginalName("HeroSkillUnlock")] HeroSkillUnlock = 12307,
    /// <summary>
    /// 英雄不能在队伍内
    /// </summary>
    [pbr::OriginalName("HeroCanNotInTeam")] HeroCanNotInTeam = 12308,
    /// <summary>
    /// 英雄晋升已满级
    /// </summary>
    [pbr::OriginalName("HeroHonorLevelFull")] HeroHonorLevelFull = 12309,
    /// <summary>
    /// 英雄创建失败
    /// </summary>
    [pbr::OriginalName("HeroCreateFailed")] HeroCreateFailed = 12340,
    /// <summary>
    /// 12400 ~ 12599 建筑
    /// </summary>
    [pbr::OriginalName("BuildAlreadyExists")] BuildAlreadyExists = 12401,
    /// <summary>
    /// 建造或者升级条件不满足
    /// </summary>
    [pbr::OriginalName("BuildDemandNotSatisfied")] BuildDemandNotSatisfied = 12402,
    /// <summary>
    /// 建造所需资源不足
    /// </summary>
    [pbr::OriginalName("BuildCreateCostNotEnough")] BuildCreateCostNotEnough = 12403,
    /// <summary>
    /// 建筑不能升级
    /// </summary>
    [pbr::OriginalName("BuildCanNotupgrade")] BuildCanNotupgrade = 12404,
    /// <summary>
    /// 建筑不存在
    /// </summary>
    [pbr::OriginalName("BuildNotExists")] BuildNotExists = 12405,
    /// <summary>
    /// 建筑正在创建或者升级
    /// </summary>
    [pbr::OriginalName("BuildIsCreatingOrUpgrading")] BuildIsCreatingOrUpgrading = 12406,
    /// <summary>
    /// 升级所需资源不足
    /// </summary>
    [pbr::OriginalName("BuildUpgradeCostNotEnough")] BuildUpgradeCostNotEnough = 12407,
    /// <summary>
    /// 建筑不能移动
    /// </summary>
    [pbr::OriginalName("BuildCanNotMove")] BuildCanNotMove = 12408,
    /// <summary>
    /// 队列不存在
    /// </summary>
    [pbr::OriginalName("BuildQueueNotExists")] BuildQueueNotExists = 12421,
    /// <summary>
    /// 队列未完成
    /// </summary>
    [pbr::OriginalName("BuildQueueNotFinish")] BuildQueueNotFinish = 12422,
    /// <summary>
    /// 队列已经帮助
    /// </summary>
    [pbr::OriginalName("BuildQueueHasBeenHelped")] BuildQueueHasBeenHelped = 12423,
    /// <summary>
    /// 队列已完成
    /// </summary>
    [pbr::OriginalName("BuildQueueIsFinish")] BuildQueueIsFinish = 12424,
    /// <summary>
    /// 队列不能帮助
    /// </summary>
    [pbr::OriginalName("BuildQueueCanNotHelp")] BuildQueueCanNotHelp = 12425,
    /// <summary>
    /// 队列不能加速
    /// </summary>
    [pbr::OriginalName("BuildQueueCanNotAccelerate")] BuildQueueCanNotAccelerate = 12426,
    /// <summary>
    /// 物品不能加速此队列
    /// </summary>
    [pbr::OriginalName("BuildQueueItemCanNotAccelerate")] BuildQueueItemCanNotAccelerate = 12427,
    /// <summary>
    /// 道具加速不足
    /// </summary>
    [pbr::OriginalName("BuildQueueItemAccelerateNoteEnough")] BuildQueueItemAccelerateNoteEnough = 12428,
    /// <summary>
    /// 钻石加速不足
    /// </summary>
    [pbr::OriginalName("BuildQueueDiamondAccelerateNoteEnough")] BuildQueueDiamondAccelerateNoteEnough = 12429,
    /// <summary>
    /// 工人不存在
    /// </summary>
    [pbr::OriginalName("BuildWorkerNotExists")] BuildWorkerNotExists = 12430,
    /// <summary>
    /// 工人不是空闲状态
    /// </summary>
    [pbr::OriginalName("BuildWorkeruIsNotIdle")] BuildWorkeruIsNotIdle = 12431,
    /// <summary>
    /// 建筑等级达到最大
    /// </summary>
    [pbr::OriginalName("BuildMaxLevel")] BuildMaxLevel = 12432,
    /// <summary>
    /// 12600 ~ 12649 城内地图
    /// </summary>
    [pbr::OriginalName("InnerCityGridNotNext")] InnerCityGridNotNext = 12601,
    /// <summary>
    /// 城内地图关卡未通关
    /// </summary>
    [pbr::OriginalName("InnerCityDungeonNotPass")] InnerCityDungeonNotPass = 12602,
    /// <summary>
    /// 只能占领相邻区域
    /// </summary>
    [pbr::OriginalName("InnerCityRegionNotNext")] InnerCityRegionNotNext = 12603,
    /// <summary>
    /// 地图格子未解锁
    /// </summary>
    [pbr::OriginalName("InnerCityGridNotUnlock")] InnerCityGridNotUnlock = 12604,
    /// <summary>
    /// 12700 ~ 12749 装备
    /// </summary>
    [pbr::OriginalName("EquipAlreadyWorn")] EquipAlreadyWorn = 17501,
    /// <summary>
    /// 装备未找到
    /// </summary>
    [pbr::OriginalName("EquipNotFound")] EquipNotFound = 12701,
    /// <summary>
    /// 装备已满级
    /// </summary>
    [pbr::OriginalName("EquipLevelFull")] EquipLevelFull = 12702,
    /// <summary>
    /// 装备已满晋升级
    /// </summary>
    [pbr::OriginalName("EquipPromotionLevelFull")] EquipPromotionLevelFull = 12703,
    /// <summary>
    /// 装备不可生产
    /// </summary>
    [pbr::OriginalName("EquipNotProducible")] EquipNotProducible = 12704,
    /// <summary>
    ///目标相同
    /// </summary>
    [pbr::OriginalName("EquipTargetSame")] EquipTargetSame = 12705,
    /// <summary>
    ///没有目标英雄对象
    /// </summary>
    [pbr::OriginalName("EquipNotTargetObject")] EquipNotTargetObject = 12706,
    /// <summary>
    /// 装备创建失败
    /// </summary>
    [pbr::OriginalName("EquipCreateFailed")] EquipCreateFailed = 12707,
    /// <summary>
    /// 该装备不能升级
    /// </summary>
    [pbr::OriginalName("EquipCantUpgradeLevel")] EquipCantUpgradeLevel = 12708,
    /// <summary>
    /// 该装备不能晋升
    /// </summary>
    [pbr::OriginalName("EquipCantUpgradePromotion")] EquipCantUpgradePromotion = 12709,
    /// <summary>
    /// 没有可穿戴装备
    /// </summary>
    [pbr::OriginalName("EquipTakeOnNoMatch")] EquipTakeOnNoMatch = 12710,
    /// <summary>
    /// 已穿戴当前装备
    /// </summary>
    [pbr::OriginalName("EquipTakeOnCurrentAlready")] EquipTakeOnCurrentAlready = 12711,
    /// <summary>
    /// 没有装备可卸下
    /// </summary>
    [pbr::OriginalName("EquipTakeOffEmpty")] EquipTakeOffEmpty = 12712,
    /// <summary>
    /// 请选择分解对象
    /// </summary>
    [pbr::OriginalName("EquipResolveIDEmpty")] EquipResolveIdempty = 12713,
    /// <summary>
    /// 已穿戴的装备不可分解
    /// </summary>
    [pbr::OriginalName("EquipResolveDressed")] EquipResolveDressed = 12714,
    /// <summary>
    /// 该材料不可合成
    /// </summary>
    [pbr::OriginalName("EquipMaterialCantSynthesis")] EquipMaterialCantSynthesis = 12715,
    /// <summary>
    /// 该材料不可分解
    /// </summary>
    [pbr::OriginalName("EquipMaterialCantResolve")] EquipMaterialCantResolve = 12716,
    /// <summary>
    /// 12750 ~ 12799 道具
    /// </summary>
    [pbr::OriginalName("ItemNotFound")] ItemNotFound = 12750,
    /// <summary>
    /// 道具使用失败
    /// </summary>
    [pbr::OriginalName("ItemUseFailed")] ItemUseFailed = 12751,
    /// <summary>
    /// 道具添加失败
    /// </summary>
    [pbr::OriginalName("ItemAddFailed")] ItemAddFailed = 12752,
    /// <summary>
    /// 道具自动使用失败
    /// </summary>
    [pbr::OriginalName("ItemAutoUseFailed")] ItemAutoUseFailed = 12753,
    /// <summary>
    /// 道具无法直接用钻石购买
    /// </summary>
    [pbr::OriginalName("ItemCantPayDiamond")] ItemCantPayDiamond = 12754,
    /// <summary>
    /// 12800 ~ 12849 酒馆
    /// </summary>
    [pbr::OriginalName("RecruitFailed")] RecruitFailed = 12801,
    /// <summary>
    /// 请先设置心愿英雄
    /// </summary>
    [pbr::OriginalName("RecruitWishSetFirst")] RecruitWishSetFirst = 12802,
    /// <summary>
    /// 心愿次数不足
    /// </summary>
    [pbr::OriginalName("RecruitWishTimesNotEnough")] RecruitWishTimesNotEnough = 12803,
    /// <summary>
    /// 12850 ~ 12899 商店
    /// </summary>
    [pbr::OriginalName("StoreGoodsNotEnough")] StoreGoodsNotEnough = 12850,
    /// <summary>
    /// 12900 ~ 12999 商城
    /// </summary>
    [pbr::OriginalName("ShopDailyDealGiftLocked")] ShopDailyDealGiftLocked = 12901,
    /// <summary>
    /// 已购买任意档位商品无法购买此礼包
    /// </summary>
    [pbr::OriginalName("ShopCantPurchaseMerged1")] ShopCantPurchaseMerged1 = 12902,
    /// <summary>
    /// 超级月卡未激活
    /// </summary>
    [pbr::OriginalName("ShopMonthCardNotActive")] ShopMonthCardNotActive = 12903,
    /// <summary>
    /// 超级月卡已过期
    /// </summary>
    [pbr::OriginalName("ShopMonthCardExpired")] ShopMonthCardExpired = 12904,
    /// <summary>
    /// 周卡未激活
    /// </summary>
    [pbr::OriginalName("ShopWeekCardNotActive")] ShopWeekCardNotActive = 12905,
    /// <summary>
    /// 周卡已过期
    /// </summary>
    [pbr::OriginalName("ShopWeekCardExpired")] ShopWeekCardExpired = 12906,
    /// <summary>
    /// 13100 ~ 13149 关卡
    /// </summary>
    [pbr::OriginalName("DungeonCantFightMax")] DungeonCantFightMax = 13100,
    /// <summary>
    /// 结算关卡与最后挑战的关卡不匹配
    /// </summary>
    [pbr::OriginalName("DungeonLastFightNotMatch")] DungeonLastFightNotMatch = 13101,
    /// <summary>
    /// 尚未开启关卡挑战
    /// </summary>
    [pbr::OriginalName("DungeonFightFirstMission")] DungeonFightFirstMission = 13102,
    /// <summary>
    /// 该关卡尚未通关
    /// </summary>
    [pbr::OriginalName("DungeonBoxIDGreaterThenMax")] DungeonBoxIdgreaterThenMax = 13103,
    /// <summary>
    /// 该关卡奖励已领取
    /// </summary>
    [pbr::OriginalName("DungeonBoxClaimed")] DungeonBoxClaimed = 13104,
    /// <summary>
    /// 无法重复挑战
    /// </summary>
    [pbr::OriginalName("DungeonFightRepeated")] DungeonFightRepeated = 13105,
    /// <summary>
    /// 无法跳关挑战
    /// </summary>
    [pbr::OriginalName("DungeonFightNotNextID")] DungeonFightNotNextId = 13106,
    /// <summary>
    /// 13150 ~ 13199 幸存者
    /// </summary>
    [pbr::OriginalName("SurvivorNotFound")] SurvivorNotFound = 13150,
    /// <summary>
    /// 13200 ~ 13299 无人机
    /// </summary>
    [pbr::OriginalName("UavMaxLevel")] UavMaxLevel = 13200,
    /// <summary>
    /// 13300 ~ 13399 士兵
    /// </summary>
    [pbr::OriginalName("SoldierMaxNum")] SoldierMaxNum = 13300,
    /// <summary>
    /// 13400 ~ 13499 vip
    /// </summary>
    [pbr::OriginalName("VipExpire")] VipExpire = 13400,
    /// <summary>
    /// 13500 ~ 13599 城际贸易
    /// </summary>
    [pbr::OriginalName("TradeVanSetOutFull")] TradeVanSetOutFull = 13500,
    /// <summary>
    ///  城际贸易次数不足
    /// </summary>
    [pbr::OriginalName("TradeTimesNotEnough")] TradeTimesNotEnough = 13501,
    /// <summary>
    /// 找不到货车数据
    /// </summary>
    [pbr::OriginalName("TradeNotFound")] TradeNotFound = 13502,
    /// <summary>
    /// 货车已出发
    /// </summary>
    [pbr::OriginalName("TradeHaveBeenDepart")] TradeHaveBeenDepart = 13503,
    /// <summary>
    /// 货车未到达
    /// </summary>
    [pbr::OriginalName("TradeNotArrived")] TradeNotArrived = 13504,
    /// <summary>
    /// 找不到货车记录
    /// </summary>
    [pbr::OriginalName("TradeRecordNotFound")] TradeRecordNotFound = 13505,
    /// <summary>
    /// 已通缉
    /// </summary>
    [pbr::OriginalName("TradeWanted")] TradeWanted = 13506,
    /// <summary>
    /// 掠夺次数限制
    /// </summary>
    [pbr::OriginalName("TradeRobTimesLimit")] TradeRobTimesLimit = 13507,
    /// <summary>
    /// 创建货物运输失败
    /// </summary>
    [pbr::OriginalName("TradeCreateCargoTransportFailed")] TradeCreateCargoTransportFailed = 13508,
    /// <summary>
    /// 找不到火车数据
    /// </summary>
    [pbr::OriginalName("TradeTrainNotFound")] TradeTrainNotFound = 13509,
    /// <summary>
    /// 无法召唤火车
    /// </summary>
    [pbr::OriginalName("TradeCannotSummonTrain")] TradeCannotSummonTrain = 13510,
    /// <summary>
    ///  当前不在准备时间
    /// </summary>
    [pbr::OriginalName("TradeNotInPrepareTime")] TradeNotInPrepareTime = 13511,
    /// <summary>
    /// 已经感谢
    /// </summary>
    [pbr::OriginalName("TradeTrainThanksAlready")] TradeTrainThanksAlready = 13512,
    /// <summary>
    /// 布阵权限不足
    /// </summary>
    [pbr::OriginalName("TradeNoFormationPermission")] TradeNoFormationPermission = 13513,
    /// <summary>
    /// 没有权限
    /// </summary>
    [pbr::OriginalName("TradeNoPermission")] TradeNoPermission = 13514,
    /// <summary>
    /// 重复点赞
    /// </summary>
    [pbr::OriginalName("TradeRepeatLike")] TradeRepeatLike = 13515,
    /// <summary>
    /// 重复邀请
    /// </summary>
    [pbr::OriginalName("TradeRepeatInvite")] TradeRepeatInvite = 13516,
    /// <summary>
    /// 邀请cd
    /// </summary>
    [pbr::OriginalName("TradeInviteCD")] TradeInviteCd = 13517,
    /// <summary>
    /// 邀请已过期
    /// </summary>
    [pbr::OriginalName("TradeInviteExpired")] TradeInviteExpired = 13518,
    /// <summary>
    /// 列车处于保护时间
    /// </summary>
    [pbr::OriginalName("TradeTrainInProtectTime")] TradeTrainInProtectTime = 13519,
    /// <summary>
    /// 货车已到达
    /// </summary>
    [pbr::OriginalName("TradeVanArrived")] TradeVanArrived = 13520,
    /// <summary>
    /// 货车处于保护时间
    /// </summary>
    [pbr::OriginalName("TradeVanInProtectTime")] TradeVanInProtectTime = 13521,
    /// <summary>
    /// 货车列车掠夺次数限制
    /// </summary>
    [pbr::OriginalName("TradeTrainRobTimesLimit")] TradeTrainRobTimesLimit = 13522,
    /// <summary>
    /// 13600 ~ 13699 科技
    /// </summary>
    [pbr::OriginalName("TechPreTechNotActive")] TechPreTechNotActive = 13600,
    /// <summary>
    /// 前一个科技未学习
    /// </summary>
    [pbr::OriginalName("TechPreTechNotStudy")] TechPreTechNotStudy = 13601,
    /// <summary>
    /// 科技等级达到最大
    /// </summary>
    [pbr::OriginalName("TechMaxLevel")] TechMaxLevel = 13602,
    /// <summary>
    /// ----------------------- account 40000 ~ 40199
    /// </summary>
    [pbr::OriginalName("AccountPollerNotFound")] AccountPollerNotFound = 40001,
    /// <summary>
    /// 获取账号数据超时
    /// </summary>
    [pbr::OriginalName("AccountGetTimeout")] AccountGetTimeout = 40002,
    /// <summary>
    /// 账号创建失败
    /// </summary>
    [pbr::OriginalName("AccountCreateFailed")] AccountCreateFailed = 40003,
    /// <summary>
    /// ----------------------- union 41000 ~ 41499
    /// </summary>
    [pbr::OriginalName("UnionInvalidWord")] UnionInvalidWord = 41001,
    /// <summary>
    /// 创建失败
    /// </summary>
    [pbr::OriginalName("UnionCreateFailed")] UnionCreateFailed = 41002,
    /// <summary>
    /// 名称长度错误
    /// </summary>
    [pbr::OriginalName("UnionNameLength")] UnionNameLength = 41003,
    /// <summary>
    /// 名称重复
    /// </summary>
    [pbr::OriginalName("UnionNameRepeat")] UnionNameRepeat = 41004,
    /// <summary>
    /// 找不到联盟
    /// </summary>
    [pbr::OriginalName("UnionNotFound")] UnionNotFound = 41005,
    /// <summary>
    /// 同意申请
    /// </summary>
    [pbr::OriginalName("UnionApplyAgree")] UnionApplyAgree = 41006,
    /// <summary>
    /// 拒绝申请
    /// </summary>
    [pbr::OriginalName("UnionApplyRefuse")] UnionApplyRefuse = 41007,
    /// <summary>
    /// 重复申请
    /// </summary>
    [pbr::OriginalName("UnionApplyRepeat")] UnionApplyRepeat = 41008,
    /// <summary>
    /// 不满足申请条件
    /// </summary>
    [pbr::OriginalName("UnionNotEnoughApplyCondition")] UnionNotEnoughApplyCondition = 41009,
    /// <summary>
    /// 不满足创建条件
    /// </summary>
    [pbr::OriginalName("UnionNotEnoughCreateCondition")] UnionNotEnoughCreateCondition = 41010,
    /// <summary>
    /// 人数已满
    /// </summary>
    [pbr::OriginalName("UnionMemberNumLimit")] UnionMemberNumLimit = 41011,
    /// <summary>
    /// 未找到符合条件联盟
    /// </summary>
    [pbr::OriginalName("UnionNoFoundMeetCondition")] UnionNoFoundMeetCondition = 41012,
    /// <summary>
    /// 未加入联盟
    /// </summary>
    [pbr::OriginalName("UnionNoEnterUnion")] UnionNoEnterUnion = 41013,
    /// <summary>
    /// 无权限
    /// </summary>
    [pbr::OriginalName("UnionNoPermission")] UnionNoPermission = 41014,
    /// <summary>
    /// 宣言长度错误
    /// </summary>
    [pbr::OriginalName("UnionNoticeLengthErr")] UnionNoticeLengthErr = 41015,
    /// <summary>
    /// 条件参数错误
    /// </summary>
    [pbr::OriginalName("UnionConditionParamsErr")] UnionConditionParamsErr = 41016,
    /// <summary>
    /// 礼物已领取
    /// </summary>
    [pbr::OriginalName("UnionGiftHasReceived")] UnionGiftHasReceived = 41017,
    /// <summary>
    /// 申请信息超时
    /// </summary>
    [pbr::OriginalName("UnionApplyInfoTimeout")] UnionApplyInfoTimeout = 41018,
    /// <summary>
    /// 不是联盟成员
    /// </summary>
    [pbr::OriginalName("UnionNotMember")] UnionNotMember = 41019,
    /// <summary>
    /// 联盟人数大于1
    /// </summary>
    [pbr::OriginalName("UnionMemberNumGT1")] UnionMemberNumGt1 = 41020,
    /// <summary>
    /// 联盟成员不能退出
    /// </summary>
    [pbr::OriginalName("UnionMemberCannotOut")] UnionMemberCannotOut = 41021,
    /// <summary>
    /// 24小时不能加入联盟
    /// </summary>
    [pbr::OriginalName("Union24HourCannotJoin")] Union24HourCannotJoin = 41022,
    /// <summary>
    /// 没有开启联盟建筑
    /// </summary>
    [pbr::OriginalName("UnionNotOpenUnionBuild")] UnionNotOpenUnionBuild = 41023,
    /// <summary>
    /// 已经加入联盟
    /// </summary>
    [pbr::OriginalName("UnionHasJoinUnion")] UnionHasJoinUnion = 41024,
    /// <summary>
    /// 权限数量限制
    /// </summary>
    [pbr::OriginalName("UnionPermissionNumLimit")] UnionPermissionNumLimit = 41025,
    /// <summary>
    /// 联盟战力不足
    /// </summary>
    [pbr::OriginalName("UnionPowerNotEnough")] UnionPowerNotEnough = 41026,
    /// <summary>
    /// 联盟总部等级不足
    /// </summary>
    [pbr::OriginalName("UnionBaseLevelNotEnough")] UnionBaseLevelNotEnough = 41027,
    /// <summary>
    /// 礼物等级不足
    /// </summary>
    [pbr::OriginalName("UnionGiftLevelNotEnough")] UnionGiftLevelNotEnough = 41028,
    /// <summary>
    /// 礼物不存在
    /// </summary>
    [pbr::OriginalName("UnionGiftNotExist")] UnionGiftNotExist = 41029,
    /// <summary>
    /// 联盟成员总部等级不足
    /// </summary>
    [pbr::OriginalName("UnionMemberHeadquartersLevelNotEnough")] UnionMemberHeadquartersLevelNotEnough = 41030,
    /// <summary>
    /// 联盟科技升级中
    /// </summary>
    [pbr::OriginalName("UnionTechUpgrading")] UnionTechUpgrading = 41031,
    /// <summary>
    /// 联盟科技已满级
    /// </summary>
    [pbr::OriginalName("UnionTechLevelMax")] UnionTechLevelMax = 41032,
    /// <summary>
    /// 联盟科技捐献次数已达上限
    /// </summary>
    [pbr::OriginalName("UnionTechDonateTimesMax")] UnionTechDonateTimesMax = 41033,
    /// <summary>
    /// 联盟科技不存在
    /// </summary>
    [pbr::OriginalName("UnionTechNotEnough")] UnionTechNotEnough = 41034,
    /// <summary>
    /// 联盟科技只能推荐一个
    /// </summary>
    [pbr::OriginalName("UnionOnlyOneRecommendTech")] UnionOnlyOneRecommendTech = 41035,
    /// <summary>
    /// 联盟里程碑已结束
    /// </summary>
    [pbr::OriginalName("UnionMileStoneEnd")] UnionMileStoneEnd = 41036,
    /// <summary>
    /// 联盟里程碑礼物未领取，不能转让
    /// </summary>
    [pbr::OriginalName("UnionMileStoneRewardNoReceive1")] UnionMileStoneRewardNoReceive1 = 41037,
    /// <summary>
    /// 联盟里程碑礼物未领取，不能被转让
    /// </summary>
    [pbr::OriginalName("UnionMileStoneRewardNoReceive2")] UnionMileStoneRewardNoReceive2 = 41038,
    /// <summary>
    /// 联盟科技前置科技未锁定
    /// </summary>
    [pbr::OriginalName("UnionPreTechLocked")] UnionPreTechLocked = 41039,
    /// <summary>
    /// 联盟科技未解锁
    /// </summary>
    [pbr::OriginalName("UnionTechTagLocked")] UnionTechTagLocked = 41040,
    /// <summary>
    /// 联盟科技等待升级
    /// </summary>
    [pbr::OriginalName("UnionTechWaitUpgrade")] UnionTechWaitUpgrade = 41041,
    /// <summary>
    /// 联盟里程碑完成时不是成员
    /// </summary>
    [pbr::OriginalName("UnionMilestoneNotMember")] UnionMilestoneNotMember = 41042,
    /// <summary>
    /// 礼物已过期
    /// </summary>
    [pbr::OriginalName("UnionGiftTimeout")] UnionGiftTimeout = 41043,
    /// <summary>
    /// 当前已经有列车
    /// </summary>
    [pbr::OriginalName("UnionTradeHaveTrain")] UnionTradeHaveTrain = 41044,
    /// <summary>
    /// 召唤列车失败
    /// </summary>
    [pbr::OriginalName("UnionTradeTrainSummonFail")] UnionTradeTrainSummonFail = 41045,
    /// <summary>
    /// 加入联盟时间不足，不能参与列车
    /// </summary>
    [pbr::OriginalName("UnionTradeJoinTimeNotEnough")] UnionTradeJoinTimeNotEnough = 41046,
    /// <summary>
    /// ----------------------- arena 42000 ~ 42599
    /// </summary>
    [pbr::OriginalName("ArenaSrvTimeout")] ArenaSrvTimeout = 42000,
    /// <summary>
    /// 远程调用竞技场服务失败
    /// </summary>
    [pbr::OriginalName("ArenaRpcFail")] ArenaRpcFail = 42001,
    /// <summary>
    /// 竞技场分组未配置
    /// </summary>
    [pbr::OriginalName("ArenaGroupNotFound")] ArenaGroupNotFound = 42003,
    /// <summary>
    /// 竞技场poller没找到
    /// </summary>
    [pbr::OriginalName("ArenaPollerNotFound")] ArenaPollerNotFound = 42004,
    /// <summary>
    /// 竞技场没找到
    /// </summary>
    [pbr::OriginalName("ArenaTypeNotFound")] ArenaTypeNotFound = 42005,
    /// <summary>
    /// 玩家数据未初始化
    /// </summary>
    [pbr::OriginalName("NoviceInfoNotCreate")] NoviceInfoNotCreate = 42011,
    /// <summary>
    /// 挑战的排名不合法
    /// </summary>
    [pbr::OriginalName("NoviceChallengeRankNotValid")] NoviceChallengeRankNotValid = 42012,
    /// <summary>
    /// 挑战的数据不匹配
    /// </summary>
    [pbr::OriginalName("NoviceChallengeNotMatch")] NoviceChallengeNotMatch = 42013,
    /// <summary>
    /// 训练营购买挑战次数失败
    /// </summary>
    [pbr::OriginalName("NoviceBuyChallengeFail")] NoviceBuyChallengeFail = 42014,
    /// <summary>
    /// 已领取成就奖励
    /// </summary>
    [pbr::OriginalName("NoviceAchieveAlreadyDraw")] NoviceAchieveAlreadyDraw = 42015,
    /// <summary>
    /// 训练营历史最高排名不够
    /// </summary>
    [pbr::OriginalName("NoviceAchieveRankNotEnough")] NoviceAchieveRankNotEnough = 42016,
    /// <summary>
    /// 挑战次数不足
    /// </summary>
    [pbr::OriginalName("NoviceChallengeTimesNotEnough")] NoviceChallengeTimesNotEnough = 42017,
    /// <summary>
    /// 巅峰竞技场排名分组未找到
    /// </summary>
    [pbr::OriginalName("PeakRankGroupNotFound")] PeakRankGroupNotFound = 42100,
    /// <summary>
    /// 挑战次数不足
    /// </summary>
    [pbr::OriginalName("PeakChallengeTimesNotEnough")] PeakChallengeTimesNotEnough = 42101,
    /// <summary>
    /// 巅峰竞技场未报名
    /// </summary>
    [pbr::OriginalName("PeakInfoNotCreate")] PeakInfoNotCreate = 42103,
    /// <summary>
    /// 挑战的数据不匹配
    /// </summary>
    [pbr::OriginalName("PeakChallengeNotMatch")] PeakChallengeNotMatch = 42104,
    /// <summary>
    /// 挑战的排名不在允许范围内
    /// </summary>
    [pbr::OriginalName("PeakChallengeRankNotRange")] PeakChallengeRankNotRange = 42105,
    /// <summary>
    /// 43000 ~ 43099 充值
    /// </summary>
    [pbr::OriginalName("PaymentNotSupportTokenPay")] PaymentNotSupportTokenPay = 43001,
    /// <summary>
    /// 价格不匹配
    /// </summary>
    [pbr::OriginalName("PaymentPriceNotMatch")] PaymentPriceNotMatch = 43002,
    /// <summary>
    /// 订单未找到
    /// </summary>
    [pbr::OriginalName("PaymentOrderNotFound")] PaymentOrderNotFound = 43003,
    /// <summary>
    /// 订单无效
    /// </summary>
    [pbr::OriginalName("PaymentOrderInvalid")] PaymentOrderInvalid = 43004,
    /// <summary>
    /// 充值渠道未找到
    /// </summary>
    [pbr::OriginalName("PaymentChannelNotFound")] PaymentChannelNotFound = 43005,
    /// <summary>
    /// 订单更新失败
    /// </summary>
    [pbr::OriginalName("PaymentOrderUpdateFailed")] PaymentOrderUpdateFailed = 43006,
    /// <summary>
    /// 订单发货失败
    /// </summary>
    [pbr::OriginalName("PaymentOrderDeliveryFailed")] PaymentOrderDeliveryFailed = 43007,
    /// <summary>
    /// 下单前检查失败(远程调用失败)
    /// </summary>
    [pbr::OriginalName("PaymentOrderCheckFailedRemoteCall")] PaymentOrderCheckFailedRemoteCall = 43008,
    /// <summary>
    /// 该礼包已购买
    /// </summary>
    [pbr::OriginalName("PaymentGiftPurchased")] PaymentGiftPurchased = 43009,
    /// <summary>
    /// ----------------------- battle &amp; report 44000 ~ 44999
    /// </summary>
    [pbr::OriginalName("FormationHeroRepeatInTeam")] FormationHeroRepeatInTeam = 44001,
    /// <summary>
    /// 同一个队伍中位置不可重复
    /// </summary>
    [pbr::OriginalName("FormationPositionRepeatInTeam")] FormationPositionRepeatInTeam = 44002,
    /// <summary>
    /// 所有队伍中英雄不可重复
    /// </summary>
    [pbr::OriginalName("FormationHeroRepeatInAllTeams")] FormationHeroRepeatInAllTeams = 44003,
    /// <summary>
    /// 所有队伍中位置不可重复
    /// </summary>
    [pbr::OriginalName("FormationPositionRepeatInAllTeams")] FormationPositionRepeatInAllTeams = 44004,
    /// <summary>
    /// 无效位置
    /// </summary>
    [pbr::OriginalName("FormationInvalidPos")] FormationInvalidPos = 44005,
    /// <summary>
    /// 阵容尚未设置
    /// </summary>
    [pbr::OriginalName("BattleFormationNotFound")] BattleFormationNotFound = 44101,
    /// <summary>
    /// 英雄技能为空
    /// </summary>
    [pbr::OriginalName("BattleHeroSkillEmpty")] BattleHeroSkillEmpty = 44102,
    /// <summary>
    /// 英雄属性为空
    /// </summary>
    [pbr::OriginalName("BattleHeroAttrEmpty")] BattleHeroAttrEmpty = 44103,
    /// <summary>
    /// 解析阵容失败
    /// </summary>
    [pbr::OriginalName("BattleParseTeamFailed")] BattleParseTeamFailed = 44201,
    /// <summary>
    /// 战斗创建失败
    /// </summary>
    [pbr::OriginalName("BattleCreateFailed")] BattleCreateFailed = 44202,
    /// <summary>
    /// ----------------------- web 50000 ~ 52999
    /// ----------------------- 通用错误码 50000 ~ 50999 -----------------------
    /// </summary>
    [pbr::OriginalName("Failed")] Failed = 50001,
    /// <summary>
    /// 超时
    /// </summary>
    [pbr::OriginalName("Timeout")] Timeout = 50002,
    /// <summary>
    /// token未找到
    /// </summary>
    [pbr::OriginalName("TokenNotFound")] TokenNotFound = 50003,
    /// <summary>
    /// token验证失败
    /// </summary>
    [pbr::OriginalName("TokenVerifyFailed")] TokenVerifyFailed = 50004,
    /// <summary>
    /// 功能未开放
    /// </summary>
    [pbr::OriginalName("FeatureNotOpen")] FeatureNotOpen = 50005,
    /// <summary>
    /// ----------------------- web 51000 ~ 52999 -----------------------
    /// ------------------------ commmon 51000 ~ 51499
    /// </summary>
    [pbr::OriginalName("WebRouterNotFound")] WebRouterNotFound = 51001,
    /// <summary>
    /// 内部错误
    /// </summary>
    [pbr::OriginalName("WebInternalErr")] WebInternalErr = 51002,
    /// <summary>
    /// 参数未找到
    /// </summary>
    [pbr::OriginalName("WebParamNotFound")] WebParamNotFound = 51003,
    /// <summary>
    /// 参数格式不正确
    /// </summary>
    [pbr::OriginalName("WebParamInvalidFormat")] WebParamInvalidFormat = 51004,
    /// <summary>
    /// 参数读取失败
    /// </summary>
    [pbr::OriginalName("WebParamReadFailed")] WebParamReadFailed = 51005,
    /// <summary>
    /// 参数长度不正确
    /// </summary>
    [pbr::OriginalName("WebParamInvalidLength")] WebParamInvalidLength = 51006,
    /// <summary>
    /// 参数验证失败
    /// </summary>
    [pbr::OriginalName("WebParamVerifyFailed")] WebParamVerifyFailed = 51007,
    /// <summary>
    /// 缺少必要参数
    /// </summary>
    [pbr::OriginalName("WebParamMissRequired")] WebParamMissRequired = 51008,
    /// <summary>
    /// 缺少签名字段
    /// </summary>
    [pbr::OriginalName("WebParamMissSign")] WebParamMissSign = 51009,
    /// <summary>
    /// 获取限流数据失败
    /// </summary>
    [pbr::OriginalName("WebRateLimitGetFailed")] WebRateLimitGetFailed = 51021,
    /// <summary>
    /// 系统繁忙，请稍后再试
    /// </summary>
    [pbr::OriginalName("WebRateLimitNotAllow")] WebRateLimitNotAllow = 51022,
    /// <summary>
    /// 当前时间与服务器时间相差太多
    /// </summary>
    [pbr::OriginalName("WebInvalidTimestamp")] WebInvalidTimestamp = 51023,
    /// <summary>
    /// 签名校验失败
    /// </summary>
    [pbr::OriginalName("WebSignVerifyFailed")] WebSignVerifyFailed = 51024,
    /// <summary>
    /// ------------------------ account 51500 ~ 51599
    /// </summary>
    [pbr::OriginalName("WebSignInFailed")] WebSignInFailed = 51501,
    /// <summary>
    /// 生成验证token失败
    /// </summary>
    [pbr::OriginalName("WebCreateTokenFailed")] WebCreateTokenFailed = 51502,
    /// <summary>
    /// 已封号
    /// </summary>
    [pbr::OriginalName("WebAccountStatusForbid")] WebAccountStatusForbid = 51503,
  }

  #endregion

}

#endregion Designer generated code
