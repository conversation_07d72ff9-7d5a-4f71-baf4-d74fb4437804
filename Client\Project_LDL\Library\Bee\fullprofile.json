{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 17736, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 17736, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 17736, "tid": 2165, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 17736, "tid": 2165, "ts": 1751336637308650, "dur": 1133, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 17736, "tid": 2165, "ts": 1751336637313613, "dur": 856, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 17736, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 17736, "tid": 1, "ts": 1751336631877446, "dur": 4456, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17736, "tid": 1, "ts": 1751336631881909, "dur": 73611, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 17736, "tid": 1, "ts": 1751336631955543, "dur": 452792, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 17736, "tid": 2165, "ts": 1751336637314474, "dur": 32, "ph": "X", "name": "", "args": {}}, {"pid": 17736, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631876026, "dur": 35026, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631911057, "dur": 5383456, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631911884, "dur": 1673, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631913570, "dur": 1227, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631914804, "dur": 201, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915011, "dur": 351, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915366, "dur": 132, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915506, "dur": 12, "ph": "X", "name": "ProcessMessages 14802", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915521, "dur": 33, "ph": "X", "name": "ReadAsync 14802", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915556, "dur": 2, "ph": "X", "name": "ProcessMessages 2148", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915559, "dur": 14, "ph": "X", "name": "ReadAsync 2148", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915574, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915577, "dur": 18, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915598, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915601, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915619, "dur": 1, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915621, "dur": 25, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915649, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915651, "dur": 16, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915669, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915686, "dur": 21, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915710, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915725, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915727, "dur": 13, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915743, "dur": 12, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915759, "dur": 19, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915779, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915781, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915797, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915799, "dur": 13, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915815, "dur": 14, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915832, "dur": 10, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915845, "dur": 12, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915860, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915875, "dur": 11, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915890, "dur": 34, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915930, "dur": 3, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915936, "dur": 21, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915958, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915961, "dur": 12, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631915976, "dur": 51, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916030, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916047, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916049, "dur": 15, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916067, "dur": 12, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916082, "dur": 13, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916098, "dur": 10, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916111, "dur": 10, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916124, "dur": 26, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916152, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916154, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916173, "dur": 12, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916188, "dur": 11, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916203, "dur": 17, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916222, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916224, "dur": 26, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916252, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916272, "dur": 12, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916287, "dur": 12, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916304, "dur": 13, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916320, "dur": 10, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916334, "dur": 16, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916358, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916362, "dur": 23, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916387, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916391, "dur": 14, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916408, "dur": 44, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916455, "dur": 29, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916486, "dur": 1, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916489, "dur": 25, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916516, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916517, "dur": 13, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916534, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916555, "dur": 20, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916577, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916596, "dur": 1, "ph": "X", "name": "ProcessMessages 842", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916598, "dur": 12, "ph": "X", "name": "ReadAsync 842", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916613, "dur": 11, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916627, "dur": 19, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916649, "dur": 15, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916668, "dur": 14, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916683, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916685, "dur": 12, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916700, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916725, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916729, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916759, "dur": 2, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916762, "dur": 12, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916776, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916778, "dur": 16, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916797, "dur": 11, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916812, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916832, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916850, "dur": 23, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916874, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916876, "dur": 17, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916896, "dur": 11, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916911, "dur": 11, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916925, "dur": 12, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916940, "dur": 12, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916955, "dur": 11, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916969, "dur": 11, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916981, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916983, "dur": 10, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631916995, "dur": 11, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917010, "dur": 12, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917024, "dur": 17, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917047, "dur": 3, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917053, "dur": 24, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917079, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917082, "dur": 11, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917096, "dur": 8, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917107, "dur": 12, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917121, "dur": 15, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917139, "dur": 15, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917159, "dur": 12, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917174, "dur": 12, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917190, "dur": 10, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917201, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917203, "dur": 12, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917218, "dur": 12, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917235, "dur": 12, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917251, "dur": 11, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917265, "dur": 19, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917289, "dur": 10, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917302, "dur": 12, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917316, "dur": 13, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917333, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917354, "dur": 24, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917381, "dur": 11, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917395, "dur": 12, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917411, "dur": 13, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917427, "dur": 15, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917445, "dur": 16, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917463, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917464, "dur": 13, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917479, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917481, "dur": 11, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917495, "dur": 12, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917508, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917510, "dur": 13, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917526, "dur": 14, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917544, "dur": 15, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917562, "dur": 10, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917574, "dur": 11, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917588, "dur": 15, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917607, "dur": 15, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917624, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917626, "dur": 28, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917657, "dur": 12, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917672, "dur": 12, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917687, "dur": 13, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917703, "dur": 13, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917719, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917743, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917745, "dur": 12, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917760, "dur": 14, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917778, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917805, "dur": 15, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917822, "dur": 1, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917824, "dur": 13, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917841, "dur": 10, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631917854, "dur": 158, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918018, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918057, "dur": 3, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918063, "dur": 26, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918091, "dur": 1, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918094, "dur": 20, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918116, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918118, "dur": 16, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918136, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918138, "dur": 15, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918157, "dur": 17, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918175, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918177, "dur": 16, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918197, "dur": 14, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918215, "dur": 12, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918228, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918230, "dur": 11, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918244, "dur": 13, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918260, "dur": 15, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918277, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918279, "dur": 14, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918297, "dur": 13, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918312, "dur": 32, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918347, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918349, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918369, "dur": 1, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918371, "dur": 21, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918394, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918396, "dur": 14, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918414, "dur": 13, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918430, "dur": 14, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918447, "dur": 12, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918461, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918463, "dur": 13, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918479, "dur": 13, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918495, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918511, "dur": 11, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918526, "dur": 16, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918544, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918546, "dur": 17, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918566, "dur": 11, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918580, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918605, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918610, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918635, "dur": 2, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918638, "dur": 16, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918656, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918659, "dur": 14, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918676, "dur": 12, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918691, "dur": 11, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918705, "dur": 15, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918723, "dur": 12, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918739, "dur": 12, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918755, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918785, "dur": 3, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918790, "dur": 28, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918821, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918823, "dur": 13, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918839, "dur": 12, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918853, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918855, "dur": 11, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918869, "dur": 15, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918888, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918925, "dur": 14, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918941, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918943, "dur": 14, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918960, "dur": 14, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918979, "dur": 13, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631918994, "dur": 13, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919010, "dur": 13, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919027, "dur": 20, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919049, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919051, "dur": 13, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919067, "dur": 18, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919087, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919090, "dur": 17, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919110, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919112, "dur": 14, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919128, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919130, "dur": 12, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919145, "dur": 13, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919161, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919163, "dur": 14, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919179, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919181, "dur": 12, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919196, "dur": 15, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919214, "dur": 13, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919229, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919231, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919246, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919250, "dur": 16, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919269, "dur": 13, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919286, "dur": 11, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919300, "dur": 21, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919325, "dur": 14, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919341, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919343, "dur": 12, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919358, "dur": 12, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919375, "dur": 13, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919391, "dur": 10, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919404, "dur": 11, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919419, "dur": 12, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919434, "dur": 13, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919449, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919451, "dur": 12, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919466, "dur": 12, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919481, "dur": 12, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919496, "dur": 20, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919519, "dur": 12, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919535, "dur": 13, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919550, "dur": 27, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919580, "dur": 12, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919596, "dur": 10, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919608, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919609, "dur": 12, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919624, "dur": 13, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919640, "dur": 12, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919653, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919655, "dur": 11, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919669, "dur": 12, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919685, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919702, "dur": 11, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919716, "dur": 12, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919731, "dur": 33, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919766, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919768, "dur": 13, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919784, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919804, "dur": 14, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919819, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919821, "dur": 13, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919889, "dur": 23, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919914, "dur": 2, "ph": "X", "name": "ProcessMessages 2572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919917, "dur": 14, "ph": "X", "name": "ReadAsync 2572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919934, "dur": 15, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919951, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919953, "dur": 14, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919970, "dur": 14, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919985, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631919987, "dur": 11, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920001, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920047, "dur": 3, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920053, "dur": 39, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920095, "dur": 3, "ph": "X", "name": "ProcessMessages 1672", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920100, "dur": 37, "ph": "X", "name": "ReadAsync 1672", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920141, "dur": 2, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920144, "dur": 34, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920183, "dur": 3, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920188, "dur": 20, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920211, "dur": 1, "ph": "X", "name": "ProcessMessages 49", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920213, "dur": 21, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920237, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920239, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920266, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920280, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920282, "dur": 18, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920304, "dur": 12, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920320, "dur": 27, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920352, "dur": 3, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920357, "dur": 28, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920387, "dur": 2, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920391, "dur": 17, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920411, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920414, "dur": 22, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920439, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920442, "dur": 23, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920470, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920474, "dur": 35, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920510, "dur": 1, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920513, "dur": 17, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920533, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920535, "dur": 20, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920558, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920561, "dur": 18, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920580, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920582, "dur": 15, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920600, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920602, "dur": 14, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920620, "dur": 14, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920636, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920638, "dur": 12, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920653, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920673, "dur": 26, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920702, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920706, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920728, "dur": 1, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920730, "dur": 12, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920745, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920763, "dur": 13, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920779, "dur": 14, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920797, "dur": 11, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920809, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920811, "dur": 17, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920832, "dur": 23, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920857, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920859, "dur": 28, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920890, "dur": 2, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920893, "dur": 15, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920912, "dur": 16, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920931, "dur": 13, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920947, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920968, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920971, "dur": 19, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920992, "dur": 1, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631920994, "dur": 9, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921006, "dur": 11, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921020, "dur": 12, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921035, "dur": 11, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921050, "dur": 14, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921067, "dur": 14, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921083, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921085, "dur": 13, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921101, "dur": 11, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921115, "dur": 11, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921128, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921130, "dur": 18, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921152, "dur": 29, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921182, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921185, "dur": 21, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921208, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921209, "dur": 24, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921237, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921241, "dur": 33, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921275, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921277, "dur": 19, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921299, "dur": 1, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921301, "dur": 14, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921318, "dur": 12, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921334, "dur": 10, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921346, "dur": 14, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921362, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921365, "dur": 13, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921382, "dur": 24, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921413, "dur": 3, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921419, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921458, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921462, "dur": 32, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921498, "dur": 2, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921501, "dur": 22, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921525, "dur": 2, "ph": "X", "name": "ProcessMessages 1540", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921528, "dur": 21, "ph": "X", "name": "ReadAsync 1540", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921552, "dur": 15, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921569, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921571, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921589, "dur": 3, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921594, "dur": 17, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921614, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921618, "dur": 14, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921634, "dur": 12, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921649, "dur": 15, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921668, "dur": 16, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921687, "dur": 13, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921704, "dur": 10, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921718, "dur": 23, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921745, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921749, "dur": 19, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921769, "dur": 2, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921772, "dur": 12, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921788, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921806, "dur": 11, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921820, "dur": 14, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921838, "dur": 12, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921853, "dur": 10, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921868, "dur": 12, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921882, "dur": 12, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921896, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921898, "dur": 18, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921919, "dur": 13, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921935, "dur": 14, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921951, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921952, "dur": 28, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631921983, "dur": 13, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922000, "dur": 11, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922014, "dur": 14, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922032, "dur": 14, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922050, "dur": 15, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922067, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922069, "dur": 17, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922089, "dur": 12, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922105, "dur": 11, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922118, "dur": 10, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922132, "dur": 33, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922170, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922174, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922211, "dur": 2, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922216, "dur": 17, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922236, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922254, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922256, "dur": 14, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922273, "dur": 12, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922289, "dur": 14, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922306, "dur": 12, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922320, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922322, "dur": 13, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922338, "dur": 11, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922352, "dur": 9, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922364, "dur": 35, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922402, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922418, "dur": 12, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922434, "dur": 14, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922450, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922452, "dur": 13, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922469, "dur": 13, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922485, "dur": 12, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922498, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922500, "dur": 12, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922515, "dur": 10, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922529, "dur": 33, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922565, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922583, "dur": 11, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922597, "dur": 13, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922612, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922614, "dur": 15, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922630, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922632, "dur": 13, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922647, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922649, "dur": 11, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922663, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922678, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922714, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922729, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922730, "dur": 11, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922743, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922745, "dur": 26, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922774, "dur": 35, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922812, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922830, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922832, "dur": 13, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922848, "dur": 12, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922862, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922864, "dur": 13, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922879, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922881, "dur": 14, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922898, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922900, "dur": 14, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922916, "dur": 11, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922931, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922962, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922982, "dur": 13, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631922999, "dur": 50, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923051, "dur": 2, "ph": "X", "name": "ProcessMessages 1711", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923054, "dur": 14, "ph": "X", "name": "ReadAsync 1711", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923071, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923107, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923126, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923129, "dur": 186, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923321, "dur": 3, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923328, "dur": 44, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923373, "dur": 5, "ph": "X", "name": "ProcessMessages 4497", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923380, "dur": 15, "ph": "X", "name": "ReadAsync 4497", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923398, "dur": 28, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923430, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923443, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923445, "dur": 12, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923461, "dur": 13, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923477, "dur": 12, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923492, "dur": 12, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923507, "dur": 13, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923522, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923524, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923564, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923580, "dur": 14, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923596, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923598, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923614, "dur": 30, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923646, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923661, "dur": 11, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923675, "dur": 11, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923689, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923734, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923753, "dur": 11, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923768, "dur": 12, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923783, "dur": 10, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923797, "dur": 30, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923830, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923846, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923848, "dur": 13, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923865, "dur": 12, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923880, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923909, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923928, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923929, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923954, "dur": 13, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923969, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631923970, "dur": 32, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924005, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924021, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924023, "dur": 12, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924038, "dur": 11, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924052, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924088, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924105, "dur": 27, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924136, "dur": 26, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924164, "dur": 28, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924195, "dur": 22, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924221, "dur": 15, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924240, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924257, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924260, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924283, "dur": 3, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924288, "dur": 20, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924311, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924327, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924329, "dur": 13, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924346, "dur": 11, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924361, "dur": 30, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924393, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924410, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924412, "dur": 15, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924428, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924430, "dur": 12, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924446, "dur": 26, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924475, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924491, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924493, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924510, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924512, "dur": 11, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924526, "dur": 27, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924557, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924572, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924574, "dur": 15, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924592, "dur": 10, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924605, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924634, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924650, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924653, "dur": 12, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924668, "dur": 16, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924686, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924688, "dur": 14, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924705, "dur": 12, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924720, "dur": 12, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924735, "dur": 12, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924750, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924787, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924816, "dur": 3, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924822, "dur": 25, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924851, "dur": 2, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924855, "dur": 23, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924881, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924885, "dur": 23, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924910, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924914, "dur": 16, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924932, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924935, "dur": 18, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924956, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924975, "dur": 19, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631924998, "dur": 21, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925022, "dur": 23, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925049, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925071, "dur": 1, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925074, "dur": 10, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925087, "dur": 27, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925117, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925134, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925135, "dur": 16, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925155, "dur": 31, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925189, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925205, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925208, "dur": 12, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925223, "dur": 11, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925237, "dur": 33, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925273, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925307, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925308, "dur": 13, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925323, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925325, "dur": 34, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925363, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925379, "dur": 28, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925414, "dur": 3, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925419, "dur": 20, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925445, "dur": 44, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925495, "dur": 3, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925501, "dur": 49, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925557, "dur": 4, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925563, "dur": 28, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925597, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925602, "dur": 21, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925626, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925671, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925706, "dur": 2, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925710, "dur": 39, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925752, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925754, "dur": 15, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925771, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925773, "dur": 11, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925787, "dur": 24, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925815, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925817, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925850, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925853, "dur": 19, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925881, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925901, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925904, "dur": 13, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925920, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925923, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925944, "dur": 16, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631925965, "dur": 41, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926009, "dur": 13, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926026, "dur": 11, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926040, "dur": 57, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926103, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926145, "dur": 2, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926149, "dur": 32, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926183, "dur": 2, "ph": "X", "name": "ProcessMessages 1038", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926187, "dur": 18, "ph": "X", "name": "ReadAsync 1038", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926208, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926211, "dur": 18, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926232, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926235, "dur": 20, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926258, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926262, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926320, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926338, "dur": 1, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926340, "dur": 24, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926368, "dur": 22, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926392, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926394, "dur": 20, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926421, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926426, "dur": 26, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926455, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926458, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926480, "dur": 14, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926496, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926497, "dur": 14, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926514, "dur": 13, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926529, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926531, "dur": 12, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926546, "dur": 18, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926566, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926567, "dur": 15, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926586, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926629, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926661, "dur": 2, "ph": "X", "name": "ProcessMessages 903", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926664, "dur": 11, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926679, "dur": 12, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926694, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926697, "dur": 17, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926717, "dur": 12, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926732, "dur": 13, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926748, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926750, "dur": 26, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926779, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926809, "dur": 1, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926812, "dur": 13, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926827, "dur": 13, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926843, "dur": 14, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926861, "dur": 12, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926876, "dur": 10, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926889, "dur": 12, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926904, "dur": 30, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926937, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926954, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926957, "dur": 13, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926973, "dur": 18, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926993, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631926994, "dur": 13, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927011, "dur": 13, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927027, "dur": 10, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927089, "dur": 19, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927110, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927112, "dur": 13, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927127, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927129, "dur": 38, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927170, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927195, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927197, "dur": 12, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927213, "dur": 13, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927229, "dur": 9, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927239, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927242, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927290, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927306, "dur": 12, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927321, "dur": 24, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927350, "dur": 2, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927354, "dur": 27, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927384, "dur": 2, "ph": "X", "name": "ProcessMessages 1121", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927387, "dur": 21, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927411, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927414, "dur": 40, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927462, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927531, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927535, "dur": 30, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927570, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927574, "dur": 23, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927600, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927603, "dur": 19, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927628, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927670, "dur": 4, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927677, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927709, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927712, "dur": 27, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927741, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927744, "dur": 24, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927771, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927774, "dur": 26, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927801, "dur": 2, "ph": "X", "name": "ProcessMessages 1228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927805, "dur": 32, "ph": "X", "name": "ReadAsync 1228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927843, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927848, "dur": 31, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927882, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927887, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927922, "dur": 1, "ph": "X", "name": "ProcessMessages 1130", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927925, "dur": 27, "ph": "X", "name": "ReadAsync 1130", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927957, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927963, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631927997, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928026, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928031, "dur": 22, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928056, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928060, "dur": 114, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928178, "dur": 154, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928339, "dur": 4, "ph": "X", "name": "ProcessMessages 1122", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928345, "dur": 48, "ph": "X", "name": "ReadAsync 1122", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928399, "dur": 4, "ph": "X", "name": "ProcessMessages 2001", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928405, "dur": 26, "ph": "X", "name": "ReadAsync 2001", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928435, "dur": 33, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928473, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928478, "dur": 30, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928509, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928511, "dur": 15, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928529, "dur": 12, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928544, "dur": 20, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928570, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928590, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928592, "dur": 26, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928621, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928642, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928646, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928670, "dur": 32, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928704, "dur": 1, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928706, "dur": 25, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928735, "dur": 19, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928757, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928781, "dur": 1, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928783, "dur": 16, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928803, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928806, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928828, "dur": 16, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928846, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928848, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928866, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928868, "dur": 37, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928908, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928925, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928927, "dur": 13, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928942, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928944, "dur": 25, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928976, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631928981, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929003, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929005, "dur": 19, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929027, "dur": 26, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929056, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929057, "dur": 29, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929090, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929118, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929120, "dur": 13, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929139, "dur": 28, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929170, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929238, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929240, "dur": 180, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929424, "dur": 2, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929428, "dur": 42, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929472, "dur": 2, "ph": "X", "name": "ProcessMessages 3147", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929476, "dur": 18, "ph": "X", "name": "ReadAsync 3147", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929497, "dur": 19, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929519, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929541, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929543, "dur": 36, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929585, "dur": 3, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929590, "dur": 61, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929654, "dur": 2, "ph": "X", "name": "ProcessMessages 774", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929659, "dur": 32, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929694, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929696, "dur": 12, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929713, "dur": 248, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929968, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631929971, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930047, "dur": 4, "ph": "X", "name": "ProcessMessages 3111", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930053, "dur": 18, "ph": "X", "name": "ReadAsync 3111", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930074, "dur": 56, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930136, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930139, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930162, "dur": 1, "ph": "X", "name": "ProcessMessages 1003", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930165, "dur": 34, "ph": "X", "name": "ReadAsync 1003", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930205, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930210, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930257, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930261, "dur": 24, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930288, "dur": 2, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930293, "dur": 23, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930323, "dur": 2, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930327, "dur": 26, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930355, "dur": 2, "ph": "X", "name": "ProcessMessages 1064", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930358, "dur": 27, "ph": "X", "name": "ReadAsync 1064", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930390, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930433, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930435, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930465, "dur": 3, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930470, "dur": 17, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930490, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930493, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930520, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930525, "dur": 32, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930561, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930564, "dur": 17, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930584, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930587, "dur": 18, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930608, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930623, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930627, "dur": 13, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930648, "dur": 42, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930694, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930697, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930728, "dur": 2, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930732, "dur": 25, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930762, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930764, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930793, "dur": 2, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930797, "dur": 15, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930816, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930818, "dur": 23, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930845, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930860, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930862, "dur": 13, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930879, "dur": 14, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930899, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930904, "dur": 19, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930928, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930971, "dur": 3, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631930977, "dur": 31, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931010, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931013, "dur": 21, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931036, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931039, "dur": 27, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931069, "dur": 22, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931094, "dur": 1, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931096, "dur": 11, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931110, "dur": 12, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931125, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931128, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931204, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931207, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931258, "dur": 4, "ph": "X", "name": "ProcessMessages 1113", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931264, "dur": 31, "ph": "X", "name": "ReadAsync 1113", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931298, "dur": 2, "ph": "X", "name": "ProcessMessages 1231", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931302, "dur": 25, "ph": "X", "name": "ReadAsync 1231", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931335, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931356, "dur": 1, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931358, "dur": 29, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931392, "dur": 2, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931396, "dur": 27, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931428, "dur": 4, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931435, "dur": 32, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931470, "dur": 2, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931473, "dur": 15, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931491, "dur": 18, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931513, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931517, "dur": 37, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931558, "dur": 2, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931562, "dur": 44, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931612, "dur": 5, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931618, "dur": 22, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931644, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931666, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931691, "dur": 3, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931696, "dur": 22, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931722, "dur": 12, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931737, "dur": 13, "ph": "X", "name": "ReadAsync 111", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931753, "dur": 14, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931770, "dur": 10, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931783, "dur": 12, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931799, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931816, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931818, "dur": 11, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931833, "dur": 12, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931848, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931887, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931913, "dur": 14, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931930, "dur": 12, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931944, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931946, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631931965, "dur": 34, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932002, "dur": 11, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932016, "dur": 11, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932030, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932072, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932086, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932088, "dur": 13, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932104, "dur": 15, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932122, "dur": 19, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932144, "dur": 19, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932165, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932167, "dur": 11, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932182, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932217, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932237, "dur": 15, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932255, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932258, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932279, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932281, "dur": 12, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932297, "dur": 13, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932313, "dur": 11, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932326, "dur": 15, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932344, "dur": 27, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932375, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932378, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932403, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932405, "dur": 15, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932424, "dur": 16, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932443, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932445, "dur": 15, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932461, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932464, "dur": 12, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932478, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932536, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932540, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932559, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932562, "dur": 119, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932685, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932687, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631932720, "dur": 336, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933062, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933114, "dur": 7, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933123, "dur": 35, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933165, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933170, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933209, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933214, "dur": 32, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933251, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933255, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933296, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933299, "dur": 22, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933325, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933329, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933364, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933369, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933400, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933403, "dur": 30, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933439, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933443, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933477, "dur": 3, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933482, "dur": 48, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933535, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933540, "dur": 48, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933593, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933601, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933648, "dur": 7, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933658, "dur": 38, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933699, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933705, "dur": 42, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933749, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933753, "dur": 30, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933787, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933791, "dur": 61, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933857, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933863, "dur": 42, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933911, "dur": 5, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933918, "dur": 44, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933968, "dur": 3, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631933974, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934024, "dur": 4, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934031, "dur": 38, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934071, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934075, "dur": 37, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934115, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934119, "dur": 37, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934163, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934176, "dur": 179, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934360, "dur": 4, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934367, "dur": 41, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934414, "dur": 5, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934421, "dur": 103, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934529, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934533, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934592, "dur": 4, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934599, "dur": 44, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934650, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934656, "dur": 40, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934701, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934707, "dur": 41, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934755, "dur": 4, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934762, "dur": 44, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934810, "dur": 3, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934815, "dur": 44, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934867, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934873, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934921, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934928, "dur": 41, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934976, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631934982, "dur": 54, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935042, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935048, "dur": 39, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935090, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935094, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935126, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935132, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935169, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935172, "dur": 53, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935229, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935233, "dur": 31, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935269, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631935272, "dur": 4648, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631939932, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631939939, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631940004, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631940010, "dur": 2329, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942349, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942355, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942415, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942421, "dur": 162, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942590, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942596, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942647, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631942660, "dur": 3856, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946527, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946534, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946591, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946597, "dur": 73, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946677, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946681, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946700, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946702, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946962, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946967, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946993, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631946996, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947041, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947047, "dur": 222, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947276, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947280, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947327, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947332, "dur": 77, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947414, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947419, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947458, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947463, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947493, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947496, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947524, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947526, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947551, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947554, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947577, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947618, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947623, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947655, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947708, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947734, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947831, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947835, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947864, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947867, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631947892, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948036, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948065, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948069, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948096, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948099, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948121, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948160, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948195, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948356, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948360, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948394, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948396, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948423, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948530, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948568, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948573, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948600, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948604, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948640, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948644, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948690, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948693, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948726, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948730, "dur": 153, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948887, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948916, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948919, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948965, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631948991, "dur": 379, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949378, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949410, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949413, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949448, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949453, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949485, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949488, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949523, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949528, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949572, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949575, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949621, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949660, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949665, "dur": 32, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949702, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949723, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949725, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949793, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949818, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949822, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949892, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949918, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949921, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949945, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631949963, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950007, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950028, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950030, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950050, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950078, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950082, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950112, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950115, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950188, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950209, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950260, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950264, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950299, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950303, "dur": 17, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950322, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950324, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950351, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950355, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950379, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950382, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950418, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950422, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950445, "dur": 129, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950578, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950613, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950617, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950643, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950688, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950691, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950711, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950713, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950787, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950811, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950814, "dur": 116, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950938, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950943, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950978, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631950981, "dur": 81, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951066, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951093, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951097, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951123, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951143, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951145, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951164, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951166, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951204, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951228, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951250, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951270, "dur": 111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951387, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951391, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951420, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951423, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951448, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951653, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951655, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951696, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951701, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951762, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951765, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951796, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951799, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951887, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951891, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951915, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951919, "dur": 67, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631951990, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952007, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952026, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952042, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952095, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952121, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952126, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952152, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952155, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952252, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952256, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952296, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952300, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952339, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952344, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952504, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952539, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952542, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952564, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952567, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952588, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952591, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952617, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952620, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952639, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952642, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952659, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952662, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952679, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952682, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952719, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952724, "dur": 131, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952859, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952862, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952883, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952886, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952941, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952945, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952963, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952965, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631952990, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953035, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953061, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953063, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953087, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953090, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953114, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953117, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953134, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953136, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953172, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953196, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953199, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953299, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953303, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953346, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953351, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953392, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953397, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953436, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953441, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953473, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953477, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953510, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953515, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953543, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953546, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953620, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953636, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953723, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953749, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953753, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953791, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953797, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953836, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953841, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953867, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953870, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953895, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953899, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953947, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631953953, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954037, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954040, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954073, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954075, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954095, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954097, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954130, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954135, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954155, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954306, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954326, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954329, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954627, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954630, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954670, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954673, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954707, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954711, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954739, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954742, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954773, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954777, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954797, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954801, "dur": 17, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954821, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954823, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954884, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954888, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954909, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631954912, "dur": 174, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631955090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631955092, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631955110, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631955113, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631955177, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631955181, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631955219, "dur": 961, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956188, "dur": 65, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956261, "dur": 5, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956268, "dur": 512, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956787, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956792, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956832, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956837, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956935, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956941, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956964, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631956967, "dur": 544, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957515, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957518, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957549, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957553, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957605, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957609, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957722, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957738, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957740, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957762, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957767, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631957789, "dur": 329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958122, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958143, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958148, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958169, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958170, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958195, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958219, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336631958225, "dur": 414034, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336632372277, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336632372285, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336632372421, "dur": 1681, "ph": "X", "name": "ProcessMessages 4374", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336632374111, "dur": 3648287, "ph": "X", "name": "ReadAsync 4374", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636022411, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636022417, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636022475, "dur": 29, "ph": "X", "name": "ProcessMessages 9274", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636022507, "dur": 29684, "ph": "X", "name": "ReadAsync 9274", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636052201, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636052208, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636052258, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636052264, "dur": 148104, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636200377, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636200383, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636200452, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636200461, "dur": 124577, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636325048, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636325054, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636325107, "dur": 26, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636325136, "dur": 5841, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636330986, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636330992, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636331039, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636331043, "dur": 2773, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636333827, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636333841, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636333904, "dur": 31, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636333938, "dur": 38190, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372139, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372145, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372205, "dur": 2, "ph": "X", "name": "ProcessMessages 23660", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372234, "dur": 37, "ph": "X", "name": "ReadAsync 23660", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372274, "dur": 1, "ph": "X", "name": "ProcessMessages 97427", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372277, "dur": 25, "ph": "X", "name": "ReadAsync 97427", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372306, "dur": 31, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636372340, "dur": 7293, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636379643, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636379649, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636379700, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636379706, "dur": 99171, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636478891, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636478900, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636478976, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636478984, "dur": 2628, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636481627, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636481635, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636481698, "dur": 39, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636481741, "dur": 85897, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636567652, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636567660, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636567716, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636567725, "dur": 24728, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636592463, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636592470, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636592541, "dur": 37, "ph": "X", "name": "ProcessMessages 1818", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636592583, "dur": 127266, "ph": "X", "name": "ReadAsync 1818", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636719857, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636719865, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636719906, "dur": 41, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636719950, "dur": 3755, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636723716, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636723722, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636723765, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636723771, "dur": 408, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636724189, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636724195, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636724238, "dur": 30, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636724271, "dur": 118590, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636842871, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636842884, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636842946, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636842955, "dur": 106043, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949012, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949021, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949071, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949090, "dur": 540, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949637, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949642, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949705, "dur": 35, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636949744, "dur": 29133, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636978887, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636978894, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636978938, "dur": 32, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636978974, "dur": 3561, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636982542, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636982547, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636982583, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636982588, "dur": 449, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636983045, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636983051, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636983092, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336636983122, "dur": 293588, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637276723, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637276733, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637276775, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637276784, "dur": 643, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637277438, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637277445, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637277482, "dur": 28, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637277512, "dur": 780, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637278300, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637278305, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637278433, "dur": 632, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 17736, "tid": 12884901888, "ts": 1751336637279073, "dur": 15382, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 17736, "tid": 2165, "ts": 1751336637314509, "dur": 3805, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 17736, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 17736, "tid": 8589934592, "ts": 1751336631874280, "dur": 534101, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 17736, "tid": 8589934592, "ts": 1751336632408385, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 17736, "tid": 8589934592, "ts": 1751336632408392, "dur": 780, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 17736, "tid": 2165, "ts": 1751336637318317, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 17736, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 17736, "tid": 4294967296, "ts": 1751336631784203, "dur": 5512022, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 17736, "tid": 4294967296, "ts": 1751336631794496, "dur": 61904, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 17736, "tid": 4294967296, "ts": 1751336637296336, "dur": 7491, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 17736, "tid": 4294967296, "ts": 1751336637300323, "dur": 80, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 17736, "tid": 4294967296, "ts": 1751336637303924, "dur": 25, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 17736, "tid": 2165, "ts": 1751336637318333, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751336631910391, "dur": 1730, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336631912130, "dur": 1414, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336631913676, "dur": 141, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751336631913817, "dur": 911, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336631915461, "dur": 384, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_8B8C2A5D7E21B518.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751336631916239, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_11F785D0C1C4CFF6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751336631914746, "dur": 18637, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336631933399, "dur": 5344928, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336637278329, "dur": 268, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336637278597, "dur": 61, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336637279002, "dur": 91, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336637279121, "dur": 8524, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751336631914413, "dur": 19016, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631933435, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_77012D7F45F75688.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631933600, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631933780, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_58764E3BED2F1349.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631933881, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1DAAD3ADBE6389DE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631934043, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_1DAAD3ADBE6389DE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631934253, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751336631934410, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751336631934515, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631934574, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751336631935092, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631935410, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631935819, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10266345212867571173.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751336631936010, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631936433, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631937055, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631937939, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631938453, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631938946, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631939655, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631940351, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631941118, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631942012, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631942903, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631943683, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631944352, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631944952, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631945621, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631946682, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631947369, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631947900, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631949203, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631949465, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751336631950560, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631950628, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631950735, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631951015, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631951085, "dur": 1168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751336631952253, "dur": 1157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631953416, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631953476, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631953593, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631953692, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751336631954119, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631954195, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631954252, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631954637, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631954770, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631954947, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751336631955428, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631955546, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631955628, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631955726, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751336631956131, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631956261, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631956365, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751336631956663, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631956750, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751336631956821, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751336631957022, "dur": 1406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336631958429, "dur": 138155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336632096586, "dur": 3419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751336632100006, "dur": 868, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336632100882, "dur": 4475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751336632105358, "dur": 818, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336632106182, "dur": 6242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751336632112425, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751336632112494, "dur": 5165822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631914371, "dur": 19035, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631933432, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631933595, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631933700, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631933817, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_3D48DF43E878CE71.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751336631934304, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_CE65D8670DA73F5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751336631934525, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631935040, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751336631935183, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631935563, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631935926, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751336631935980, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631937393, "dur": 694, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Editor\\AssetBundleCollector\\CollectRules\\IAddressRule.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751336631936791, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631938206, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631938412, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631939149, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631939842, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631940328, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631941023, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631941589, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631942138, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631942780, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631943090, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631943685, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631944543, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631945534, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631946293, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631946746, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631947376, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631947930, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751336631948223, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631948907, "dur": 1988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751336631950896, "dur": 1096, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631951996, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751336631953037, "dur": 1890, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631954930, "dur": 1243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631956174, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631956270, "dur": 2178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336631958448, "dur": 138938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336632097387, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751336632100342, "dur": 3635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336632103983, "dur": 7033, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751336632111017, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336632111141, "dur": 782, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336632111927, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751336632112524, "dur": 5165844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631914389, "dur": 19028, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631933431, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631933619, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631933885, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631934232, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_881974EA7212F31B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751336631934376, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751336631934649, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631934941, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.Public.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751336631935129, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631935469, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751336631935555, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631935961, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631936213, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631936559, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631937386, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.code-philosophy.hybridclr\\Editor\\3rds\\7zip\\Compress\\LZMA\\LzmaEncoder.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751336631937128, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631937912, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631938288, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631939334, "dur": 1011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631940345, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631941005, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631941824, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631942601, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631943185, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631943877, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631944476, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631945142, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631945755, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631946322, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631946639, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631947362, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631947886, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751336631948757, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631948923, "dur": 3161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751336631952085, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631952263, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751336631952594, "dur": 1420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751336631954015, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631954189, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751336631954366, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751336631954736, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631954886, "dur": 1297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631956184, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631956267, "dur": 2175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336631958442, "dur": 137992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336632096434, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/YooAsset.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751336632098684, "dur": 1412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336632100101, "dur": 6043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityGameFramework.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751336632106145, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336632106523, "dur": 5796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751336632112321, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751336632112448, "dur": 5165870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631914603, "dur": 19045, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631933755, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631933892, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631934310, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751336631934462, "dur": 8618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751336631943081, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631943213, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751336631943416, "dur": 3633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751336631947050, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631947404, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751336631947511, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751336631947737, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631947904, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751336631948107, "dur": 1599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751336631949707, "dur": 756, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631950478, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631950543, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751336631950893, "dur": 2272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751336631953166, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631953560, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751336631953950, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631954010, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751336631954511, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631954623, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751336631955108, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631955524, "dur": 227, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336631956046, "dur": 136944, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1751336632094471, "dur": 1237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751336632095709, "dur": 1311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336632097024, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751336632099780, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336632100094, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751336632102801, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336632102876, "dur": 3680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751336632106557, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336632106640, "dur": 4995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751336632111636, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336632111745, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751336632112446, "dur": 5165869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631914438, "dur": 18999, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631933443, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_BEE963F0D57B0FA6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751336631933575, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631933859, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631934500, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751336631934669, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631934848, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Mosframe.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751336631935011, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Game.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751336631935113, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631935426, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751336631935521, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631935618, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9775225882482023368.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751336631935823, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18430889845441911807.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751336631936021, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631936427, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631937158, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631938053, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631938597, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631939256, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631940030, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631940457, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631941051, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631941457, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631941705, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631942605, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631943087, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631943692, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631944241, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631944924, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631945848, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631946557, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631947388, "dur": 511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631947899, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751336631948137, "dur": 963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751336631949101, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631949528, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631949725, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751336631950069, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631950213, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751336631951869, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631952251, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751336631952637, "dur": 720, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631953358, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631953572, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631954623, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631954776, "dur": 1384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631956160, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631956254, "dur": 2157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336631958413, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751336631958606, "dur": 135864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336632094472, "dur": 2299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751336632096772, "dur": 869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336632097645, "dur": 3906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751336632101552, "dur": 1632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336632103187, "dur": 3478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/YooAsset.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751336632106666, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336632106914, "dur": 5446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751336632112361, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751336632112455, "dur": 5165852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631914463, "dur": 18982, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631933453, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_78B89FCDC6642218.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751336631933830, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631934546, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Profiling.Core.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751336631934752, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751336631934866, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751336631935177, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631935573, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751336631935852, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/345768040152562728.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751336631935982, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631936501, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631937054, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631937302, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631938362, "dur": 159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631938521, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631938943, "dur": 373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631939317, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631940388, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631941353, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631941809, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631942512, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631943126, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631943709, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631944621, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631945242, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631946164, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631946232, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631946832, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631947382, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631948121, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751336631948540, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751336631949739, "dur": 1351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631951144, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751336631952777, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631952940, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631953556, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751336631953787, "dur": 834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751336631954622, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631954785, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751336631954972, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751336631955409, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631955544, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751336631955629, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751336631955849, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631955942, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631956165, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631956258, "dur": 2189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336631958448, "dur": 136025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632094474, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751336632096992, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632097418, "dur": 5187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityGameFramework.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751336632102606, "dur": 1177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632103790, "dur": 4071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751336632107862, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632108206, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632108481, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632108587, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632108667, "dur": 2038, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632110718, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632110790, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632111101, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632111183, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632111296, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632111419, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632111664, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751336632112242, "dur": 5166088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631914496, "dur": 18958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631933461, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_33CE7EEC29BAD477.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751336631933892, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631934195, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631934247, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_C9E7F9647D79AD26.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751336631934347, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751336631934490, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751336631934637, "dur": 5910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751336631940549, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631940738, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631940834, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631941515, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631942186, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631942829, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631943515, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631944168, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631945008, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631945662, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631946606, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631947393, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631947906, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751336631948173, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631948407, "dur": 1316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751336631949724, "dur": 518, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631950271, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631950476, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751336631950959, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631951099, "dur": 1320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751336631952420, "dur": 1087, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631953567, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631954629, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631954772, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631955545, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631956168, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631956274, "dur": 2157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336631958431, "dur": 136046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336632094478, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751336632096839, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336632097087, "dur": 4211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751336632101299, "dur": 851, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336632102157, "dur": 3368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751336632105577, "dur": 6098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751336632111676, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336632111753, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336632111891, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751336632112442, "dur": 5165884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631914522, "dur": 18942, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631933472, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_E1E71A410CED3589.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751336631933862, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631934040, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631934264, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631934532, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751336631934715, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631935020, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751336631935167, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631935478, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751336631935654, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/511321666269118904.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751336631935956, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631936397, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631936692, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631937321, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework.performance\\Editor\\PerformanceTestBuildAssemblyFilter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751336631937012, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631937978, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631938328, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631939078, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631939961, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631940212, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631941076, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631941985, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631942710, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631943522, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631944354, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631945110, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631945767, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631946529, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631947384, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631947930, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751336631948384, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631948460, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751336631948878, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631948943, "dur": 1388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751336631950332, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631950452, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751336631950685, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631951095, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751336631952428, "dur": 1496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631953959, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631954646, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631954789, "dur": 1382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631956171, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631956262, "dur": 2183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336631958445, "dur": 136037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336632094483, "dur": 2446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751336632096930, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336632097084, "dur": 4696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751336632101781, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336632101850, "dur": 3752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/SingularityGroup.HotReload.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751336632105603, "dur": 1430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336632107039, "dur": 5308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/AmplifyShaderEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751336632112349, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751336632112443, "dur": 5165909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631914536, "dur": 18936, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631933475, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_69064AC5249257A6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751336631933830, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_448B8ED848F44F0A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751336631934249, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751336631934441, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751336631934711, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631934864, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751336631935024, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751336631935077, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631935402, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751336631935604, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9925002852875113866.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751336631935657, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631935723, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14262630423516740202.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751336631935774, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631935968, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631936441, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631936878, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631937375, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631938659, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631939197, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631940204, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631941451, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631941983, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631942528, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631943197, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631943752, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631944326, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631944885, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631945873, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631946668, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631947379, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631947917, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751336631948476, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631948667, "dur": 1466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751336631950134, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631950342, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751336631950981, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631951203, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751336631952176, "dur": 644, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631952827, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751336631953312, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631953425, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631953564, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631954633, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631954781, "dur": 1376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631956158, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751336631956331, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751336631956740, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631956819, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631956878, "dur": 2220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336631959098, "dur": 137333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336632096432, "dur": 3998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751336632100431, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336632100544, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Coffee.UIParticle.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751336632103429, "dur": 1182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336632104639, "dur": 6657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/HybridCLR.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751336632111297, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336632111653, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336632112042, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751336632112453, "dur": 5165857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631914555, "dur": 18923, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631933490, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_68B4DCDA9586A490.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751336631933893, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_48FCA722B5EE0F32.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751336631934320, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751336631934496, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631935018, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751336631935110, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631935814, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1596698183737856109.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751336631935900, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7908171937365489455.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751336631935977, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631936307, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631936984, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631937376, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631938730, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631939591, "dur": 890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631940481, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631941266, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631941814, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631942709, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631943236, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631944115, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631944968, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631945864, "dur": 798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631946663, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631947368, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631947909, "dur": 1381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751336631949291, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631949353, "dur": 1594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751336631950948, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631951051, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631951528, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751336631951975, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751336631953371, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631953445, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631953867, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631954637, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631954785, "dur": 1377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631956162, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631956252, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751336631956430, "dur": 2016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336631958446, "dur": 136063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632094510, "dur": 2024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.MemoryProfiler.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751336632096534, "dur": 1787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632098328, "dur": 4283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751336632102612, "dur": 1721, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632104340, "dur": 4185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751336632108526, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632108641, "dur": 2043, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632110827, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632111322, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632111661, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632112061, "dur": 297949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632411306, "dur": 90, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 10, "ts": 1751336632411396, "dur": 499, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Program/Unity/Editor/6000.0.28f1c1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 10, "ts": 1751336632410012, "dur": 1905, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751336632411917, "dur": 4866428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631914567, "dur": 18925, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631933745, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631933801, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7188A8B2F60639BF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751336631933859, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_05AA7A44FF2C458E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751336631934262, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631934571, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751336631934643, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751336631934811, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751336631934982, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Game.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751336631935174, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631935632, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631935687, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17854342601108681021.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751336631935741, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631935863, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3925285597375108267.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751336631936022, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631936593, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631936969, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631937319, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631938255, "dur": 1027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631939282, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631939897, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631940512, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631941319, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631942083, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631942757, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631943047, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631943467, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631943949, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631944736, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631945533, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631946299, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631946841, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631947387, "dur": 500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631947892, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751336631948187, "dur": 1065, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631949256, "dur": 1451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751336631950708, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631951284, "dur": 691, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631951982, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631952039, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631953137, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631953561, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631954625, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631954777, "dur": 1385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631956163, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631956256, "dur": 2159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631958423, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751336631958582, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751336631958921, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336631959007, "dur": 137466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632096475, "dur": 3438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/IngameDebugConsole.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751336632099913, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632100270, "dur": 2860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751336632103131, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632103482, "dur": 4262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751336632107745, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632108139, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632108343, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632108739, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632108841, "dur": 1879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632110763, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632110881, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632110969, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632111027, "dur": 604, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632111652, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632111804, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751336632112459, "dur": 5165849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631914594, "dur": 19045, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631933643, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_8BFEBFA7E2066F58.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751336631933862, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631934190, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631934405, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631934964, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751336631935101, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631935479, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751336631935802, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1212262423713005172.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751336631935955, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11221626692241425917.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751336631936027, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631936343, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631936952, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631937358, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\FileSystem\\DefaultCacheFileSystem\\DefaultCacheDownloadCenter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751336631937358, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631938329, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631939350, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631940288, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631940968, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631941895, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631942645, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631943629, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631944323, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631945270, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631946040, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631946762, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631947395, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631947934, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751336631948260, "dur": 1124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631949387, "dur": 1525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336631950913, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631951224, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751336631951645, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631951908, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631952627, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631953576, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751336631953818, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336631954314, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631954672, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631954794, "dur": 1371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631956165, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631956250, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Game.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751336631956380, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631956438, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Game.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336631956896, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631956984, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336631958764, "dur": 196, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336631959650, "dur": 4064574, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336636028933, "dur": 23664, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Game.Hotfix.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751336636028689, "dur": 23991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336636052916, "dur": 61, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336636372890, "dur": 228, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336636053353, "dur": 325154, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336636380038, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751336636379802, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336636380796, "dur": 212487, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751336636594646, "dur": 247444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751336636594640, "dur": 248297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751336636843504, "dur": 124, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751336636843920, "dur": 135769, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751336636983232, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751336636983225, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751336636983349, "dur": 510, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751336636983863, "dur": 294450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631914411, "dur": 19012, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631933428, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_94D2176B11FDF36E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751336631933729, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631933896, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_6D0654A6A14E91C5.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751336631933955, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631934340, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751336631934731, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751336631934902, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631935118, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631935806, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3951265154693766969.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751336631935911, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6581075648720537469.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751336631936004, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631936621, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631937391, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Packages\\com.singularitygroup.hotreload\\Editor\\CompileChecker\\ICompileChecker.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751336631936964, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631938093, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631939055, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631939585, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631940175, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631941228, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631941975, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631942695, "dur": 1051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631943747, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631944328, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631945168, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631946073, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631946803, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631947372, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631948131, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751336631948593, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631948664, "dur": 3151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751336631951815, "dur": 1589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631953407, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751336631954097, "dur": 532, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631954632, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631954778, "dur": 1381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631956159, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631956253, "dur": 2168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336631958422, "dur": 138095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336632096519, "dur": 4197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/SingularityGroup.HotReload.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751336632100716, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336632101010, "dur": 4146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751336632105157, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336632105764, "dur": 6137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751336632111902, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336632112141, "dur": 4218609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751336636330772, "dur": 982, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Game.Hotfix.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751336636330751, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.Hotfix.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751336636331780, "dur": 2895, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Game.Hotfix.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751336636334680, "dur": 943658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631914622, "dur": 19137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631933897, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_6DF54DD596B6ED4D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751336631933962, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631934038, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631934311, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_B7135FF78EF23DD9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751336631934721, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631934810, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751336631934888, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631935016, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751336631935130, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751336631935185, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631935525, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631935731, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6619294975191528769.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751336631935785, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631936002, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631936346, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631937169, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631938349, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631939538, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631940136, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631940443, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631941200, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631941515, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631942040, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631942751, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631943288, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631943787, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631944696, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631945244, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631946079, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631946833, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631947369, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631947920, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751336631948533, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631948921, "dur": 2419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751336631951341, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631951979, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631952299, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751336631952724, "dur": 1531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751336631954256, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631954586, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751336631954735, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751336631955419, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631955474, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751336631955565, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751336631955659, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751336631956041, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751336631956124, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751336631956364, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631956488, "dur": 1950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336631958438, "dur": 136089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336632094528, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751336632095826, "dur": 885, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336632096758, "dur": 3474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751336632100233, "dur": 608, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336632100846, "dur": 3415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751336632104262, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336632104689, "dur": 6184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.MemoryProfiler.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751336632110874, "dur": 559, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336632111628, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336632111893, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751336632112519, "dur": 5165784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631914647, "dur": 19143, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631933893, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631933996, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631934166, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631934440, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751336631934676, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751336631935022, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.MemoryProfilerModule.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751336631935085, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631935479, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.Tests.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751336631935870, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631936012, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631936500, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631936872, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631937389, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\ResourcePackage\\PackageAsset.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751336631937265, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631938431, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631938971, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631939819, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631940531, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631941586, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631941916, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631942599, "dur": 1095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631943694, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631944473, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631945074, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631945681, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631946243, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631946880, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631947366, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631947930, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751336631948610, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631948879, "dur": 1541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751336631950421, "dur": 438, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631950864, "dur": 1719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751336631952584, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631952713, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751336631953458, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631953578, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631954643, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631954787, "dur": 1380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631956167, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631956261, "dur": 2179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336631958440, "dur": 136017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632094458, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751336632096373, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632096461, "dur": 3533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751336632099995, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632100078, "dur": 4155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751336632104233, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632104337, "dur": 6626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751336632110964, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632111036, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632111223, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632111481, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632111662, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336632112126, "dur": 3916566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336636028713, "dur": 170910, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Game.Hotfix.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751336636028694, "dur": 171709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Game.Hotfix.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751336636201023, "dur": 141, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751336636202094, "dur": 123784, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Game.Hotfix.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751336636330752, "dur": 148904, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Game.Hotfix.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751336636330746, "dur": 148911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Game.Hotfix.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751336636479675, "dur": 2753, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Game.Hotfix.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751336636482436, "dur": 795898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631914662, "dur": 19168, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631933895, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_B8655BD4881E8EE6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751336631934078, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631934240, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751336631934413, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751336631934845, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751336631935039, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751336631935130, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751336631935190, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631935518, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631935660, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10316626440694862930.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751336631935921, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3994725896039383208.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751336631936050, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631936585, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631937154, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631937897, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631938094, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631939035, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631939750, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631940064, "dur": 534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631940598, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631941141, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631941723, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631942334, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631942878, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631943282, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631944039, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631945013, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631945551, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631946163, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631946258, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631946843, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631947382, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631947929, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Mosframe.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751336631948125, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631948863, "dur": 1448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Mosframe.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751336631950311, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631950936, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751336631951448, "dur": 1198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751336631952647, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631952946, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631953342, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751336631953481, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751336631953809, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631954287, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631954656, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631954779, "dur": 1396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631956175, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631956262, "dur": 2179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336631958441, "dur": 136012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336632094456, "dur": 1236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751336632095693, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336632095937, "dur": 2643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751336632098631, "dur": 3937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/SingularityGroup.HotReload.Runtime.Public.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751336632102569, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336632102703, "dur": 4292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751336632106996, "dur": 835, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751336632107837, "dur": 4645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/IngameDebugConsole.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751336632112543, "dur": 5165840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631914776, "dur": 19547, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631934780, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Runtime.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751336631934931, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631934986, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751336631935125, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631935478, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631935568, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751336631935916, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11834159886482143544.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751336631935972, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631936601, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631937086, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631937397, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\FileSystem\\DefaultUnpackFileSystem\\DefaultUnpackRemoteServices.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751336631937315, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631938378, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631939408, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631940314, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631940900, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631941466, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631941811, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631942317, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631942731, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631943124, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631943593, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631944256, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631945131, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631945714, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631946202, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631946531, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631947361, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631947887, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751336631948075, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631948141, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751336631949076, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631949187, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_CE3A3B080CE6F7DB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751336631949324, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631949403, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751336631950430, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631950764, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/AmplifyShaderEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751336631951028, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/AmplifyShaderEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751336631953286, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631953347, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631953576, "dur": 1048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631954624, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631954773, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631955612, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631956177, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631956268, "dur": 2161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336631958430, "dur": 138003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632096433, "dur": 1849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1751336632098283, "dur": 2179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632100468, "dur": 3904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1751336632104425, "dur": 3195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1751336632107621, "dur": 582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632108277, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751336632108380, "dur": 2338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632110774, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632111183, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632111522, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632111969, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751336632112550, "dur": 5165806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631914691, "dur": 19182, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631933879, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_8622EB2AD063DE6D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751336631934023, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631934224, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631934354, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751336631934553, "dur": 163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751336631934758, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631934844, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751336631935131, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751336631935199, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631935566, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751336631935844, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17191867709733493891.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751336631935930, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17191867709733493891.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751336631936047, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631936523, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631936791, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631937990, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631938758, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631939610, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631940450, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631940974, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631941620, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631942320, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631942883, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631943200, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631943566, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631944346, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631945072, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631945669, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631946574, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631947361, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631947890, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/GameFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751336631948376, "dur": 2383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/GameFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751336631950760, "dur": 1005, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631951769, "dur": 1288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751336631953057, "dur": 1067, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631954157, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751336631954326, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751336631954721, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631954790, "dur": 1386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631956176, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631956266, "dur": 2171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336631958438, "dur": 137020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336632095460, "dur": 3182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1751336632098643, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336632098731, "dur": 5472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/GameFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1751336632104203, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336632104452, "dur": 7691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1751336632112195, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751336632112269, "dur": 5166051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631914705, "dur": 19188, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631933899, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_2281B71902E16F2A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751336631934041, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_3287111FDB331714.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751336631934416, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751336631934559, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751336631935006, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Mosframe.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751336631935092, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631935657, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14434236700012358782.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751336631935994, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631936408, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631936952, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631937384, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\ResourceManager\\Provider\\CompletedProvider.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751336631937294, "dur": 1369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631938663, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631939393, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631940498, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631941505, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631941899, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631942513, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631943816, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631944904, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631945772, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631946621, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631947367, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631947884, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751336631948103, "dur": 656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751336631948760, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631949363, "dur": 1080, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631950450, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751336631950734, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751336631952985, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631953127, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631953573, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631954644, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631954786, "dur": 1384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631956171, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631956271, "dur": 2161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336631958432, "dur": 138003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336632096436, "dur": 3767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1751336632100203, "dur": 1187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336632101394, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1751336632104114, "dur": 2592, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336632106713, "dur": 5352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1751336632112066, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751336632112451, "dur": 5165866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631914718, "dur": 19191, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631933912, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_134F3A144AD95EA3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751336631934047, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_134F3A144AD95EA3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751336631934264, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751336631934551, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751336631934717, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631934803, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751336631935039, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751336631935192, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631935701, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10064837326866123306.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751336631935828, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6699892698108865450.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751336631935902, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17654094715076075116.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751336631936015, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631936418, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631937383, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Editor\\AssetBundleBuilder\\BuildPipeline\\EditorSimulateBuildPipeline\\BuildTasks\\TaskGetBuildMap_ESBP.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751336631936860, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631937994, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631938294, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631938809, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631939946, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631940185, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631941149, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631941788, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631942543, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631943431, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631943965, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631945002, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631945839, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631946621, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631947364, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631947891, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751336631948085, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631948344, "dur": 1100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751336631949445, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631949520, "dur": 805, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631950367, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751336631950786, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631950890, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751336631951136, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631951762, "dur": 1084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751336631952846, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631953099, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631953570, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631954634, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631954781, "dur": 1385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631956166, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631956257, "dur": 2187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336631958444, "dur": 136170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336632094616, "dur": 3733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/FancyScrollView.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1751336632098350, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336632098745, "dur": 3422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Game.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1751336632102168, "dur": 2526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336632104699, "dur": 4171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1751336632108871, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336632108946, "dur": 3375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751336632112327, "dur": 5165995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631914732, "dur": 19183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631933924, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_BE0BBC97B2860369.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751336631934459, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631934525, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751336631934691, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631934745, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751336631934875, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631935121, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631935483, "dur": 229, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/AmplifyShaderEditor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751336631936006, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631936565, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631936968, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631937391, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Assets\\ThirdParty\\TableView\\ScrollView\\PageView.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751336631937391, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631938689, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631939563, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631940082, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631940608, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631941159, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631941745, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631942120, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631942839, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631943437, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631944065, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631944913, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631945801, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631946650, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631947375, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631947897, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751336631948432, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751336631949359, "dur": 1101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631950469, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631950554, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751336631950785, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751336631951690, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631952099, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631952491, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631953560, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631954627, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631954777, "dur": 1389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631956167, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631956260, "dur": 2160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336631958420, "dur": 138986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632097408, "dur": 4953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1751336632102363, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632102863, "dur": 4740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 21, "ts": 1751336632107605, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632107889, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632108095, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632108179, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632108384, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632108634, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632109006, "dur": 1560, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632110580, "dur": 656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632111246, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632111419, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632111650, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632111865, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751336632112434, "dur": 5165887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631914746, "dur": 19181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631933933, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_0B623C68B67432F5.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751336631934615, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751336631934741, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Runtime.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751336631935091, "dur": 434, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631935528, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631935716, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631935969, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631936328, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631936773, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631937129, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631937966, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631938454, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "D:\\Program\\Unity\\Editor\\6000.0.28f1c1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 22, "ts": 1751336631938379, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631939223, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631939867, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631940377, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631941003, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631941829, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631942821, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631943193, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631943674, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631944646, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631945435, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631946214, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631946832, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631947388, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631947912, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751336631948106, "dur": 1216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751336631949323, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631949797, "dur": 1668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751336631951466, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631951555, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631951624, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751336631951852, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631952504, "dur": 1048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631953553, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751336631953774, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751336631954611, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631954792, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751336631954935, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751336631955389, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631955541, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751336631955637, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751336631956042, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631956163, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751336631956307, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751336631956590, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631956717, "dur": 1733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336631958450, "dur": 136093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632094544, "dur": 1197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/HybridCLR.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1751336632095741, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632095950, "dur": 4245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1751336632100195, "dur": 3325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632103526, "dur": 5191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/FancyScrollView.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 22, "ts": 1751336632108718, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632108800, "dur": 1887, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632110856, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632111411, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632111640, "dur": 568, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336632112210, "dur": 4871018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751336636983250, "dur": 294222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 22, "ts": 1751336636983230, "dur": 294244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 22, "ts": 1751336637277492, "dur": 740, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1751336631914761, "dur": 19174, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631933941, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_13B91445996F2B3C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751336631934308, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751336631934394, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631934481, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751336631934851, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751336631935114, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631935426, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/YooAsset.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751336631935919, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11228397997354669771.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751336631935979, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631936285, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631937030, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631937909, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631938194, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631938575, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631939141, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631939793, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631940289, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631941050, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631941743, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631942351, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631943219, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631943600, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631944359, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631945307, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631946241, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631946846, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631947387, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631947898, "dur": 963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751336631948889, "dur": 1568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.MemoryProfiler.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751336631950457, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631950914, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751336631951316, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631951411, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751336631951702, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631951775, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751336631952673, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631952988, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631953555, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751336631953690, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751336631954117, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631954186, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631954458, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631954636, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631954770, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751336631954923, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751336631955189, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631955464, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751336631955538, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631956164, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631956253, "dur": 2164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336631958418, "dur": 136041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336632094466, "dur": 1889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Coffee.UIParticle.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1751336632096355, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336632096457, "dur": 4845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1751336632101302, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336632101602, "dur": 4016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1751336632105619, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336632106213, "dur": 4998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 23, "ts": 1751336632111212, "dur": 783, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336632112008, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336632112107, "dur": 299813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751336632411920, "dur": 4866458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631914679, "dur": 19186, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631933900, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631933977, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B3DDABABB0A5567E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751336631934054, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_B3DDABABB0A5567E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751336631934236, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631934385, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631934488, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631935116, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631935601, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17165269797377373369.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751336631935817, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9655524186228112463.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751336631935905, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4571106892069148322.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751336631936009, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631936477, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631936921, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631937876, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631938161, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631939201, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631939756, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631940069, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631940336, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631941322, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631942050, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631942727, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631943094, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631943442, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631944412, "dur": 527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631944939, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631945771, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631946540, "dur": 818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631947378, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631947918, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751336631948237, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751336631948721, "dur": 1378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751336631950099, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631950209, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.Public.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751336631950510, "dur": 940, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631951453, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.Public.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751336631952275, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631952872, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751336631953433, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/SingularityGroup.HotReload.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751336631953900, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631954324, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631954649, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631954793, "dur": 1367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631956161, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631956258, "dur": 2161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336631958419, "dur": 138964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336632097384, "dur": 5275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1751336632102660, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336632103058, "dur": 4446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1751336632107505, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336632107782, "dur": 4637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 24, "ts": 1751336632112420, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751336632112521, "dur": 5165784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631914796, "dur": 19347, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631934434, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1751336631934730, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1751336631935037, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1751336631935104, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631935440, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631935582, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631936013, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631936388, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631936703, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631937022, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631937863, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631938388, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631938818, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631939328, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631940301, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631941062, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631941656, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631942665, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631943445, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631944176, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631944965, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631945618, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631946087, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631946590, "dur": 781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631947371, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631947910, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1751336631948361, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631948683, "dur": 1898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1751336631950581, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631951273, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1751336631951623, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1751336631951837, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631951943, "dur": 1049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1751336631952993, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631953432, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631953569, "dur": 1057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631954626, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631954783, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631956157, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1751336631956259, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631956365, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1751336631957517, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631957633, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1751336631957758, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1751336631958258, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631958425, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1751336631958556, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1751336631958762, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336631959041, "dur": 138341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632097391, "dur": 5349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1751336632102741, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632103140, "dur": 4881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 25, "ts": 1751336632108022, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632108287, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632108559, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632108622, "dur": 2063, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632110737, "dur": 743, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632111607, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632112049, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1751336632112559, "dur": 5165831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631914812, "dur": 19451, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631934342, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_EE393157BAF537B8.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1751336631934502, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/GameFramework.rsp2"}}, {"pid": 12345, "tid": 26, "ts": 1751336631934938, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1751336631935102, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631935418, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Game.Hotfix.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1751336631935587, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631935964, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631936271, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631936660, "dur": 409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631937069, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631937396, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Runtime\\OperationSystem\\EOperationStatus.cs"}}, {"pid": 12345, "tid": 26, "ts": 1751336631937296, "dur": 1013, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631938310, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631939329, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631940324, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631940989, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631942025, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631942695, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631943484, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631944289, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631945004, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631945702, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631946244, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631946908, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631947365, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631947902, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityGameFramework.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1751336631948145, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1751336631948343, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1751336631948627, "dur": 584, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631949216, "dur": 1548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1751336631950764, "dur": 1222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631952041, "dur": 1517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631953558, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631954628, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631954772, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1751336631954968, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1751336631955464, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631955567, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1751336631955725, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1751336631956121, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631956310, "dur": 2113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336631958423, "dur": 138017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336632096441, "dur": 2734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UIEffect.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1751336632099175, "dur": 3793, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336632102976, "dur": 3490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 26, "ts": 1751336632106467, "dur": 4651, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336632111193, "dur": 928, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336632112149, "dur": 4612228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1751336636724398, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1751336636724378, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1751336636724508, "dur": 500, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1751336636725012, "dur": 553384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631914835, "dur": 19404, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631934353, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1751336631934662, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631934740, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1751336631934873, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631934930, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1751336631935122, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631935394, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/AmplifyShaderEditor.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1751336631935516, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631935667, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6237039172248439883.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1751336631935778, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631935966, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631936442, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631937390, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.tuyoogame.yooasset\\Editor\\AssetBundleBuilder\\EBuildinFileCopyOption.cs"}}, {"pid": 12345, "tid": 27, "ts": 1751336631936812, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631937923, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631938581, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631939000, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631939353, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631940489, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631941118, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631941577, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631941806, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631942559, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631943452, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631944253, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631945032, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631945979, "dur": 751, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631946731, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631947374, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631947927, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1751336631948317, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1751336631950422, "dur": 679, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631951107, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631951187, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631951253, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1751336631951610, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631951771, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1751336631952825, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631953081, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631953578, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631954628, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631954774, "dur": 1399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631956173, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631956260, "dur": 2153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336631958415, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1751336631958592, "dur": 138883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336632097475, "dur": 3112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1751336632100588, "dur": 778, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336632101371, "dur": 3377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UIEffect-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1751336632104749, "dur": 1778, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336632106534, "dur": 5408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1751336632111943, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336632112040, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336632112148, "dur": 4267657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336636379833, "dur": 186739, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1751336636379807, "dur": 187840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1751336636568276, "dur": 145, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1751336636568713, "dur": 151950, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 27, "ts": 1751336636724381, "dur": 225363, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1751336636724374, "dur": 225371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1751336636949762, "dur": 694, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1751336636950461, "dur": 327864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631914854, "dur": 19459, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631934353, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 28, "ts": 1751336631934842, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Game.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1751336631935101, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631935404, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Coffee.UIParticle.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1751336631935550, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631935864, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631935984, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631936594, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631936830, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631937380, "dur": 889, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631938269, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631938486, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631939268, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631939645, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631940617, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631941312, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631942017, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631942596, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631943214, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631943484, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631944070, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631944796, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631945360, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631945995, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631946696, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631947370, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631947915, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1751336631948440, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631949001, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1751336631950210, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631950466, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/HybridCLR.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1751336631950740, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1751336631951157, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1751336631952220, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631952714, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631953559, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631954635, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631954768, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1751336631954856, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631955149, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1751336631955469, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1751336631955575, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631956172, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631956267, "dur": 2156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336631958424, "dur": 138987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336632097412, "dur": 4172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Mosframe.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1751336632101585, "dur": 1221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336632102810, "dur": 3682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.EditorCoroutines.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1751336632106492, "dur": 1340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336632107842, "dur": 4597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 28, "ts": 1751336632112440, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1751336632112512, "dur": 5165799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751336637293361, "dur": 1531, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 17736, "tid": 2165, "ts": 1751336637319267, "dur": 25629, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 17736, "tid": 2165, "ts": 1751336637345267, "dur": 2366, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 17736, "tid": 2165, "ts": 1751336637311696, "dur": 37580, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}