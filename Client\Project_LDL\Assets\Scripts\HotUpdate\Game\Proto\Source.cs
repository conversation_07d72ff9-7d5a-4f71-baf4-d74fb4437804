// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: source.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Source {

  /// <summary>Holder for reflection information generated from source.proto</summary>
  public static partial class SourceReflection {

    #region Descriptor
    /// <summary>File descriptor for source.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static SourceReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "Cgxzb3VyY2UucHJvdG8SBnNvdXJjZSqSDwoGU291cmNlEgcKA05pbBAAEggK",
            "BFRlc3QQARIGCgJHTRACEg4KCkRhaWx5UmVzZXQQCxIPCgtXZWVrbHlSZXNl",
            "dBAMEhAKDE1vbnRobHlSZXNldBANEg8KClJvbGVDcmVhdGUQ6QcSDgoJUm9s",
            "ZUxvZ2luEOoHEgwKB0l0ZW1Vc2UQsAkSDAoHSXRlbUJ1eRCxCRIYChNJbm5l",
            "ckNpdHlPY2N1cHlHcmlkEPkKEhUKEEhlcm9VcGdyYWRlTGV2ZWwQ0Q8SFAoP",
            "SGVyb1VwZ3JhZGVTdGFyENIPEhUKEEhlcm9VcGdyYWRlU2tpbGwQ0w8SEwoO",
            "SGVyb0hvbm9yTGV2ZWwQ1A8SEgoNSGVyb1N5bnRoZXRpYxDVDxIQCgtIZXJv",
            "TWFwU2hvdxDWDxIQCgtFcXVpcFRha2VPbhCZERIRCgxFcXVpcFRha2VPZmYQ",
            "mhESFgoRRXF1aXBMZXZlbFVwZ3JhZGUQmxESEwoORXF1aXBQcm9tb3Rpb24Q",
            "nBESEQoMRXF1aXBSZXBsYWNlEJ0REhEKDEVxdWlwUHJvZHVjZRCeERIRCgxF",
            "cXVpcFJlc29sdmUQnxESGwoWRXF1aXBNYXRlcmlhbFN5bnRoZXNpcxCgERIZ",
            "ChRFcXVpcE1hdGVyaWFsUmVzb2x2ZRChERIUCg9RdWV1ZUFjY2VsZXJhdGUQ",
            "/RESEAoLQnVpbGRDcmVhdGUQ/hESEQoMQnVpbGRVcGdyYWRlEP8REhMKDlN1",
            "cnZpdm9yU3VtbW9uEKkUEhMKDlN1cnZpdm9yU3RhclVwEKoUEhQKD1NvbGRp",
            "ZXJUcmFpbmluZxCNFRIVChBTb2xkaWVyUHJvbW90aW9uEI4VEhUKEFNvbGRp",
            "ZXJUcmVhdG1lbnQQjxUSEgoNV29ya2VyUmVudGluZxDxFRIOCglUZWNoU3R1",
            "ZHkQ1RYSDwoKVWF2VXBncmFkZRC5FxIZChRVYXZPbmVDbGlja1N5bnRoZXNp",
            "cxC6FxIQCgtVYXZSZXNlYXJjaBC7FxISCg1TdG9yZUJ1eUdvb2RzEJ0YEhsK",
            "FlNob3BEYWlseURlYWxDbGFpbUZyZWUQgRkSGwoWU2hvcERhaWx5TXVzdEhh",
            "dmVDbGFpbRCCGRIXChJTaG9wTW9udGhDYXJkQ2xhaW0QgxkSGgoVU2hvcFdl",
            "ZWtDYXJkQ2xhaW1GcmVlEIQZEhYKEVNob3BXZWVrQ2FyZENsYWltEIUZEhYK",
            "EVNob3BEYXduRnVuZENsYWltEIYZEhkKFFZpcFJlY2VpdmVEYWlseVBvaW50",
            "EMkaEhgKE1ZpcFJlY2VpdmVEYWlseUdpZnQQyhoSFgoRVHJhZGVHb29kc1Jl",
            "d2FyZHMQrRsSFAoPVHJhZGVSb2JSZXdhcmRzEK4bEhQKD1RyYWRlUmVmcmVz",
            "aFZhbhCvGxIYChNUcmFkZURvbmF0ZUNvbnRyYWN0ELAbEh0KGFRyYWRlUmVj",
            "ZWl2ZURvbmF0ZVJld2FyZBCxGxIWChFUcmFkZVJlZnJlc2hUcmFpbhCyGxIS",
            "Cg1UcmFkZVRyYWluUm9iELMbEhUKEFRyYWRlU3VtbW9uVHJhaW4QtBsSFAoP",
            "VW5pb25DcmVhdGVDb3N0EKEfEhQKD1VuaW9uR2lmdFJld2FyZBCiHxIZChRV",
            "bmlvbk1pbGVzdG9uZVJld2FyZBCjHxIkCh9Vbmlvbk1pbGVzdG9uZUtpY2tP",
            "dXRSZXdhcmRNYWlsEKQfEiMKHlVuaW9uTWlsZXN0b25lRmluaXNoUmV3YXJk",
            "TWFpbBClHxIkCh9Vbmlvbk1pbGVzdG9uZUltcGVhY2hSZXdhcmRNYWlsEKYf",
            "EhsKFlVuaW9uVGVjaERvbmF0ZVJld2FyZHMQpx8SEgoNTm92aWNlUmVmcmVz",
            "aBCJJxIXChJOb3ZpY2VCdXlDaGFsbGVuZ2UQiicSFgoRTm92aWNlQWNoaWV2",
            "ZURyYXcQiycSGAoTRHVuZ2VvblNldHRsZVJld2FyZBC1KRIVChBEdW5nZW9u",
            "Qm94UmV3YXJkELYpEhAKC1Rhc2tSZWNlaXZlEOYpEhsKFlRhc2tTY29yZVJl",
            "d2FyZFJlY2VpdmUQ5ykSDgoJVGFza1Jlc2V0EOgpEg0KCFB1cmNoYXNlEPEu",
            "EhoKFVB1cmNoYXNlU2hvcERhaWx5RGVhbBDyLhIeChlQdXJjaGFzZVNob3BE",
            "YWlseU11c3RIYXZlEPMuEhsKFlB1cmNoYXNlU2hvcFdlZWtseURlYWwQ9C4S",
            "GgoVUHVyY2hhc2VTaG9wTW9udGhDYXJkEPUuEhsKFlB1cmNoYXNlU2hvcFdl",
            "ZWtseUNhcmQQ9i4SHQoYUHVyY2hhc2VTaG9wR2lmdFBhY2tNYWxsEPcuEhkK",
            "FFB1cmNoYXNlU2hvcERhd25GdW5kEPguEhwKF1B1cmNoYXNlU2hvcERpYW1v",
            "bmRTaG9wEPkuEh4KGVB1cmNoYXNlU2hvcFdlZWtseUNhcmRBbGwQ+i4SFwoS",
            "UHVyY2hhc2VCdWlsZFF1ZXVlEPsuEhwKF1B1cmNoYXNlVGVjaG5vbG9neVF1",
            "ZXVlEPwuEhQKD1B1cmNoYXNlVklQR2lmdBD9LkIZWhdzZXJ2ZXIvYXBpL3Bi",
            "L3BiX3NvdXJjZWIGcHJvdG8z"));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Source.Source), }, null, null));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  /// 操作来源
  /// </summary>
  public enum Source {
    [pbr::OriginalName("Nil")] Nil = 0,
    /// <summary>
    /// 1 ~ 999 系统
    /// </summary>
    [pbr::OriginalName("Test")] Test = 1,
    /// <summary>
    /// 开挂
    /// </summary>
    [pbr::OriginalName("GM")] Gm = 2,
    /// <summary>
    /// 每日重置
    /// </summary>
    [pbr::OriginalName("DailyReset")] DailyReset = 11,
    /// <summary>
    /// 每周重置
    /// </summary>
    [pbr::OriginalName("WeeklyReset")] WeeklyReset = 12,
    /// <summary>
    /// 每月重置
    /// </summary>
    [pbr::OriginalName("MonthlyReset")] MonthlyReset = 13,
    /// <summary>
    /// 1000 ~ 1199 角色
    /// </summary>
    [pbr::OriginalName("RoleCreate")] RoleCreate = 1001,
    /// <summary>
    /// 角色登录
    /// </summary>
    [pbr::OriginalName("RoleLogin")] RoleLogin = 1002,
    /// <summary>
    /// 1200 ~ 1399 道具
    /// </summary>
    [pbr::OriginalName("ItemUse")] ItemUse = 1200,
    [pbr::OriginalName("ItemBuy")] ItemBuy = 1201,
    /// <summary>
    /// 1400 ~ 1449 城内地图
    /// </summary>
    [pbr::OriginalName("InnerCityOccupyGrid")] InnerCityOccupyGrid = 1401,
    /// <summary>
    /// 2000 ~ 2199 英雄
    /// </summary>
    [pbr::OriginalName("HeroUpgradeLevel")] HeroUpgradeLevel = 2001,
    /// <summary>
    /// 英雄升星
    /// </summary>
    [pbr::OriginalName("HeroUpgradeStar")] HeroUpgradeStar = 2002,
    /// <summary>
    /// 英雄升技能
    /// </summary>
    [pbr::OriginalName("HeroUpgradeSkill")] HeroUpgradeSkill = 2003,
    /// <summary>
    /// 英雄晋升等级
    /// </summary>
    [pbr::OriginalName("HeroHonorLevel")] HeroHonorLevel = 2004,
    /// <summary>
    /// 英雄合成
    /// </summary>
    [pbr::OriginalName("HeroSynthetic")] HeroSynthetic = 2005,
    /// <summary>
    /// 英雄空投
    /// </summary>
    [pbr::OriginalName("HeroMapShow")] HeroMapShow = 2006,
    /// <summary>
    /// 2200 ~ 2299 装备
    /// </summary>
    [pbr::OriginalName("EquipTakeOn")] EquipTakeOn = 2201,
    /// <summary>
    /// 装备脱下
    /// </summary>
    [pbr::OriginalName("EquipTakeOff")] EquipTakeOff = 2202,
    /// <summary>
    /// 装备升级
    /// </summary>
    [pbr::OriginalName("EquipLevelUpgrade")] EquipLevelUpgrade = 2203,
    /// <summary>
    /// 装备晋升
    /// </summary>
    [pbr::OriginalName("EquipPromotion")] EquipPromotion = 2204,
    /// <summary>
    /// 装备替换
    /// </summary>
    [pbr::OriginalName("EquipReplace")] EquipReplace = 2205,
    /// <summary>
    /// 装备生产
    /// </summary>
    [pbr::OriginalName("EquipProduce")] EquipProduce = 2206,
    /// <summary>
    /// 装备分解
    /// </summary>
    [pbr::OriginalName("EquipResolve")] EquipResolve = 2207,
    /// <summary>
    /// 装备材料合成
    /// </summary>
    [pbr::OriginalName("EquipMaterialSynthesis")] EquipMaterialSynthesis = 2208,
    /// <summary>
    /// 装备材料分解
    /// </summary>
    [pbr::OriginalName("EquipMaterialResolve")] EquipMaterialResolve = 2209,
    /// <summary>
    /// 2300 ~ 2599 建筑
    /// </summary>
    [pbr::OriginalName("QueueAccelerate")] QueueAccelerate = 2301,
    /// <summary>
    /// 建筑创建
    /// </summary>
    [pbr::OriginalName("BuildCreate")] BuildCreate = 2302,
    /// <summary>
    /// 建筑升级
    /// </summary>
    [pbr::OriginalName("BuildUpgrade")] BuildUpgrade = 2303,
    /// <summary>
    /// 2600 ~ 2699 幸存者
    /// </summary>
    [pbr::OriginalName("SurvivorSummon")] SurvivorSummon = 2601,
    /// <summary>
    /// 幸存者升星
    /// </summary>
    [pbr::OriginalName("SurvivorStarUp")] SurvivorStarUp = 2602,
    /// <summary>
    /// 2700 ~ 2799 士兵
    /// </summary>
    [pbr::OriginalName("SoldierTraining")] SoldierTraining = 2701,
    /// <summary>
    /// 士兵晋升
    /// </summary>
    [pbr::OriginalName("SoldierPromotion")] SoldierPromotion = 2702,
    /// <summary>
    /// 士兵治疗
    /// </summary>
    [pbr::OriginalName("SoldierTreatment")] SoldierTreatment = 2703,
    /// <summary>
    /// 2800 ~ 2899 工人
    /// </summary>
    [pbr::OriginalName("WorkerRenting")] WorkerRenting = 2801,
    /// <summary>
    /// 2900 ~ 2999 个人科技
    /// </summary>
    [pbr::OriginalName("TechStudy")] TechStudy = 2901,
    /// <summary>
    /// 3000 ~ 3099 无人机
    /// </summary>
    [pbr::OriginalName("UavUpgrade")] UavUpgrade = 3001,
    /// <summary>
    ///无人机一键合成
    /// </summary>
    [pbr::OriginalName("UavOneClickSynthesis")] UavOneClickSynthesis = 3002,
    /// <summary>
    ///无人机研究
    /// </summary>
    [pbr::OriginalName("UavResearch")] UavResearch = 3003,
    /// <summary>
    /// 3100 ~ 3199 商店
    /// </summary>
    [pbr::OriginalName("StoreBuyGoods")] StoreBuyGoods = 3101,
    /// <summary>
    /// 3200 ~ 3299 商城
    /// </summary>
    [pbr::OriginalName("ShopDailyDealClaimFree")] ShopDailyDealClaimFree = 3201,
    /// <summary>
    /// 商城每日必买礼包领取
    /// </summary>
    [pbr::OriginalName("ShopDailyMustHaveClaim")] ShopDailyMustHaveClaim = 3202,
    /// <summary>
    ///  商城超级月卡领取今日奖励
    /// </summary>
    [pbr::OriginalName("ShopMonthCardClaim")] ShopMonthCardClaim = 3203,
    /// <summary>
    /// 商城周卡免费礼包领取
    /// </summary>
    [pbr::OriginalName("ShopWeekCardClaimFree")] ShopWeekCardClaimFree = 3204,
    /// <summary>
    /// 商城周卡领取今日奖励
    /// </summary>
    [pbr::OriginalName("ShopWeekCardClaim")] ShopWeekCardClaim = 3205,
    /// <summary>
    /// 商城黎明基金领取奖励
    /// </summary>
    [pbr::OriginalName("ShopDawnFundClaim")] ShopDawnFundClaim = 3206,
    /// <summary>
    /// 3400 ~ 3499 vip
    /// </summary>
    [pbr::OriginalName("VipReceiveDailyPoint")] VipReceiveDailyPoint = 3401,
    /// <summary>
    /// 领取每日免费vip礼包
    /// </summary>
    [pbr::OriginalName("VipReceiveDailyGift")] VipReceiveDailyGift = 3402,
    /// <summary>
    /// 3500 ~ 3599 城际贸易
    /// </summary>
    [pbr::OriginalName("TradeGoodsRewards")] TradeGoodsRewards = 3501,
    /// <summary>
    /// 贸易掠夺奖励
    /// </summary>
    [pbr::OriginalName("TradeRobRewards")] TradeRobRewards = 3502,
    /// <summary>
    /// 贸易刷新货车
    /// </summary>
    [pbr::OriginalName("TradeRefreshVan")] TradeRefreshVan = 3503,
    /// <summary>
    /// 捐赠合约
    /// </summary>
    [pbr::OriginalName("TradeDonateContract")] TradeDonateContract = 3504,
    /// <summary>
    /// 领取捐赠奖励
    /// </summary>
    [pbr::OriginalName("TradeReceiveDonateReward")] TradeReceiveDonateReward = 3505,
    /// <summary>
    /// 贸易刷新火车
    /// </summary>
    [pbr::OriginalName("TradeRefreshTrain")] TradeRefreshTrain = 3506,
    /// <summary>
    /// 贸易火车掠夺
    /// </summary>
    [pbr::OriginalName("TradeTrainRob")] TradeTrainRob = 3507,
    /// <summary>
    /// 贸易召唤火车
    /// </summary>
    [pbr::OriginalName("TradeSummonTrain")] TradeSummonTrain = 3508,
    /// <summary>
    /// 4000 ~ 4999 联盟
    /// </summary>
    [pbr::OriginalName("UnionCreateCost")] UnionCreateCost = 4001,
    /// <summary>
    /// 联盟领取礼物
    /// </summary>
    [pbr::OriginalName("UnionGiftReward")] UnionGiftReward = 4002,
    /// <summary>
    /// 联盟领取里程碑奖励
    /// </summary>
    [pbr::OriginalName("UnionMilestoneReward")] UnionMilestoneReward = 4003,
    /// <summary>
    /// 联盟踢出补发里程碑奖励邮件
    /// </summary>
    [pbr::OriginalName("UnionMilestoneKickOutRewardMail")] UnionMilestoneKickOutRewardMail = 4004,
    /// <summary>
    /// 联盟里程碑结束补发奖励邮件
    /// </summary>
    [pbr::OriginalName("UnionMilestoneFinishRewardMail")] UnionMilestoneFinishRewardMail = 4005,
    /// <summary>
    /// 联盟里程碑弹劾补发奖励邮件
    /// </summary>
    [pbr::OriginalName("UnionMilestoneImpeachRewardMail")] UnionMilestoneImpeachRewardMail = 4006,
    /// <summary>
    /// 联盟科技捐献奖励
    /// </summary>
    [pbr::OriginalName("UnionTechDonateRewards")] UnionTechDonateRewards = 4007,
    /// <summary>
    /// 5000 ~ 5199 竞技场
    /// </summary>
    [pbr::OriginalName("NoviceRefresh")] NoviceRefresh = 5001,
    /// <summary>
    /// 训练营购买挑战
    /// </summary>
    [pbr::OriginalName("NoviceBuyChallenge")] NoviceBuyChallenge = 5002,
    /// <summary>
    /// 训练营领取成就奖励
    /// </summary>
    [pbr::OriginalName("NoviceAchieveDraw")] NoviceAchieveDraw = 5003,
    /// <summary>
    /// 5300 ~ 5349 关卡
    /// </summary>
    [pbr::OriginalName("DungeonSettleReward")] DungeonSettleReward = 5301,
    /// <summary>
    /// 关卡宝箱奖励
    /// </summary>
    [pbr::OriginalName("DungeonBoxReward")] DungeonBoxReward = 5302,
    /// <summary>
    /// 5350 ~ 5399 任务
    /// </summary>
    [pbr::OriginalName("TaskReceive")] TaskReceive = 5350,
    /// <summary>
    /// 任务积分奖励领取
    /// </summary>
    [pbr::OriginalName("TaskScoreRewardReceive")] TaskScoreRewardReceive = 5351,
    /// <summary>
    /// 任务重置
    /// </summary>
    [pbr::OriginalName("TaskReset")] TaskReset = 5352,
    /// <summary>
    /// 6000 ~ 6999 充值
    /// </summary>
    [pbr::OriginalName("Purchase")] Purchase = 6001,
    /// <summary>
    /// 充值:每日特惠
    /// </summary>
    [pbr::OriginalName("PurchaseShopDailyDeal")] PurchaseShopDailyDeal = 6002,
    /// <summary>
    /// 充值:每日必买
    /// </summary>
    [pbr::OriginalName("PurchaseShopDailyMustHave")] PurchaseShopDailyMustHave = 6003,
    /// <summary>
    /// 充值:每周特惠
    /// </summary>
    [pbr::OriginalName("PurchaseShopWeeklyDeal")] PurchaseShopWeeklyDeal = 6004,
    /// <summary>
    /// 充值:超级月卡
    /// </summary>
    [pbr::OriginalName("PurchaseShopMonthCard")] PurchaseShopMonthCard = 6005,
    /// <summary>
    /// 充值:周卡
    /// </summary>
    [pbr::OriginalName("PurchaseShopWeeklyCard")] PurchaseShopWeeklyCard = 6006,
    /// <summary>
    /// 充值:礼包商城
    /// </summary>
    [pbr::OriginalName("PurchaseShopGiftPackMall")] PurchaseShopGiftPackMall = 6007,
    /// <summary>
    /// 充值:黎明基金
    /// </summary>
    [pbr::OriginalName("PurchaseShopDawnFund")] PurchaseShopDawnFund = 6008,
    /// <summary>
    /// 充值:钻石
    /// </summary>
    [pbr::OriginalName("PurchaseShopDiamondShop")] PurchaseShopDiamondShop = 6009,
    /// <summary>
    /// 充值:周卡合买
    /// </summary>
    [pbr::OriginalName("PurchaseShopWeeklyCardAll")] PurchaseShopWeeklyCardAll = 6010,
    /// <summary>
    /// 充值:额外的建造队列
    /// </summary>
    [pbr::OriginalName("PurchaseBuildQueue")] PurchaseBuildQueue = 6011,
    /// <summary>
    /// 充值:额外的科技队列
    /// </summary>
    [pbr::OriginalName("PurchaseTechnologyQueue")] PurchaseTechnologyQueue = 6012,
    /// <summary>
    /// 充值:VIP礼包
    /// </summary>
    [pbr::OriginalName("PurchaseVIPGift")] PurchaseVipgift = 6013,
  }

  #endregion

}

#endregion Designer generated code
