using System.Collections;
using System.Collections.Generic;
using Game.Hotfix.Config;
using UnityEngine;
using UnityEngine.UI;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    public partial class UITechQueueForm : UGuiFormEx
    {
        private List<int> m_ShowList;
        protected override void OnInit(object userData)
        {
            base.OnInit(userData);

            InitBind();
        }

        protected override void OnOpen(object userData)
        {
            base.OnOpen(userData);
            m_ShowList = GetQueueList();
            ResetUI();
        }

        protected override void OnClose(bool isShutdown, object userData)
        {
            base.OnClose(isShutdown, userData);
        }

        public override void OnRefresh(object userData)
        {
            base.OnRefresh(userData);
            ResetUI();
        }

        private List<int> GetQueueList()
        {
            List<int> techBuildingList = new List<int>{2201,2202,2203};
            List<int> showBuildings = new List<int>();
            for (int i = 0; i < techBuildingList.Count; i++)
            {
                int buildingId = techBuildingList[i];
                bool canShow = GameEntry.LogicData.BuildingData.CheckBuildingCanShow(buildingId);
                if (canShow)
                {
                    showBuildings.Add(buildingId);
                }
            }

            return showBuildings;
        }

        private void ResetUI()
        {
            if (m_ShowList.Count > 0)
            {
                for (int i = 0; i < m_ShowList.Count; i++)
                {
                    GameObject itemObj;
                    if (m_goQueueList.transform.childCount <= i)
                    {
                        itemObj = Instantiate(m_goQueueItem, m_goQueueList.transform);
                        itemObj.SetActive(true);
                    }
                    else
                    {
                        itemObj = m_goQueueList.transform.GetChild(i).gameObject;
                    }

                    UpdateTechQueueList(i,itemObj);
                }
            }
        }

        private void UpdateTechQueueList(int index, GameObject obj)
        {
            Transform itemTransform    = obj.transform;
            UIImage queueIcon          = itemTransform.Find("queueIcon").GetComponent<UIImage>();
            Transform verRoot          = itemTransform.Find("verRoot");
            UIText txtQueueDesc        = itemTransform.Find("verRoot/txtQueueDesc").GetComponent<UIText>();
            UIText txtItemDesc         = obj.transform.Find("verRoot/txtItemDesc").GetComponent<UIText>();
            Slider sliderQueue         = obj.transform.Find("sliderQueueBg/sliderQueue").GetComponent<Slider>();
            UIImage sliderQueueBg      = obj.transform.Find("sliderQueueBg").GetComponent<UIImage>();
            UIImage unLock             = obj.transform.Find("unLock").GetComponent<UIImage>();
            UIText txtLockDesc         = obj.transform.Find("unLock/txtLockDesc").GetComponent<UIText>();
            
            UIButton btnBuy            = obj.transform.Find("btnBuy").GetComponent<UIButton>();
            UIText txtBuy              = obj.transform.Find("btnBuy/txtBuy").GetComponent<UIText>();
            UIButton btnDone           = obj.transform.Find("btnDone").GetComponent<UIButton>();
            UIButton btnTech           = obj.transform.Find("btnTech").GetComponent<UIButton>();
            UIButton btnSpeedUp        = obj.transform.Find("btnSpeedUp").GetComponent<UIButton>();
            UIButton btnUnLock         = obj.transform.Find("btnUnLock").GetComponent<UIButton>();

            int buildingId = m_ShowList[index];
            BuildingModule buildingModule = GameEntry.LogicData.BuildingData.GetBuildingModuleById((uint)buildingId);
            btnBuy.gameObject.SetActive(false);
            btnDone.gameObject.SetActive(false);
            btnTech.gameObject.SetActive(false);
            btnSpeedUp.gameObject.SetActive(false);
            unLock.gameObject.SetActive(false);
            btnUnLock.gameObject.SetActive(false);
            verRoot.gameObject.SetActive(false);
            unLock.gameObject.SetActive(false);
            bool isUnlock = false;
            if (index == 0)
            {
                isUnlock = true;
            }
            else if (index == 1)
            {
                isUnlock = GameEntry.LogicData.TechData.IsBuyLine2();
            }
            else
            {
                isUnlock = GameEntry.LogicData.TechData.IsBuyLine3();
            }

            if (isUnlock)
            {
                verRoot.gameObject.SetActive(true);
                TechQueue techQueueModule = GameEntry.LogicData.QueueData.GetTechQueueModule((uint)buildingId)as TechQueue;
                bool isWork = techQueueModule != null;
                if (isWork)
                {
                    int curLevel = GameEntry.LogicData.TechData.GetCompeleteTechLevelByGroup((int)techQueueModule.curTechGroup);
                    var config = GameEntry.LogicData.TechData.GetTechConfigByGroupLevel((int)techQueueModule.curTechGroup, curLevel);
                    if (config != null && !string.IsNullOrEmpty(config.tech_icon))
                    {
                        queueIcon.SetImage(config.tech_icon);
                        string techName = ToolScriptExtend.GetLang(config.tech_title);
                        txtQueueDesc.text = ToolScriptExtend.GetLangFormat(1100135, curLevel.ToString(),techName);
                        
                        sliderQueueBg.gameObject.SetActive(true);
                        txtItemDesc.gameObject.SetActive(true);
                        float remainTime = techQueueModule.GetRemainTime();
                        var totalTime = techQueueModule.GetTotalTime();
                        btnSpeedUp.gameObject.SetActive(remainTime > 0);
                        string timerKey = $"techQueue{index}";
                        Timers.Instance.Add(timerKey, 1f, (param) =>
                        {
                            if (remainTime > 0)
                            {
                                txtItemDesc.text = ToolScriptExtend.GetLang(1100136) + TimeHelper.FormatGameTimeWithDays(Mathf.FloorToInt(remainTime));
                                sliderQueue.value = 1 - (remainTime / totalTime);
                            }
                            else
                            {
                                txtQueueDesc.text = ToolScriptExtend.GetLangFormat(1345,techName);
                                txtItemDesc.gameObject.SetActive(false);
                                sliderQueue.value = 1;
                                btnSpeedUp.gameObject.SetActive(false);
                                btnDone.gameObject.SetActive(true);
                            }
                            if (remainTime <= 0)
                            {
                                Timers.Instance.Remove(timerKey);
                            }
                        }, -1);
                        
                        btnSpeedUp.onClick.RemoveAllListeners();
                        btnSpeedUp.onClick.AddListener(() =>
                        {
                            itemsubtype itemSubType = itemsubtype.itemsubtype_researchspeedup;
                            GameEntry.UI.OpenUIForm(EnumUIForm.UIBuildingSpeedUpForm,new OpenSpeedUpParam(buildingModule,itemSubType,itemid.itemid_nil, (int)techQueueModule.BindBuildNo));
                        });
                        
                        btnDone.onClick.RemoveAllListeners();
                        btnDone.onClick.AddListener(() =>
                        {
                            GameEntry.LogicData.TechData.CompeleteTech(techQueueModule);
                        });
                    }
                }
                else
                {
                    // 空闲
                    txtQueueDesc.text = ToolScriptExtend.GetLang(1100133);
                    sliderQueueBg.gameObject.SetActive(false);
                    txtItemDesc.gameObject.SetActive(false);
                    string imgPath = "Sprite/ui_mainface/home_left1_yanjiulist.png";
                    queueIcon.SetImage(imgPath);
                    btnTech.gameObject.SetActive(true);
                    btnTech.onClick.RemoveAllListeners();
                    btnTech.onClick.AddListener(() =>
                    {
                        GameEntry.UI.OpenUIForm(EnumUIForm.UITechForm,buildingModule);
                        
                        //如果页面打开了 刷新,添加0.1f延迟
                        Timers.Instance.Add("UITechQueueForm", 0.1f, (param) =>
                        {
                            var ui = GameEntry.UI.GetUIForm(EnumUIForm.UITechForm) as UITechForm;
                            if (ui != null)
                            {
                                ui.GoToTuiJian();
                                Close();
                            }
                        }, 1);
                    });
                }
            }
            else
            {
                string imgPath = "Sprite/ui_mainface/home_left1_yanjiulist.png";
                queueIcon.SetImage(imgPath,true);
                btnBuy.gameObject.SetActive(true);
                sliderQueueBg.gameObject.SetActive(false);
                unLock.gameObject.SetActive(true);
                int langId = 1100477;
                paymentid payId = paymentid.paymentid_20201001;
                if (index == 2)
                {
                    langId = 1100478;
                    payId = paymentid.paymentid_20301002;
                }

                txtLockDesc.text = ToolScriptExtend.GetLang(langId);
                if (ToolScriptExtend.GetConfigById<Config.payment>((int)payId, out var data1))
                {
                    txtBuy.text = ToolScriptExtend.GetLang(data1.price_lang_id);
                }

                btnBuy.onClick.RemoveAllListeners();
                btnBuy.onClick.AddListener(() =>
                {
                    GameEntry.UI.OpenUIForm(EnumUIForm.UIBuyTechLineForm);
                    Close();
                });
            }
        }

        private void OnBtnCloseClick()
        {
            Close();
        }
    }
}
