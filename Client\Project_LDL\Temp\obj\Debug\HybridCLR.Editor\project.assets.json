{"version": 3, "targets": {".NETStandard,Version=v2.1": {"HybridCLR.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/HybridCLR.Runtime.dll": {}}, "runtime": {"bin/placeholder/HybridCLR.Runtime.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}}}, "libraries": {"HybridCLR.Runtime/1.0.0": {"type": "project", "path": "HybridCLR.Runtime.csproj", "msbuildProject": "HybridCLR.Runtime.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["HybridCLR.Runtime >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Editor.csproj", "projectName": "HybridCLR.Editor", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\HybridCLR.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}