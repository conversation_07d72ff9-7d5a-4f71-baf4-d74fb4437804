{"version": 3, "targets": {".NETStandard,Version=v2.1": {"AmplifyShaderEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/AmplifyShaderEditor.dll": {}}, "runtime": {"bin/placeholder/AmplifyShaderEditor.dll": {}}}, "Assembly-CSharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"AmplifyShaderEditor": "1.0.0", "Coffee.UIParticle": "1.0.0", "FancyScrollView": "1.0.0", "FancyScrollView.Editor": "1.0.0", "Game": "1.0.0", "Game.Hotfix": "1.0.0", "GameFramework": "1.0.0", "HybridCLR.Editor": "1.0.0", "HybridCLR.Runtime": "1.0.0", "IngameDebugConsole.Editor": "1.0.0", "IngameDebugConsole.Runtime": "1.0.0", "Mosframe": "1.0.0", "PPv2URPConverters": "1.0.0", "SingularityGroup.HotReload.Runtime.Public": "1.0.0", "Sirenix.OdinInspector.Modules.UnityMathematics": "1.0.0", "UIEffect": "1.0.0", "Unity.2D.Sprite.Editor": "1.0.0", "Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "Unity.AI.Navigation.Updater": "1.0.0", "Unity.Burst": "1.0.0", "Unity.Burst.Editor": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Collections.Editor": "1.0.0", "Unity.EditorCoroutines.Editor": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.MemoryProfiler": "1.0.0", "Unity.MemoryProfiler.Editor": "1.0.0", "Unity.MemoryProfiler.Editor.MemoryProfilerModule": "1.0.0", "Unity.Multiplayer.Center.Common": "1.0.0", "Unity.Multiplayer.Center.Editor": "1.0.0", "Unity.PlasticSCM.Editor": "1.0.0", "Unity.Profiling.Core": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Shaders": "1.0.0", "Unity.Rendering.LightTransport.Editor": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "Unity.Rider.Editor": "1.0.0", "Unity.ScriptableBuildPipeline": "1.0.0", "Unity.ScriptableBuildPipeline.Editor": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "Unity.TextMeshPro": "1.0.0", "Unity.TextMeshPro.Editor": "1.0.0", "Unity.Timeline": "1.0.0", "Unity.Timeline.Editor": "1.0.0", "Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.SettingsProvider.Editor": "1.0.0", "Unity.VisualScripting.Shared.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "Unity.VisualStudio.Editor": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGameFramework.Editor": "1.0.0", "UnityGameFramework.Runtime": "1.0.0", "YooAsset": "1.0.0", "YooAsset.Editor": "1.0.0", "spine-csharp": "1.0.0", "spine-unity": "1.0.0", "spine-unity-editor": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp.dll": {}}}, "Coffee.UIParticle/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Coffee.UIParticle.dll": {}}, "runtime": {"bin/placeholder/Coffee.UIParticle.dll": {}}}, "FancyScrollView/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/FancyScrollView.dll": {}}, "runtime": {"bin/placeholder/FancyScrollView.dll": {}}}, "FancyScrollView.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FancyScrollView": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/FancyScrollView.Editor.dll": {}}, "runtime": {"bin/placeholder/FancyScrollView.Editor.dll": {}}}, "Game/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameFramework": "1.0.0", "HybridCLR.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGameFramework.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Game.dll": {}}, "runtime": {"bin/placeholder/Game.dll": {}}}, "Game.Hotfix/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FancyScrollView": "1.0.0", "Game": "1.0.0", "GameFramework": "1.0.0", "IngameDebugConsole.Runtime": "1.0.0", "Mosframe": "1.0.0", "UIEffect": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGameFramework.Runtime": "1.0.0", "spine-csharp": "1.0.0", "spine-unity": "1.0.0"}, "compile": {"bin/placeholder/Game.Hotfix.dll": {}}, "runtime": {"bin/placeholder/Game.Hotfix.dll": {}}}, "GameFramework/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/GameFramework.dll": {}}, "runtime": {"bin/placeholder/GameFramework.dll": {}}}, "HybridCLR.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"HybridCLR.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/HybridCLR.Editor.dll": {}}, "runtime": {"bin/placeholder/HybridCLR.Editor.dll": {}}}, "HybridCLR.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/HybridCLR.Runtime.dll": {}}, "runtime": {"bin/placeholder/HybridCLR.Runtime.dll": {}}}, "IngameDebugConsole.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"IngameDebugConsole.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/IngameDebugConsole.Editor.dll": {}}, "runtime": {"bin/placeholder/IngameDebugConsole.Editor.dll": {}}}, "IngameDebugConsole.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/IngameDebugConsole.Runtime.dll": {}}, "runtime": {"bin/placeholder/IngameDebugConsole.Runtime.dll": {}}}, "Mosframe/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Mosframe.dll": {}}, "runtime": {"bin/placeholder/Mosframe.dll": {}}}, "PPv2URPConverters/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Editor": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/PPv2URPConverters.dll": {}}, "runtime": {"bin/placeholder/PPv2URPConverters.dll": {}}}, "SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}, "runtime": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}}, "Sirenix.OdinInspector.Modules.UnityMathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Sirenix.OdinInspector.Modules.UnityMathematics.dll": {}}, "runtime": {"bin/placeholder/Sirenix.OdinInspector.Modules.UnityMathematics.dll": {}}}, "spine-csharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/spine-csharp.dll": {}}, "runtime": {"bin/placeholder/spine-csharp.dll": {}}}, "spine-unity/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0", "spine-csharp": "1.0.0"}, "compile": {"bin/placeholder/spine-unity.dll": {}}, "runtime": {"bin/placeholder/spine-unity.dll": {}}}, "spine-unity-editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "spine-csharp": "1.0.0", "spine-unity": "1.0.0"}, "compile": {"bin/placeholder/spine-unity-editor.dll": {}}, "runtime": {"bin/placeholder/spine-unity-editor.dll": {}}}, "UIEffect/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UIEffect.dll": {}}, "runtime": {"bin/placeholder/UIEffect.dll": {}}}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.2D.Sprite.Editor.dll": {}}}, "Unity.AI.Navigation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.dll": {}}}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.dll": {}}}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Editor.ConversionSystem.dll": {}}}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.AI.Navigation": "1.0.0", "Unity.AI.Navigation.Editor": "1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}, "runtime": {"bin/placeholder/Unity.AI.Navigation.Updater.dll": {}}}, "Unity.Burst/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.dll": {}}}, "Unity.Burst.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Burst.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Burst.Editor.dll": {}}}, "Unity.Collections/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.dll": {}}}, "Unity.Collections.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Collections": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Collections.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Collections.Editor.dll": {}}}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.EditorCoroutines.Editor.dll": {}}}, "Unity.Mathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.dll": {}}}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Mathematics": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Mathematics.Editor.dll": {}}}, "Unity.MemoryProfiler/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.MemoryProfiler.dll": {}}, "runtime": {"bin/placeholder/Unity.MemoryProfiler.dll": {}}}, "Unity.MemoryProfiler.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.EditorCoroutines.Editor": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.MemoryProfiler": "1.0.0", "Unity.Profiling.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.MemoryProfiler.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.MemoryProfiler.Editor.dll": {}}}, "Unity.MemoryProfiler.Editor.MemoryProfilerModule/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.MemoryProfiler.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll": {}}, "runtime": {"bin/placeholder/Unity.MemoryProfiler.Editor.MemoryProfilerModule.dll": {}}}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Common.dll": {}}}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Multiplayer.Center.Common": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Multiplayer.Center.Editor.dll": {}}}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.PlasticSCM.Editor.dll": {}}}, "Unity.Profiling.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Profiling.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.Profiling.Core.dll": {}}}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Editor.dll": {}}}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.Rendering.LightTransport.Runtime.dll": {}}}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipeline.Universal.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.Rendering.LightTransport.Runtime": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.dll": {}}}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Editor.Shared.dll": {}}}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.dll": {}}}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.Runtime.Shared.dll": {}}}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Core.ShaderLibrary.dll": {}}}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Collections": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.GPUDriven.Runtime.dll": {}}}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll": {}}}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.2D.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Config.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst.Editor": "1.0.0", "Unity.Mathematics.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Editor.Shared": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Runtime": "1.0.0", "Unity.ShaderGraph.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Editor.dll": {}}}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Burst": "1.0.0", "Unity.Mathematics": "1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared": "1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime": "1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Runtime.dll": {}}}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}, "runtime": {"bin/placeholder/Unity.RenderPipelines.Universal.Shaders.dll": {}}}, "Unity.Rider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Rider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Rider.Editor.dll": {}}}, "Unity.ScriptableBuildPipeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ScriptableBuildPipeline.dll": {}}, "runtime": {"bin/placeholder/Unity.ScriptableBuildPipeline.dll": {}}}, "Unity.ScriptableBuildPipeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.ScriptableBuildPipeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ScriptableBuildPipeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ScriptableBuildPipeline.Editor.dll": {}}}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Searcher.Editor.dll": {}}}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Editor": "1.0.0", "Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.Searcher.Editor": "1.0.0", "Unity.ShaderGraph.Utilities": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Editor.dll": {}}}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}, "runtime": {"bin/placeholder/Unity.ShaderGraph.Utilities.dll": {}}}, "Unity.TextMeshPro/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.dll": {}}}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.RenderPipelines.Core.Runtime": "1.0.0", "Unity.TextMeshPro": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.TextMeshPro.Editor.dll": {}}}, "Unity.Timeline/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.dll": {}}}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.Timeline": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.Timeline.Editor.dll": {}}}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.dll": {}}}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Core.Editor.dll": {}}}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.dll": {}}}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Flow.Editor.dll": {}}}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.SettingsProvider.Editor.dll": {}}}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "Unity.VisualScripting.State.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.Shared.Editor.dll": {}}}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.dll": {}}}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.VisualScripting.Core": "1.0.0", "Unity.VisualScripting.Core.Editor": "1.0.0", "Unity.VisualScripting.Flow": "1.0.0", "Unity.VisualScripting.Flow.Editor": "1.0.0", "Unity.VisualScripting.State": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualScripting.State.Editor.dll": {}}}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}, "runtime": {"bin/placeholder/Unity.VisualStudio.Editor.dll": {}}}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEngine.TestRunner": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.TestRunner.dll": {}}}, "UnityEditor.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.TestRunner": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityEditor.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEditor.UI.dll": {}}}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.TestRunner.dll": {}}}, "UnityEngine.UI/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.UI.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.UI.dll": {}}}, "UnityGameFramework.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameFramework": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "UnityGameFramework.Runtime": "1.0.0"}, "compile": {"bin/placeholder/UnityGameFramework.Editor.dll": {}}, "runtime": {"bin/placeholder/UnityGameFramework.Editor.dll": {}}}, "UnityGameFramework.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameFramework": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/UnityGameFramework.Runtime.dll": {}}, "runtime": {"bin/placeholder/UnityGameFramework.Runtime.dll": {}}}, "YooAsset/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UnityEditor.UI": "1.0.0", "UnityEngine.UI": "1.0.0"}, "compile": {"bin/placeholder/YooAsset.dll": {}}, "runtime": {"bin/placeholder/YooAsset.dll": {}}}, "YooAsset.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Unity.ScriptableBuildPipeline": "1.0.0", "Unity.ScriptableBuildPipeline.Editor": "1.0.0", "UnityEditor.TestRunner": "1.0.0", "UnityEditor.UI": "1.0.0", "UnityEngine.TestRunner": "1.0.0", "UnityEngine.UI": "1.0.0", "YooAsset": "1.0.0"}, "compile": {"bin/placeholder/YooAsset.Editor.dll": {}}, "runtime": {"bin/placeholder/YooAsset.Editor.dll": {}}}}}, "libraries": {"AmplifyShaderEditor/1.0.0": {"type": "project", "path": "AmplifyShaderEditor.csproj", "msbuildProject": "AmplifyShaderEditor.csproj"}, "Assembly-CSharp/1.0.0": {"type": "project", "path": "Assembly-CSharp.csproj", "msbuildProject": "Assembly-CSharp.csproj"}, "Coffee.UIParticle/1.0.0": {"type": "project", "path": "Coffee.UIParticle.csproj", "msbuildProject": "Coffee.UIParticle.csproj"}, "FancyScrollView/1.0.0": {"type": "project", "path": "FancyScrollView.csproj", "msbuildProject": "FancyScrollView.csproj"}, "FancyScrollView.Editor/1.0.0": {"type": "project", "path": "FancyScrollView.Editor.csproj", "msbuildProject": "FancyScrollView.Editor.csproj"}, "Game/1.0.0": {"type": "project", "path": "Game.csproj", "msbuildProject": "Game.csproj"}, "Game.Hotfix/1.0.0": {"type": "project", "path": "Game.Hotfix.csproj", "msbuildProject": "Game.Hotfix.csproj"}, "GameFramework/1.0.0": {"type": "project", "path": "GameFramework.csproj", "msbuildProject": "GameFramework.csproj"}, "HybridCLR.Editor/1.0.0": {"type": "project", "path": "HybridCLR.Editor.csproj", "msbuildProject": "HybridCLR.Editor.csproj"}, "HybridCLR.Runtime/1.0.0": {"type": "project", "path": "HybridCLR.Runtime.csproj", "msbuildProject": "HybridCLR.Runtime.csproj"}, "IngameDebugConsole.Editor/1.0.0": {"type": "project", "path": "IngameDebugConsole.Editor.csproj", "msbuildProject": "IngameDebugConsole.Editor.csproj"}, "IngameDebugConsole.Runtime/1.0.0": {"type": "project", "path": "IngameDebugConsole.Runtime.csproj", "msbuildProject": "IngameDebugConsole.Runtime.csproj"}, "Mosframe/1.0.0": {"type": "project", "path": "Mosframe.csproj", "msbuildProject": "Mosframe.csproj"}, "PPv2URPConverters/1.0.0": {"type": "project", "path": "PPv2URPConverters.csproj", "msbuildProject": "PPv2URPConverters.csproj"}, "SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "path": "SingularityGroup.HotReload.Runtime.Public.csproj", "msbuildProject": "SingularityGroup.HotReload.Runtime.Public.csproj"}, "Sirenix.OdinInspector.Modules.UnityMathematics/1.0.0": {"type": "project", "path": "Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "msbuildProject": "Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "spine-csharp/1.0.0": {"type": "project", "path": "spine-csharp.csproj", "msbuildProject": "spine-csharp.csproj"}, "spine-unity/1.0.0": {"type": "project", "path": "spine-unity.csproj", "msbuildProject": "spine-unity.csproj"}, "spine-unity-editor/1.0.0": {"type": "project", "path": "spine-unity-editor.c<PERSON><PERSON>j", "msbuildProject": "spine-unity-editor.c<PERSON><PERSON>j"}, "UIEffect/1.0.0": {"type": "project", "path": "UIEffect.csproj", "msbuildProject": "UIEffect.csproj"}, "Unity.2D.Sprite.Editor/1.0.0": {"type": "project", "path": "Unity.2D.Sprite.Editor.csproj", "msbuildProject": "Unity.2D.Sprite.Editor.csproj"}, "Unity.AI.Navigation/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.csproj", "msbuildProject": "Unity.AI.Navigation.csproj"}, "Unity.AI.Navigation.Editor/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.csproj"}, "Unity.AI.Navigation.Editor.ConversionSystem/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Editor.ConversionSystem.csproj", "msbuildProject": "Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "Unity.AI.Navigation.Updater/1.0.0": {"type": "project", "path": "Unity.AI.Navigation.Updater.csproj", "msbuildProject": "Unity.AI.Navigation.Updater.csproj"}, "Unity.Burst/1.0.0": {"type": "project", "path": "Unity.Burst.csproj", "msbuildProject": "Unity.Burst.csproj"}, "Unity.Burst.Editor/1.0.0": {"type": "project", "path": "Unity.Burst.Editor.csproj", "msbuildProject": "Unity.Burst.Editor.csproj"}, "Unity.Collections/1.0.0": {"type": "project", "path": "Unity.Collections.csproj", "msbuildProject": "Unity.Collections.csproj"}, "Unity.Collections.Editor/1.0.0": {"type": "project", "path": "Unity.Collections.Editor.csproj", "msbuildProject": "Unity.Collections.Editor.csproj"}, "Unity.EditorCoroutines.Editor/1.0.0": {"type": "project", "path": "Unity.EditorCoroutines.Editor.csproj", "msbuildProject": "Unity.EditorCoroutines.Editor.csproj"}, "Unity.Mathematics/1.0.0": {"type": "project", "path": "Unity.Mathematics.csproj", "msbuildProject": "Unity.Mathematics.csproj"}, "Unity.Mathematics.Editor/1.0.0": {"type": "project", "path": "Unity.Mathematics.Editor.csproj", "msbuildProject": "Unity.Mathematics.Editor.csproj"}, "Unity.MemoryProfiler/1.0.0": {"type": "project", "path": "Unity.MemoryProfiler.csproj", "msbuildProject": "Unity.MemoryProfiler.csproj"}, "Unity.MemoryProfiler.Editor/1.0.0": {"type": "project", "path": "Unity.MemoryProfiler.Editor.csproj", "msbuildProject": "Unity.MemoryProfiler.Editor.csproj"}, "Unity.MemoryProfiler.Editor.MemoryProfilerModule/1.0.0": {"type": "project", "path": "Unity.MemoryProfiler.Editor.MemoryProfilerModule.csproj", "msbuildProject": "Unity.MemoryProfiler.Editor.MemoryProfilerModule.csproj"}, "Unity.Multiplayer.Center.Common/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Common.csproj", "msbuildProject": "Unity.Multiplayer.Center.Common.csproj"}, "Unity.Multiplayer.Center.Editor/1.0.0": {"type": "project", "path": "Unity.Multiplayer.Center.Editor.csproj", "msbuildProject": "Unity.Multiplayer.Center.Editor.csproj"}, "Unity.PlasticSCM.Editor/1.0.0": {"type": "project", "path": "Unity.PlasticSCM.Editor.csproj", "msbuildProject": "Unity.PlasticSCM.Editor.csproj"}, "Unity.Profiling.Core/1.0.0": {"type": "project", "path": "Unity.Profiling.Core.csproj", "msbuildProject": "Unity.Profiling.Core.csproj"}, "Unity.Rendering.LightTransport.Editor/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Editor.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Editor.csproj"}, "Unity.Rendering.LightTransport.Runtime/1.0.0": {"type": "project", "path": "Unity.Rendering.LightTransport.Runtime.csproj", "msbuildProject": "Unity.Rendering.LightTransport.Runtime.csproj"}, "Unity.RenderPipeline.Universal.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "Unity.RenderPipelines.Core.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.csproj"}, "Unity.RenderPipelines.Core.Editor.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Editor.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "Unity.RenderPipelines.Core.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.csproj"}, "Unity.RenderPipelines.Core.Runtime.Shared/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.Runtime.Shared.csproj", "msbuildProject": "Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "Unity.RenderPipelines.Core.ShaderLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Core.ShaderLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "Unity.RenderPipelines.GPUDriven.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.GPUDriven.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj", "msbuildProject": "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "Unity.RenderPipelines.Universal.2D.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.2D.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Config.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Config.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Editor/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Editor.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Editor.csproj"}, "Unity.RenderPipelines.Universal.Runtime/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Runtime.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Runtime.csproj"}, "Unity.RenderPipelines.Universal.Shaders/1.0.0": {"type": "project", "path": "Unity.RenderPipelines.Universal.Shaders.csproj", "msbuildProject": "Unity.RenderPipelines.Universal.Shaders.csproj"}, "Unity.Rider.Editor/1.0.0": {"type": "project", "path": "Unity.Rider.Editor.csproj", "msbuildProject": "Unity.Rider.Editor.csproj"}, "Unity.ScriptableBuildPipeline/1.0.0": {"type": "project", "path": "Unity.ScriptableBuildPipeline.csproj", "msbuildProject": "Unity.ScriptableBuildPipeline.csproj"}, "Unity.ScriptableBuildPipeline.Editor/1.0.0": {"type": "project", "path": "Unity.ScriptableBuildPipeline.Editor.csproj", "msbuildProject": "Unity.ScriptableBuildPipeline.Editor.csproj"}, "Unity.Searcher.Editor/1.0.0": {"type": "project", "path": "Unity.Searcher.Editor.csproj", "msbuildProject": "Unity.Searcher.Editor.csproj"}, "Unity.ShaderGraph.Editor/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Editor.csproj", "msbuildProject": "Unity.ShaderGraph.Editor.csproj"}, "Unity.ShaderGraph.Utilities/1.0.0": {"type": "project", "path": "Unity.ShaderGraph.Utilities.csproj", "msbuildProject": "Unity.ShaderGraph.Utilities.csproj"}, "Unity.TextMeshPro/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.csproj", "msbuildProject": "Unity.TextMeshPro.csproj"}, "Unity.TextMeshPro.Editor/1.0.0": {"type": "project", "path": "Unity.TextMeshPro.Editor.csproj", "msbuildProject": "Unity.TextMeshPro.Editor.csproj"}, "Unity.Timeline/1.0.0": {"type": "project", "path": "Unity.Timeline.csproj", "msbuildProject": "Unity.Timeline.csproj"}, "Unity.Timeline.Editor/1.0.0": {"type": "project", "path": "Unity.Timeline.Editor.csproj", "msbuildProject": "Unity.Timeline.Editor.csproj"}, "Unity.VisualScripting.Core/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.csproj", "msbuildProject": "Unity.VisualScripting.Core.csproj"}, "Unity.VisualScripting.Core.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Core.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Core.Editor.csproj"}, "Unity.VisualScripting.Flow/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.csproj", "msbuildProject": "Unity.VisualScripting.Flow.csproj"}, "Unity.VisualScripting.Flow.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Flow.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Flow.Editor.csproj"}, "Unity.VisualScripting.SettingsProvider.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.SettingsProvider.Editor.csproj", "msbuildProject": "Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "Unity.VisualScripting.Shared.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.Shared.Editor.csproj", "msbuildProject": "Unity.VisualScripting.Shared.Editor.csproj"}, "Unity.VisualScripting.State/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.csproj", "msbuildProject": "Unity.VisualScripting.State.csproj"}, "Unity.VisualScripting.State.Editor/1.0.0": {"type": "project", "path": "Unity.VisualScripting.State.Editor.csproj", "msbuildProject": "Unity.VisualScripting.State.Editor.csproj"}, "Unity.VisualStudio.Editor/1.0.0": {"type": "project", "path": "Unity.VisualStudio.Editor.csproj", "msbuildProject": "Unity.VisualStudio.Editor.csproj"}, "UnityEditor.TestRunner/1.0.0": {"type": "project", "path": "UnityEditor.TestRunner.csproj", "msbuildProject": "UnityEditor.TestRunner.csproj"}, "UnityEditor.UI/1.0.0": {"type": "project", "path": "UnityEditor.UI.csproj", "msbuildProject": "UnityEditor.UI.csproj"}, "UnityEngine.TestRunner/1.0.0": {"type": "project", "path": "UnityEngine.TestRunner.csproj", "msbuildProject": "UnityEngine.TestRunner.csproj"}, "UnityEngine.UI/1.0.0": {"type": "project", "path": "UnityEngine.UI.csproj", "msbuildProject": "UnityEngine.UI.csproj"}, "UnityGameFramework.Editor/1.0.0": {"type": "project", "path": "UnityGameFramework.Editor.csproj", "msbuildProject": "UnityGameFramework.Editor.csproj"}, "UnityGameFramework.Runtime/1.0.0": {"type": "project", "path": "UnityGameFramework.Runtime.csproj", "msbuildProject": "UnityGameFramework.Runtime.csproj"}, "YooAsset/1.0.0": {"type": "project", "path": "YooAsset.csproj", "msbuildProject": "YooAsset.csproj"}, "YooAsset.Editor/1.0.0": {"type": "project", "path": "YooAsset.Editor.csproj", "msbuildProject": "YooAsset.Editor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["AmplifyShaderEditor >= 1.0.0", "Assembly-CSharp >= 1.0.0", "Coffee.UIParticle >= 1.0.0", "FancyScrollView >= 1.0.0", "FancyScrollView.Editor >= 1.0.0", "Game >= 1.0.0", "Game.Hotfix >= 1.0.0", "GameFramework >= 1.0.0", "HybridCLR.Editor >= 1.0.0", "HybridCLR.Runtime >= 1.0.0", "IngameDebugConsole.Editor >= 1.0.0", "IngameDebugConsole.Runtime >= 1.0.0", "Mosframe >= 1.0.0", "PPv2URPConverters >= 1.0.0", "SingularityGroup.HotReload.Runtime.Public >= 1.0.0", "Sirenix.OdinInspector.Modules.UnityMathematics >= 1.0.0", "UIEffect >= 1.0.0", "Unity.2D.Sprite.Editor >= 1.0.0", "Unity.AI.Navigation >= 1.0.0", "Unity.AI.Navigation.Editor >= 1.0.0", "Unity.AI.Navigation.Editor.ConversionSystem >= 1.0.0", "Unity.AI.Navigation.Updater >= 1.0.0", "Unity.Burst >= 1.0.0", "Unity.Burst.Editor >= 1.0.0", "Unity.Collections >= 1.0.0", "Unity.Collections.Editor >= 1.0.0", "Unity.EditorCoroutines.Editor >= 1.0.0", "Unity.Mathematics >= 1.0.0", "Unity.Mathematics.Editor >= 1.0.0", "Unity.MemoryProfiler >= 1.0.0", "Unity.MemoryProfiler.Editor >= 1.0.0", "Unity.MemoryProfiler.Editor.MemoryProfilerModule >= 1.0.0", "Unity.Multiplayer.Center.Common >= 1.0.0", "Unity.Multiplayer.Center.Editor >= 1.0.0", "Unity.PlasticSCM.Editor >= 1.0.0", "Unity.Profiling.Core >= 1.0.0", "Unity.RenderPipeline.Universal.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.Core.Editor >= 1.0.0", "Unity.RenderPipelines.Core.Editor.Shared >= 1.0.0", "Unity.RenderPipelines.Core.Runtime >= 1.0.0", "Unity.RenderPipelines.Core.Runtime.Shared >= 1.0.0", "Unity.RenderPipelines.Core.ShaderLibrary >= 1.0.0", "Unity.RenderPipelines.GPUDriven.Runtime >= 1.0.0", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary >= 1.0.0", "Unity.RenderPipelines.Universal.2D.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Config.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Editor >= 1.0.0", "Unity.RenderPipelines.Universal.Runtime >= 1.0.0", "Unity.RenderPipelines.Universal.Shaders >= 1.0.0", "Unity.Rendering.LightTransport.Editor >= 1.0.0", "Unity.Rendering.LightTransport.Runtime >= 1.0.0", "Unity.Rider.Editor >= 1.0.0", "Unity.ScriptableBuildPipeline >= 1.0.0", "Unity.ScriptableBuildPipeline.Editor >= 1.0.0", "Unity.Searcher.Editor >= 1.0.0", "Unity.ShaderGraph.Editor >= 1.0.0", "Unity.TextMeshPro >= 1.0.0", "Unity.TextMeshPro.Editor >= 1.0.0", "Unity.Timeline >= 1.0.0", "Unity.Timeline.Editor >= 1.0.0", "Unity.VisualScripting.Core >= 1.0.0", "Unity.VisualScripting.Core.Editor >= 1.0.0", "Unity.VisualScripting.Flow >= 1.0.0", "Unity.VisualScripting.Flow.Editor >= 1.0.0", "Unity.VisualScripting.SettingsProvider.Editor >= 1.0.0", "Unity.VisualScripting.Shared.Editor >= 1.0.0", "Unity.VisualScripting.State >= 1.0.0", "Unity.VisualScripting.State.Editor >= 1.0.0", "Unity.VisualStudio.Editor >= 1.0.0", "UnityEditor.TestRunner >= 1.0.0", "UnityEditor.UI >= 1.0.0", "UnityEngine.TestRunner >= 1.0.0", "UnityEngine.UI >= 1.0.0", "UnityGameFramework.Editor >= 1.0.0", "UnityGameFramework.Runtime >= 1.0.0", "YooAsset >= 1.0.0", "YooAsset.Editor >= 1.0.0", "spine-csharp >= 1.0.0", "spine-unity >= 1.0.0", "spine-unity-editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\AmplifyShaderEditor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\AmplifyShaderEditor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Assembly-CSharp.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Assembly-CSharp.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Coffee.UIParticle.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Coffee.UIParticle.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\FancyScrollView.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\FancyScrollView.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\FancyScrollView.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\FancyScrollView.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Game.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Game.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Game.Hotfix.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Game.Hotfix.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\GameFramework.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\GameFramework.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\HybridCLR.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\IngameDebugConsole.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\IngameDebugConsole.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\IngameDebugConsole.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\IngameDebugConsole.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Mosframe.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Mosframe.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\PPv2URPConverters.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\PPv2URPConverters.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\SingularityGroup.HotReload.Runtime.Public.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\SingularityGroup.HotReload.Runtime.Public.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-csharp.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-csharp.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-unity-editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-unity-editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-unity.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\spine-unity.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UIEffect.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UIEffect.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.AI.Navigation.Updater.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Burst.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Burst.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Burst.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Burst.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Collections.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Collections.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Collections.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Collections.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.EditorCoroutines.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.EditorCoroutines.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Mathematics.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Mathematics.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.MemoryProfiler.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.MemoryProfiler.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.MemoryProfiler.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.MemoryProfiler.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.MemoryProfiler.Editor.MemoryProfilerModule.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.MemoryProfiler.Editor.MemoryProfilerModule.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Multiplayer.Center.Common.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Multiplayer.Center.Common.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Multiplayer.Center.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Multiplayer.Center.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Profiling.Core.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Profiling.Core.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Rendering.LightTransport.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Rendering.LightTransport.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Rendering.LightTransport.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Rendering.LightTransport.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipeline.Universal.ShaderLibrary.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Editor.Shared.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Editor.Shared.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Runtime.Shared.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.Runtime.Shared.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.ShaderLibrary.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Core.ShaderLibrary.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.GPUDriven.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.GPUDriven.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.2D.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.2D.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Config.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Config.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Shaders.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.RenderPipelines.Universal.Shaders.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Rider.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.ScriptableBuildPipeline.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.ScriptableBuildPipeline.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.ScriptableBuildPipeline.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.ScriptableBuildPipeline.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Searcher.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Searcher.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.ShaderGraph.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.ShaderGraph.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.TextMeshPro.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Timeline.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Timeline.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.Timeline.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Core.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Core.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Core.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Core.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Flow.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Flow.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Flow.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Flow.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.SettingsProvider.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.SettingsProvider.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Shared.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.Shared.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.State.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.State.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.State.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualScripting.State.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\Unity.VisualStudio.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEditor.UI.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.TestRunner.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityEngine.UI.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityGameFramework.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityGameFramework.Editor.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityGameFramework.Runtime.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\UnityGameFramework.Runtime.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\YooAsset.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\YooAsset.csproj"}, "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\YooAsset.Editor.csproj": {"projectPath": "D:\\Project_LDL_Client\\ldl-client\\Client\\Project_LDL\\YooAsset.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}